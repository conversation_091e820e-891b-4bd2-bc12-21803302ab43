# yingfei-ADMIN

代码接口  
~~~
com.yingfei     
├── yingfei-gateway         // 网关模块 [8080](鉴权,过滤请求)  所有请求都通过网关进行转发
├── yingfei-auth            // 认证中心 [9200]  用户认证
├── yingfei-api             // Feign接口模块
│       └── yingfei-api-system                          // 系统接口
│       └── yingfei-api-dataManage                      // 数据管理接口
│       └── ...                                         // 对应业务模块
├── yingfei-common          // 通用模块
│       └── yingfei-common-core                         // 核心模块
│       └── yingfei-common-datascope                    // 权限范围
│       └── yingfei-common-datasource                   // 多数据源
│       └── yingfei-common-log                          // 日志记录
│       └── yingfei-common-redis                        // 缓存服务
│       └── yingfei-common-seata                        // 分布式事务
│       └── yingfei-common-security                     // 安全模块
│       └── yingfei-common-swagger                      // 系统接口
├── yingfei-domain          // 实体类模块
├── yingfei-modules         // 业务模块
│       └── yingfei-modules-system                      // 系统模块 [9210] 用户相关,角色相关,菜单相关
│       └── yingfei-modules-dataCollection              // 采集模块 [9220] 采集数据,数据处理
│       └── yingfei-modules-dataAnalysis                // 数据分析 [9230]  数据分析
│       └── yingfei-modules-dataManagement              // 数据管理,基础信息,数据查询模块 [9240]
│       └── yingfei-modules-dataMonitor                 // 数据监控模块 [9250]
├──pom.xml                // 公共依赖
~~~