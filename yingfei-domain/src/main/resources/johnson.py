import json
import scipy.stats as si
import math
import sys

if __name__ == '__main__':
    # 获取传递的参数
    param1 = sys.argv[1]
    data = json.loads(param1)
    pValue = None
    for d in data:
        transformedValues = d.get("transformedValues")
        AD, crit, sig = si.anderson(transformedValues, dist='norm')
        AD = AD * (1 + (.75 / 50) + 2.25 / (50 ** 2))
        if AD >= .6:
            p = math.exp(1.2937 - 5.709 * AD - .0186 * (AD ** 2))
        elif AD >= .34:
            p = math.exp(.9177 - 4.279 * AD - 1.38 * (AD ** 2))
        elif AD > .2:
            p = 1 - math.exp(-8.318 + 42.796 * AD - 59.938 * (AD ** 2))
        else:
            p = 1 - math.exp(-13.436 + 101.14 * AD - 223.73 * (AD ** 2))
        if pValue is None:
            pValue = p
        else:
            if p > pValue:
                pValue = p
    print('p=', f'{pValue:.10f}')

