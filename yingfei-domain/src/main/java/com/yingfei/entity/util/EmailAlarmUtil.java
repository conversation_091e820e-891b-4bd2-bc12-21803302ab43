package com.yingfei.entity.util;

import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.entity.dto.SGRP_DSC_DTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.SYSTEM_NOTIFICATION_ALARM_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class EmailAlarmUtil {

    private static final String emailHtml = "<!DOCTYPE html>\n" +
            "<html lang=\"en\">\n" +
            "<head>\n" +
            "    <meta charset=\"UTF-8\">\n" +
            "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
            "    <title>Document</title>\n" +
            "    <style>\n" +
            "        *{\n" +
            "            margin: 0;\n" +
            "            padding: 0;\n" +
            "            box-sizing: border-box;\n" +
            "        }\n" +
            "        .row {\n" +
            "    display: flex;\n" +
            "    justify-content: space-between;\n" +
            "    font-size: 14px;\n" +
            "}\n" +
            "\n" +
            ".td {\n" +
            "    padding: 14px 16px;\n" +
            "    box-sizing: border-box;\n" +
            "    flex: 1;\n" +
            "    text-align: center;\n" +
            "    max-width: 100%;\n" +
            "    /* 防止内容撑大 */\n" +
            "    overflow: hidden;\n" +
            "    /* 隐藏溢出的内容 */\n" +
            "    text-overflow: ellipsis;\n" +
            "    /* 显示省略号 */\n" +
            "    border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n" +
            "    border-right: 1px solid rgba(0, 0, 0, 0.06);\n" +
            "    color: rgba(0,0,0,0.85);\n" +
            "    font-size: 14px;\n" +
            "    word-break: break-word;\n" +
            "}\n" +
            ".td:last-child {\n" +
            "    border-right: none;\n" +
            "}\n" +
            ".headers {\n" +
            "    height: 56px;\n" +
            "background: #FFFFFF;\n" +
            "box-shadow: 0px 2px 12px 1px rgba(0,21,41,0.08);\n" +
            "border-radius: 0px 0px 0px 0px;\n" +
            "display: flex;\n" +
            "align-items: center;\n" +
            "padding: 0 24px;\n" +
            "font-size: 16px;\n" +
            "color: rgba(0,0,0,0.85);\n" +
            "}\n" +
            "    </style>\n" +
            "</head>\n" +
            "<body>\n" +
            "    <div class=\"headers\">发生报警</div>\n" +
            "    <div style=\"padding: 16px;box-sizing: border-box;\">\n" +
            "        <div style=\"border: 1px solid rgba(0,0,0,0.06);border-bottom: none;color: rgba(0,0,0,0.85);\">\n" +
            "            <div style=\"display: flex\">\n" +
            "                <div\n" +
            "                    style=\"width: 50%; display: flex;justify-content: space-between;border-bottom: 1px solid rgba(0,0,0,0.06);font-size: 14px;\">\n" +
            "                    <div\n" +
            "                        style=\"width: 200px;border-right: 1px solid rgba(0,0,0,0.06);padding: 16px;box-sizing: border-box;background: #F7F8FA;\">\n" +
            "                        时间</div>\n" +
            "                    <div\n" +
            "                        style=\"flex: 1;border-right: 1px solid rgba(0,0,0,0.06);padding: 16px;box-sizing: border-box;color: rgba(0,0,0,0.65);\">\n" +
            "                        #{alarmTime}\n" +
            "                    </div>\n" +
            "                </div>\n" +
            "                <div\n" +
            "                    style=\"width: 50%; display: flex;justify-content: space-between;border-bottom: 1px solid rgba(0,0,0,0.06);font-size: 14px;\">\n" +
            "                    <div\n" +
            "                        style=\"width: 200px;border-right: 1px solid rgba(0,0,0,0.06);padding: 16px;box-sizing: border-box;background: #F7F8FA;\">\n" +
            "                        报警规则模板</div>\n" +
            "                    <div style=\"flex: 1;padding: 16px;box-sizing: border-box;color: rgba(0,0,0,0.65);\">#{templateName}</div>\n" +
            "                </div>\n" +
            "            </div>\n" +
            "    \n" +
            "        <div\n" +
            "            style=\"font-size: 16px;color: rgba(0,0,0,0.85);display: flex;align-items: center;margin-top: 16px;margin-bottom: 8px;\">\n" +
            "            <div\n" +
            "                style=\"width: 3px;height: 16px;background: #00AAA6;border-radius: 3px 3px 3px 3px;margin-right: 8px;\">\n" +
            "            </div> 报警详情\n" +
            "        </div>\n" +
            "    \n" +
            "        <div style=\"border: 1px solid rgba(0,0,0,0.06);border-bottom: none;\">\n" +
            "            <div class=\"row\" style=\"background: rgba(0,0,0,0.02);\">\n" +
            "                <div class=\"td\">\n" +
            "                    序号\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    图表\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    报警类型\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    详情\n" +
            "                </div>\n" +
            "            </div>\n" +
            "           #{AlarmDetail}" +
            "        </div>\n" +
            "        <div\n" +
            "            style=\"font-size: 16px;color: rgba(0,0,0,0.85);display: flex;align-items: center;margin-top: 16px;margin-bottom: 8px;\">\n" +
            "            <div\n" +
            "                style=\"width: 3px;height: 16px;background: #00AAA6;border-radius: 3px 3px 3px 3px;margin-right: 8px;\">\n" +
            "            </div> 子组详情\n" +
            "        </div>\n" +
            "    \n" +
            "        <div style=\"border: 1px solid rgba(0,0,0,0.06);border-bottom: none;\">\n" +
            "            <div class=\"row\" style=\"background: rgba(0,0,0,0.02);\">\n" +
            "                <div class=\"td\">\n" +
            "                    子组ID\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    产品\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    过程\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    样本量\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    子组时间\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    班次\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    批次\n" +
            "                </div>\n" +
            "                #{dsgp}" +
            "                <div class=\"td\">\n" +
            "                    操作员\n" +
            "                </div>\n" +
            "                #{testName}" +
            "            </div>\n" +
            "           #{line}" +
            "        </div>\n" +
            "    </div>\n" +
            "</body>\n" +
            "</html>";


    public static final String emailLine = "<div class=\"row\" >\n" +
            "                <div class=\"td\">\n" +
            "                    #{F_SGRP}\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    #{partName}\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    #{prcsName}\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    #{F_SGSZ}\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                   #{F_SGTM}\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    #{shiftName}\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    #{lotName}\n" +
            "                </div>\n" +
            "                #{desc}" +
            "                <div class=\"td\">\n" +
            "                    #{createName}\n" +
            "                </div>\n" +
            "                #{testVal}" +
            "            </div>\n";

    public static final String alarmDetail = "<div class=\"row\" >\n" +
            "                <div class=\"td\">\n" +
            "                    #{alarmNum}\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    #{alarmChartName}\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    #{alarmType}\n" +
            "                </div>\n" +
            "                <div class=\"td\">\n" +
            "                    #{alarmVal}\n" +
            "                </div>\n" +
            "            </div>\n";

    public static String getEmailContent(SYSTEM_NOTIFICATION_ALARM_DTO systemNotificationAlarmDto) {
        String html = emailHtml.replace("#{alarmTime}", systemNotificationAlarmDto.getAlarmTime())
                .replace("#{templateName}", systemNotificationAlarmDto.getTemplateName());

        List<SYSTEM_NOTIFICATION_ALARM_DTO.AlarmDetails> alarmDetailList = systemNotificationAlarmDto.getAlarmDetailList();
        StringBuilder alarmLine = new StringBuilder();
        for (int i = 0; i < alarmDetailList.size(); i++) {
            SYSTEM_NOTIFICATION_ALARM_DTO.AlarmDetails alarmDetails = alarmDetailList.get(i);
            String row = alarmDetail.replace("#{alarmNum}", String.valueOf(i + 1))
                    .replace("#{alarmChartName}", StringUtils.isEmpty(alarmDetails.getChartName()) ? "" : alarmDetails.getChartName())
                    .replace("#{alarmType}", alarmDetails.getAlarmType())
                    .replace("#{alarmVal}", StringUtils.isEmpty(alarmDetails.getConnector()) ? "" : alarmDetails.getActualValue() + alarmDetails.getConnector() + alarmDetails.getCompareValue());
            alarmLine.append(row);
        }

        html = html.replace("#{AlarmDetail}", alarmLine.toString());
        List<SubgroupDataDTO> list = systemNotificationAlarmDto.getList();
        if (CollectionUtils.isEmpty(list)) return html;
        Set<SGRP_DSC_DTO> collect = list.stream().map(SubgroupDataDTO::getSgrpDscList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toSet());
        StringBuilder dsgp = new StringBuilder();
        StringBuilder desc = new StringBuilder();
        for (SGRP_DSC_DTO sgrpDsc : collect) {
            dsgp.append("<div class=\"td\">\n")
                    .append(sgrpDsc.getDsgpName()).append("</div>\n");
            desc.append("<div class=\"td\">\n")
                    .append(sgrpDsc.getDescName()).append("</div>\n");
        }
        html = html.replace("#{dsgp}", dsgp.toString());
        /*循环测试名称和测试值*/
        StringBuilder testName = new StringBuilder();

        StringBuilder line = new StringBuilder();
        for (SubgroupDataDTO subgroupDataDTO : list) {
            testName.append("<div class=\"td\">\n")
                    .append(subgroupDataDTO.getTestName()).append("</div>\n");
        }
        SubgroupDataDTO subgroupDataDTO = list.get(0);
        for (int i = 0; i < subgroupDataDTO.getF_SGSZ(); i++) {
            String row = emailLine.replace("#{F_SGRP}", subgroupDataDTO.getF_SGRP().toString())
                    .replace("#{partName}", subgroupDataDTO.getPartName())
                    .replace("#{prcsName}", subgroupDataDTO.getPrcsName())
                    .replace("#{F_SGTM}", DateUtils.dateTimeTwo(subgroupDataDTO.getF_SGTM()))
                    .replace("#{shiftName}", subgroupDataDTO.getShiftName() == null ? "" : subgroupDataDTO.getShiftName())
                    .replace("#{lotName}", subgroupDataDTO.getLotName() == null ? "" : subgroupDataDTO.getLotName())
                    .replace("#{desc}", desc.toString())
                    .replace("#{F_SGSZ}", subgroupDataDTO.getF_SGSZ().toString())
                    .replace("#{createName}", subgroupDataDTO.getCreateName());
            StringBuilder testVal = new StringBuilder();
            for (SubgroupDataDTO dataDTO : list) {

                SGRP_VAL_CHILD_DTO.Test test = dataDTO.getSgrpValDto().getSgrpValChildDto().getTestList().get(i);
                if (CollectionUtils.isNotEmpty(test.getSubTestList())) {
                    List<Double> doubles = test.getSubTestList().stream().map(SGRP_VAL_CHILD_DTO.SubTest::getSubTestValue).collect(Collectors.toList());
                    String sub = doubles.stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining(", "));
                    testVal.append("<div class=\"td\">\n")
                            .append(sub).append("</div>\n");
                } else {
                    testVal.append("<div class=\"td\">\n")
                            .append(test.getTestVal()).append("</div>\n");
                }
            }
            row = row.replace("#{testVal}", testVal.toString());
            line.append(row);
        }
        html = html.replace("#{line}", line.toString()).replace("#{testName}", testName.toString());
        return html;
    }

}
