package com.yingfei.entity.util;

import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.enums.StatisticalViolationTypeEnum;
import com.yingfei.entity.vo.DataPointVO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * spc报警规则工具类
 *
 * @see StatisticalViolationTypeEnum 反射调用  RealTimeWarningRuleUtil
 */
@Slf4j
public class WarningRuleUtil {

    /**
     * 连续__点全部递增或者递减
     * 6点连续上升
     * 6点连续下降
     *
     * @param dataPointVOList 数据点集合
     * @param params          参数列表
     */
    public static void continuousUpDown(List<DataPointVO> dataPointVOList, Map<String, Object> params) {
        int n = (int) Math.round(Double.parseDouble(params.get("hits").toString()));
        if (dataPointVOList.size() < n + 1) return;
        int countIncrease = 0;
        int countDecrease = 0;
        double[] value = new double[dataPointVOList.size()];
        for (int i = 0; i < dataPointVOList.size(); i++) {
            DataPointVO dataPointVO = dataPointVOList.get(i);
            if (dataPointVO.getPoint() == null) continue;
            value[i] = dataPointVO.getPoint();
            if (i != 0) {
                if (value[i - 1] < value[i]) {
                    countIncrease = countIncrease + 1;
                    countDecrease = 0;
                }
                if (value[i - 1] > value[i]) {
                    countDecrease = countDecrease + 1;
                    countIncrease = 0;
                }
            }
            if (countIncrease >= n || countDecrease >= n) {
                if (countIncrease >= n) {
                    dataPointVO.getSubgroupInfoDTO()
                            .setAlarmDetail(StatisticalViolationTypeEnum.CONTINUOUS_RISE.getAbbr());
                } else {
                    dataPointVO.getSubgroupInfoDTO()
                            .setAlarmDetail(StatisticalViolationTypeEnum.CONTINUOUS_RISE.getAbbr());
                }
                dataPointVO.setType(1);
            }
        }
    }

    /**
     * 连续__点上下振荡
     * 14点振荡
     *
     * @param dataPointVOList 数据点集合
     * @param params          参数列表
     */
    public static void oscillatingUpDown(List<DataPointVO> dataPointVOList, Map<String, Object> params) {
        int n = (int) Math.round(Double.parseDouble(params.get("hits").toString()));
        if (dataPointVOList.size() < n + 1) return;
        //用于标记当前的增减，1表示增，-1表示减
        int mark = 0;
        int count = 0;
        double[] value = new double[dataPointVOList.size()];
        for (int i = 0; i < dataPointVOList.size(); i++) {
            DataPointVO dataPointVO = dataPointVOList.get(i);
            if (dataPointVO.getPoint() == null) continue;
            value[i] = dataPointVO.getPoint();

            //i=0时无法比较
            //第一次比较后，验证增减性
            if (i >= 1) {
                if (mark == 1) {
                    //如果继续增加以后没有减少，关闭。如果减少了，调整mark以备下一次使用
                    if (value[i - 1] > value[i]) {
                        mark = -1;
                        count += 1;
                    } else {
                        mark = 0;
                        count = 0;
                    }
                } else if (mark == -1) {
                    if (value[i - 1] < value[i]) {
                        mark = 1;
                        count += 1;
                    } else {
                        mark = 0;
                        count = 0;
                    }
                } else {
                    if (value[i - 1] < value[i]) {
                        mark = 1;
                        count += 1;
                    } else if (value[i - 1] > value[i]) {
                        mark = -1;
                        count += 1;
                    } else {
                        mark = 0;
                    }
                }
                if (count >= n) {
                    dataPointVO.getSubgroupInfoDTO()
                            .setAlarmDetail(StatisticalViolationTypeEnum.OSCILLATE_UP_AND_DOWN.getAbbr());
                    dataPointVO.setType(1);
                }
            }
        }
    }

    /**
     * 超过控制限上下判断
     * 控制上限以上
     * 控制下限以下
     *
     * @param dataPointVOList 数据点集合
     * @param params          参数列表
     */
    public static void outOfCtrlLimit(List<DataPointVO> dataPointVOList, Map<String, Object> params) {
        try {
            if (params.get("ucl") == null || params.get("lcl") == null) return;
            double ucl = Double.parseDouble(params.get("ucl").toString());
            double lcl = Double.parseDouble(params.get("lcl").toString());
            for (DataPointVO dataPointVO : dataPointVOList) {
                if (dataPointVO.getPoint() == null) continue;
                if (dataPointVO.getPoint() > ucl || dataPointVO.getPoint() < lcl) {
                    if (dataPointVO.getPoint() > ucl) {
                        dataPointVO.getSubgroupInfoDTO()
                                .setAlarmDetail(StatisticalViolationTypeEnum.ABOVE_CEILING.getAbbr());
                    } else {
                        dataPointVO.getSubgroupInfoDTO()
                                .setAlarmDetail(StatisticalViolationTypeEnum.BELOW_CEILING.getAbbr());
                    }
                    dataPointVO.setType(1);
                }
            }
        } catch (Exception e) {
            log.error("规则3执行错误---->{}", e.toString());
            e.printStackTrace();
        }
    }

    /**
     * 3点中有2点在或超过上A区
     * 3点中有2点在或低于下A区
     * 5点中有4点在或超过上B区
     * 5点中有4点在或低于下B区
     * 8点在中心线以上
     * 8点在中心线以下
     * <p>
     * ucl             控制限上限
     * lcl             控制限下限
     * cl              控制限中间线
     * hits            要判断的点数
     * cnt             数据点数
     * area            区间(1:A,2:B,3:C)
     *
     * @param dataPointVOList 数据点集合
     */
    public static void withinSigmaZone(List<DataPointVO> dataPointVOList, Map<String, Object> params) {
        int n = (int) Math.round(Double.parseDouble(params.get("hits").toString()));
        if (dataPointVOList.size() < n + 1) return;

        if (params.get("ucl") == null || params.get("lcl") == null || params.get("cl") == null) return;
        double ucl = Double.parseDouble(params.get("ucl").toString());
        double lcl = Double.parseDouble(params.get("lcl").toString());
        double cl = Double.parseDouble(params.get("cl").toString());
        int cnt = (int) Math.round(Double.parseDouble(params.get("cnt").toString()));
        int type = (int) Math.round(Double.parseDouble(params.get("type").toString()));
        /*计算上区间间距*/
        double a = (ucl - cl) / 3;
        /*计算下区间间距*/
        double b = (cl - lcl) / 3;

        /*计算区间值*/
        double areaUpA = 0d; //上A区
        double areaDownA = 0d; //下A区
        double areaUpB = 0d; //上B区
        double areaDownB = 0d; //下B区
        switch (type) {
            case 1:
                areaUpA = ucl - a;
                areaDownA = lcl + b;
                break;
            case 2:
                areaUpB = cl + a;
                areaDownB = cl - b;
                break;
            default:
                break;
        }
        //用于标记当前的增减，1表示增，-1表示减
        int mark = 0;
        int count = 0;
        for (int i = 0; i < dataPointVOList.size(); i++) {
            DataPointVO dataPointVO = dataPointVOList.get(i);
            if (dataPointVO.getPoint() == null) continue;
            double value = dataPointVO.getPoint();
            switch (type) {
                case 1:
                    if (value >= areaUpA) {
                        if (mark != 1) count = 0;
                        count += 1;
                        mark = 1;
                    } else if (value <= areaDownA) {
                        if (mark != -1) count = 0;
                        count += 1;
                        mark = -1;

                    } else {
                        mark = 0;
                        count = 0;
                    }
                    break;
                case 2:
                    if (value >= areaUpB) {
                        if (mark != 1) count = 0;
                        count += 1;
                        mark = 1;
                    } else if (value <= areaDownB) {
                        if (mark != -1) count = 0;
                        count += 1;
                        mark = -1;
                    } else {
                        mark = 0;
                        count = 0;
                    }
                    break;
                case 3:
                    if (value > cl) {
                        if (mark != 1) count = 0;
                        mark = 1;
                        count += 1;
                    } else if (value < cl) {
                        if (mark != -1) count = 0;
                        mark = -1;
                        count += 1;
                    } else {
                        mark = 0;
                        count = 0;
                    }
                    break;
                default:
                    break;
            }

            if (i >= cnt && count >= n) {
                switch (type) {
                    case 1:
                        if (mark == 1) {
                            dataPointVO.getSubgroupInfoDTO()
                                    .setAlarmDetail(StatisticalViolationTypeEnum.ABOVE_ZONE_A.getAbbr());
                        } else if (mark == -1) {
                            dataPointVO.getSubgroupInfoDTO()
                                    .setAlarmDetail(StatisticalViolationTypeEnum.BELOW_ZONE_A.getAbbr());
                        }
                        break;
                    case 2:
                        if (mark == 1) {
                            dataPointVO.getSubgroupInfoDTO()
                                    .setAlarmDetail(StatisticalViolationTypeEnum.ABOVE_ZONE_B.getAbbr());
                        } else if (mark == -1) {
                            dataPointVO.getSubgroupInfoDTO()
                                    .setAlarmDetail(StatisticalViolationTypeEnum.BELOW_ZONE_B.getAbbr());
                        }
                        break;
                    case 3:
                        if (mark == 1) {
                            dataPointVO.getSubgroupInfoDTO()
                                    .setAlarmDetail(StatisticalViolationTypeEnum.ABOVE_CENTER_LINE.getAbbr());
                        } else if (mark == -1) {
                            dataPointVO.getSubgroupInfoDTO()
                                    .setAlarmDetail(StatisticalViolationTypeEnum.BELOW_CENTER_LINE.getAbbr());
                        }
                        break;
                    default:
                        break;
                }
                dataPointVO.setType(1);
            }
        }
    }

    /**
     * 15点在C区
     * 8点在C区以外
     * <p>
     * ucl             控制限上限
     * lcl             控制限下限
     * hits            要判断的点数
     * type            类型。1：在C区，2：在C区以外
     *
     * @param dataPointVOList 数据点集合
     */
    public static void inOrOutZoneC(List<DataPointVO> dataPointVOList, Map<String, Object> params) {
        int n = (int) Math.round(Double.parseDouble(params.get("hits").toString()));
        if (dataPointVOList.size() < n + 1) return;

        if (params.get("ucl") == null || params.get("lcl") == null || params.get("cl") == null) return;

        double ucl = Double.parseDouble(params.get("ucl").toString());
        double lcl = Double.parseDouble(params.get("lcl").toString());
        double cl = Double.parseDouble(params.get("cl").toString());
        int type = (int) Math.round(Double.parseDouble(params.get("type").toString()));
        /*计算上区间间距*/
        double a = (ucl - cl) / 3;
        /*计算下区间间距*/
        double b = (cl - lcl) / 3;

        double areaUpC = cl + a; //上C区
        double areaDownC = cl - b; //下C区
        int count = 0;
        for (DataPointVO dataPointVO : dataPointVOList) {
            if (dataPointVO.getPoint() == null) continue;
            double value = dataPointVO.getPoint();
            switch (type) {
                case 1:
                    if (value > areaDownC && value < areaUpC) {
                        count += 1;
                    } else {
                        count = 0;
                    }
                    break;
                case 2:
                    if (value < areaDownC || value > areaUpC) {
                        count += 1;
                    } else {
                        count = 0;
                    }
                    break;
                default:
                    break;
            }
            if (count >= n) {
                if (type == 1) {
                    dataPointVO.getSubgroupInfoDTO()
                            .setAlarmDetail(StatisticalViolationTypeEnum.WITHIN_ZONE_C.getAbbr());
                } else {
                    dataPointVO.getSubgroupInfoDTO()
                            .setAlarmDetail(StatisticalViolationTypeEnum.OUTSIDE_ZONE_C.getAbbr());
                }
                dataPointVO.setType(1);
            }
        }
    }

    /**
     * 失效子组
     *
     * @param dataPointVOList
     * @param params
     */
    public static void rule6(List<DataPointVO> dataPointVOList, Map<String, Object> params) {
        for (DataPointVO dataPointVO : dataPointVOList) {
            if (dataPointVO.getSubgroupInfoDTO().getFlag() == YesOrNoEnum.YES.getType()) {
                dataPointVO.setType(1);
                dataPointVO.getSubgroupInfoDTO()
                        .setAlarmDetail(StatisticalViolationTypeEnum.IGNORE_SUBGROUP.getAbbr());
            }
        }
    }

    /**
     * 判断CP/CPK在目标下方(小于目标CP/CPK)
     */
    public static void processCapabilityBelowTarget(List<DataPointVO> dataPointVOList, Map<String, Object> params) {
//        String data = params.get(MonitorWarningRuleUtil.DATA_SUMMARY_DTO).toString();
//        if (StringUtils.isEmpty(data)) return;
//        DataSummaryDTO dataSummaryDTO = JSONObject.parseObject(data, DataSummaryDTO.class);
//        String spec = params.get(MonitorWarningRuleUtil.SPEC_INF_DTO).toString();
//        if (StringUtils.isEmpty(spec)) return;
//        SPEC_INF_DTO specInfDto = JSONObject.parseObject(spec, SPEC_INF_DTO.class);
//
//        if (dataSummaryDTO.getCp() < specInfDto.getF_CP() || dataSummaryDTO.getCpk()<specInfDto.getF_CPK()){
//            List<String> list = getDetails(params, StatisticalViolationTypeEnum.CPK_IS_BELOW_THE_TARGET);
//            if (CollectionUtils.isNotEmpty(list))
//                params.put(details, JSONArray.toJSONString(list));
//        }
    }
}
