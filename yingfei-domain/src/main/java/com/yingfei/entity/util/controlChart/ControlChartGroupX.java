package com.yingfei.entity.util.controlChart;

import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.SGRP_VAL_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.enums.CorrectionConstantEnum;
import com.yingfei.entity.util.CorrectionConstantUtil;
import com.yingfei.entity.vo.DataPointVO;
import com.yingfei.entity.util.ControlChartCalculateService;
import com.yingfei.entity.vo.SubgroupDataVO;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 控制图分组类型-------->均值图
 */
@Service
public class ControlChartGroupX implements ControlChartCalculateService {


    /**
     * 图类型为均值-极差-------> 数据点计算公式为: 子组内实际值的均值
     * 图类型为均值-极差-极差件内-------> 数据点计算公式为: 子组内每个样本子测试均值的均值
     * 图类型为均值-极差-标准差件内-------> 数据点计算公式为: 子组内每个样本子测试均值的均值
     * 图类型为均值-标准差-------> 数据点计算公式为: 子组内实际值的均值
     * 图类型为均值-标准差-极差件内-------> 数据点计算公式为: 子组内每个样本子测试均值的均值
     * 图类型为均值-标准差-标准差件内-------> 数据点计算公式为: 子组内每个样本子测试均值的均值
     *
     * @param subgroupDataDTOList 子组测试数据,要按子组时间正序排列
     * @param chartTypeEnum       图类型
     */
    @Override
    public List<DataPointVO> dataPoint(List<SubgroupDataDTO> subgroupDataDTOList, ControlChartTypeEnum chartTypeEnum) {
        List<DataPointVO> list = new ArrayList<>();
        switch (chartTypeEnum) {
            case CONTROL_CHART_X_R:
            case CONTROL_CHART_X_SD:
            case CONTROL_CHART_X_R_RW:
            case CONTROL_CHART_X_R_SDW:
            case CONTROL_CHART_X_SD_RW:
            case CONTROL_CHART_X_SD_SDW:
                subgroupDataDTOList.forEach(subgroupDataDTO -> {
                    list.add(DataPointVO.getDataPoint(subgroupDataDTO, subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getAverage()));
                });
                break;
            default:
                break;
        }
        return list;
    }

    /**
     * F_MEAN
     *
     * @return
     */
    @Override
    public Double controlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getF_MEAN();
    }

    /**
     * F_MEAN+3*F_SP/Sqrt(n)
     *
     * @return
     */
    @Override
    public Double controlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getF_MEAN() + controlLimitDto.getF_SIGMA_COUNT() * controlLimitDto.getF_SP() / Math.sqrt(controlLimitDto.getN());
    }

    /**
     * F_MEAN-3*F_SP/Sqrt(n)  如果F_SPL=0
     * F_MEAN-3*F_SPL/Sqrt(n)  如果F_SPL>0
     */
    @Override
    public Double controlLimitLCL(ControlLimitDTO controlLimitDto) {
        if (controlLimitDto.getF_SPL() == 0) {
            return controlLimitDto.getF_MEAN() - controlLimitDto.getF_SIGMA_COUNT() * controlLimitDto.getF_SP() / Math.sqrt(controlLimitDto.getN());
        } else if (controlLimitDto.getF_SPL() > 0) {
            return controlLimitDto.getF_MEAN() - controlLimitDto.getF_SIGMA_COUNT() * controlLimitDto.getF_SPL() / Math.sqrt(controlLimitDto.getN());
        } else {
            return 0d;
        }
    }

    /**
     * 历史控制限CL
     * Mean
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getMean();
    }

    /**
     * 历史控制限UCL
     * Mean+A2*R
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getMean() + CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_A2) * controlLimitDto.getR();
    }

    /**
     * 历史控制限LCL
     * Mean-A2*R
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitLCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getMean() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_A2) * controlLimitDto.getR();
    }
}
