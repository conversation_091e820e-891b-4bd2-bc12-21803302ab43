package com.yingfei.entity.util.cron;

import cn.hutool.core.util.ArrayUtil;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.entity.dto.TaskScheduleModel;
import com.yingfei.entity.enums.TimeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

@Slf4j
public class CronUtil {
    /**
     * 方法摘要：构建Cron表达式
     *
     * @param taskScheduleModel
     * @return String
     */
    public static String createCronExpression(TaskScheduleModel taskScheduleModel) {
        StringBuilder cronExp = new StringBuilder();

        if (null == taskScheduleModel.getTimeEnum()) {
            log.info("执行周期未配置");
            return null;
        }
        switch (taskScheduleModel.getTimeEnum()) {
            case SECOND:
                cronExp.append("0/").append(taskScheduleModel.getBeApart()).append(" * * * * ?");
                break;
            case MINUTE:
                cronExp.append(taskScheduleModel.getSecond()).append(" 0/").append(taskScheduleModel.getBeApart()).append(" * * * ?");
                break;
            case HOUR:
                cronExp.append(taskScheduleModel.getSecond()).append(" ").append(taskScheduleModel.getMinute()).append(" 0/").append(taskScheduleModel.getBeApart()).append(" * * ?");
                break;
            case DAY:
                //秒
                cronExp.append(taskScheduleModel.getSecond()).append(" ");
                //分
                cronExp.append(taskScheduleModel.getMinute()).append(" ");
                //小时
                cronExp.append(taskScheduleModel.getHour()).append(" ");
                if (taskScheduleModel.getBeApart() != null) {
                    cronExp.append("1");//日
                    cronExp.append("/");
                    cronExp.append(taskScheduleModel.getBeApart());
                    cronExp.append(" ");
                    cronExp.append("* ");//月
                    cronExp.append("? ");//周
                } else {
                    cronExp.append("* ");//日
                    cronExp.append("* ");//月
                    cronExp.append("?");//周
                }
                break;
            case WEEK:
                //秒
                cronExp.append(taskScheduleModel.getSecond()).append(" ");
                //分
                cronExp.append(taskScheduleModel.getMinute()).append(" ");
                //小时
                cronExp.append(taskScheduleModel.getHour()).append(" ");
                //一个月中第几天
                cronExp.append("? ");
                //月份
                cronExp.append("* ");
                //周
                Integer[] weeks = taskScheduleModel.getDayOfWeeks();
                for (int i = 0; i < weeks.length; i++) {
                    if (i == 0) {
                        cronExp.append(weeks[i]);
                    } else {
                        cronExp.append(Constants.COMMA).append(weeks[i]);
                    }
                }
                break;
            case MONTH:
                //秒
                cronExp.append(taskScheduleModel.getSecond()).append(" ");
                //分
                cronExp.append(taskScheduleModel.getMinute()).append(" ");
                //小时
                cronExp.append(taskScheduleModel.getHour()).append(" ");
                //一个月中的哪几天
                Integer[] days = taskScheduleModel.getDayOfMonths();
                for (int i = 0; i < days.length; i++) {
                    if (i == 0) {
                        if (days[i] == 32) {
                            //本月最后一天
                            return "0 0 0 L * ?";
                        } else {
                            cronExp.append(days[i]);
                        }
                    } else {
                        cronExp.append(Constants.COMMA).append(days[i]);
                    }
                }
                //月份
                cronExp.append(" * ");
                //周
                cronExp.append("?");
                break;
            case YEAR:
                //秒
                cronExp.append(taskScheduleModel.getSecond()).append(" ");
                //分
                cronExp.append(taskScheduleModel.getMinute()).append(" ");
                //小时
                cronExp.append(taskScheduleModel.getHour()).append(" ");
                //一个年中的哪几天
                Integer[] yearDays = taskScheduleModel.getDayOfMonths();
                if (ArrayUtil.isEmpty(yearDays)) {
                    cronExp.append("*");
                } else {
                    for (int i = 0; i < yearDays.length; i++) {
                        if (i == 0) {
                            cronExp.append(yearDays[i]);
                        } else {
                            cronExp.append(Constants.COMMA).append(yearDays[i]);
                        }
                    }
                }
                //月份
                Integer[] months = taskScheduleModel.getMonths();
                if (ArrayUtil.isEmpty(months)) {
                    cronExp.append(" *");
                } else {
                    for (int i = 0; i < months.length; i++) {
                        Integer month = months[i];
                        if (month > 12) {
                            throw new RuntimeException("月份数据异常: " + Arrays.toString(months));
                        }
                        if (i == 0) {
                            cronExp.append(" ").append(month);
                        } else {
                            cronExp.append(Constants.COMMA).append(month);
                        }
                    }
                }
                cronExp.append(" ?");
                break;
        }
        return cronExp.toString();
    }

    public static String createLoopCronExpression(int rate, int cycle) {
        String cron = "";
        switch (rate) {
            case 0:// 每cycle秒执行一次
                cron = "0/" + cycle + " * * * * ?";
                break;
            case 1:// 每cycle分钟执行一次
                cron = "0 0/" + cycle + " * * * ?";
                break;
            case 2:// 每cycle小时执行一次
                cron = "0 0 0/" + cycle + " * * ?";
                break;
            case 3:// 每cycle天的0点执行一次
                cron = "0 0 0 1/" + cycle + " * ?";
                break;
            case 4:// 每cycle月的1号0点执行一次
                cron = "0 0 0 1 1/" + cycle + " ? ";
                break;
            case 5://  每天cycle点执行一次
                cron = "0 0 " + cycle+ "  * * ?";
                break;
        }
        return cron;
    }
}
