package com.yingfei.entity.util;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.EvictingQueue;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.entity.dto.CalculatedControlLimit;
import com.yingfei.entity.dto.DataSummaryDTO;
import com.yingfei.entity.dto.SPEC_INF_DTO;
import com.yingfei.entity.enums.StatisticalViolationTypeEnum;
import com.yingfei.entity.vo.SubgroupDataVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import java.util.*;

/**
 * spc监控报警规则工具类
 *
 * @see StatisticalViolationTypeEnum 反射调用
 */
@Slf4j
public class MonitorWarningRuleUtil {

    /*报警详情*/
    public static final String details = "details";
    public static final String UCL = "ucl";
    public static final String UCL_ACTUALVALUE = "ucl_actualValue";
    public static final String LCL = "lcl";
    public static final String LCL_ACTUALVALUE = "lcl__actualValue";
    public static final String HITS = "hits";
    public static final String AREA = "area";
    public static final String CNT = "cnt";
    public static final String TYPE = "type";
    public static final String DATA_SUMMARY_DTO = "dataSummaryDTO";
    public static final String SPEC_INF_DTO = "specInfDto";

    /**
     * 连续__点全部递增或者递减
     * 6点连续上升
     * 6点连续下降
     *
     * @param calculatedControlLimitList 数据点集合
     * @param params                     参数列表
     */
    public static void continuousUpDown(List<CalculatedControlLimit> calculatedControlLimitList, Map<String, Object> params) {
        int n = (int) Math.round(Double.parseDouble(params.get(HITS).toString()));
        if (calculatedControlLimitList.size() < n + 1) return;
        int countIncrease = 0;
        int countDecrease = 0;
        double[] value = new double[calculatedControlLimitList.size()];
        for (int i = 0; i < calculatedControlLimitList.size(); i++) {
            CalculatedControlLimit dataPointVO = calculatedControlLimitList.get(i);
            Double dataPoint = dataPointVO.getDataPoint();
            if (dataPoint == null) dataPoint = Double.NaN;
            value[i] = dataPoint;
            if (Double.isNaN(dataPoint))
                continue;
            if (i != 0) {
                if (Double.isNaN(value[i - 1])) continue;
                if (value[i - 1] < value[i]) {
                    countIncrease = countIncrease + 1;
                    countDecrease = 0;
                } else if (value[i - 1] > value[i]) {
                    countDecrease = countDecrease + 1;
                    countIncrease = 0;
                } else {
                    return;
                }
            }
            if (countDecrease > 0 && countIncrease > 0) return;
            if (countIncrease >= n || countDecrease >= n) {
                List<String> list;
                if (countIncrease >= n) {
                    list = getDetails(params, StatisticalViolationTypeEnum.CONTINUOUS_RISE);
                } else {
                    list = getDetails(params, StatisticalViolationTypeEnum.CONTINUOUS_DECLINE);
                }
                if (CollectionUtils.isNotEmpty(list))
                    params.put(details, JSONArray.toJSONString(list));
            }
        }
    }

    /**
     * 连续__点上下振荡
     * 14点振荡
     *
     * @param calculatedControlLimitList 数据点集合
     * @param params                     参数列表
     */
    public static void oscillatingUpDown(List<CalculatedControlLimit> calculatedControlLimitList, Map<String, Object> params) {
        int hits = (int) Math.round(Double.parseDouble(params.get(HITS).toString()));
        if (calculatedControlLimitList.size() < hits + 1) return;
        //用于标记当前的增减，1表示增，-1表示减
        int mark = 0;
        int count = 0;
        double[] value = new double[hits + 1];
        for (int i = 0; i < calculatedControlLimitList.size(); i++) {
            CalculatedControlLimit dataPointVO = calculatedControlLimitList.get(i);
            Double dataPoint = dataPointVO.getDataPoint();
            if (dataPoint == null) dataPoint = Double.NaN;
            value[i] = dataPoint;
            if (Double.isNaN(dataPoint)) continue;
            //i=0时无法比较
            //第一次比较后，验证增减性
            if (i >= 1) {
                if (Double.isNaN(value[i - 1])) continue;
                if (mark == 1) {
                    //如果继续增加以后没有减少，关闭。如果减少了，调整mark以备下一次使用
                    if (value[i - 1] > value[i]) {
                        mark = -1;
                        count += 1;
                    } else {
                        return;
                    }
                } else if (mark == -1) {
                    if (value[i - 1] < value[i]) {
                        mark = 1;
                        count += 1;
                    } else {
                        return;
                    }
                } else {
                    if (value[i - 1] < value[i]) {
                        mark = 1;
                        count += 1;
                    } else if (value[i - 1] > value[i]) {
                        mark = -1;
                        count += 1;
                    } else {
                        return;
                    }
                }
                if (count >= hits) {
                    List<String> list = getDetails(params, StatisticalViolationTypeEnum.OSCILLATE_UP_AND_DOWN);
                    if (CollectionUtils.isNotEmpty(list))
                        params.put(details, JSONArray.toJSONString(list));
                }
            }
        }
    }


    private static List<String> getDetails(Map<String, Object> params, StatisticalViolationTypeEnum ruleTypeEnum) {
        Object o = params.get(details);
        List<String> list;
        if (o == null) {
            list = new ArrayList<>();
            list.add(ruleTypeEnum.getDescription());
        } else {
            list = JSONArray.parseArray(o.toString(), String.class);
            list.add(ruleTypeEnum.getDescription());
        }
        return list;
    }

    /**
     * 超过控制限上下判断
     * 控制上限以上
     * 控制下限以下
     *
     * @param calculatedControlLimitList 数据点集合
     * @param params                     参数列表
     */
    public static void outOfCtrlLimit(List<CalculatedControlLimit> calculatedControlLimitList, Map<String, Object> params) {
        try {
            int area = (int) Math.round(Double.parseDouble(params.get(AREA).toString()));
            for (CalculatedControlLimit calculatedControlLimit : calculatedControlLimitList) {
                if (calculatedControlLimit.getUCL() == null || calculatedControlLimit.getLCL() == null) continue;
                double ucl = calculatedControlLimit.getUCL();
                double lcl = calculatedControlLimit.getLCL();
                if (calculatedControlLimit.getDataPoint() == null) calculatedControlLimit.setDataPoint(Double.NaN);
                if (Double.isNaN(calculatedControlLimit.getDataPoint())) continue;
                if (calculatedControlLimit.getDataPoint() > ucl || calculatedControlLimit.getDataPoint() < lcl) {
                    List<String> list = null;
                    if (area == 1) {
                        if (calculatedControlLimit.getDataPoint() > ucl) {
                            list = getDetails(params, StatisticalViolationTypeEnum.ABOVE_CEILING);
                            params.put(UCL, ucl);
                            params.put(UCL_ACTUALVALUE, calculatedControlLimit.getDataPoint());
                        }
                    } else {
                        if (calculatedControlLimit.getDataPoint() < lcl) {
                            list = getDetails(params, StatisticalViolationTypeEnum.BELOW_CEILING);
                            params.put(LCL, lcl);
                            params.put(LCL_ACTUALVALUE, calculatedControlLimit.getDataPoint());
                        }
                    }
                    if (list != null) {
                        params.put(details, JSONArray.toJSONString(list));
                    }
                }
            }
        } catch (Exception e) {
            log.error("超控制限报警判断执行错误---->{}", e.toString());
            e.printStackTrace();
        }
    }

    /**
     * 3点中有2点在或超过上A区
     * 3点中有2点在或低于下A区
     * 5点中有4点在或超过上B区
     * 5点中有4点在或低于下B区
     * 8点在中心线以上
     * 8点在中心线以下
     * <p>
     * ucl             控制限上限
     * lcl             控制限下限
     * cl              控制限中间线
     * hits            要判断的点数
     * cnt             数据点数
     * area            区间(1:A,2:B,3:C)
     *
     * @param calculatedControlLimitList 数据点集合
     */
    public static void withinSigmaZone(List<CalculatedControlLimit> calculatedControlLimitList, Map<String, Object> params) {
        /*todo 先用reverse倒序*/
        Collections.reverse(calculatedControlLimitList);
        int hits = (int) Math.round(Double.parseDouble(params.get(HITS).toString()));
        if (calculatedControlLimitList.size() < hits) return;
        int cnt = (int) Math.round(Double.parseDouble(params.get(CNT).toString()));

        int type = (int) Math.round(Double.parseDouble(params.get(TYPE).toString()));
        int area = (int) Math.round(Double.parseDouble(params.get(AREA).toString()));

        Queue<Integer> queue = EvictingQueue.create(cnt);
        boolean b = false;
        //用于标记当前的增减，1表示增，-1表示减
        int mark = 0;
        for (int i = 0; i < calculatedControlLimitList.size(); i++) {
            CalculatedControlLimit calculatedControlLimit = calculatedControlLimitList.get(i);
            if (calculatedControlLimit.getUCL() == null || calculatedControlLimit.getLCL() == null || calculatedControlLimit.getCL() == null)
                return;
            double cl = calculatedControlLimit.getCL();
            double areaUpA = calculatedControlLimit.getAreaUpA(); //上A区
            double areaDownA = calculatedControlLimit.getAreaDownA(); //下A区
            double areaUpB = calculatedControlLimit.getAreaUpB(); //上B区
            double areaDownB = calculatedControlLimit.getAreaDownB(); //下B区
            Double dataPoint = calculatedControlLimit.getDataPoint();
            if (dataPoint == null) dataPoint = Double.NaN;
            double value = dataPoint;
            if (Double.isNaN(value)) continue;
            switch (type) {
                case 1:
                    if (value >= areaUpA) {
                        mark = 1;
                    } else if (value <= areaDownA) {
                        mark = -1;
                    } else {
                        mark = 0;
                    }
                    break;
                case 2:
                    if (value >= areaUpB) {
                        mark = 1;
                    } else if (value <= areaDownB) {
                        mark = -1;
                    } else {
                        mark = 0;
                    }
                    break;
                case 3:
                    if (value > cl) {
                        mark = 1;
                    } else if (value < cl) {
                        mark = -1;
                    } else {
                        mark = 0;
                    }
                    break;
                default:
                    break;
            }
            if (i == 0 && mark == 0) return;
            queue.add(mark);
            if (queue.size() >= hits) {
                List<String> list = new ArrayList<>();
                int numUp = 0;
                int numDown = 0;
                for (Integer m : queue) {
                    if (numUp > 0 && numDown > 0) break;
                    if (m == 1) numUp++;
                    if (m == -1) numDown++;
                }
                switch (type) {
                    case 1:
                        if (area == 1 && numUp == hits && mark == 1) {
                            list = getDetails(params, StatisticalViolationTypeEnum.ABOVE_ZONE_A);
                            b = true;
                        } else if (area == 2 && numDown == hits && mark == -1) {
                            list = getDetails(params, StatisticalViolationTypeEnum.BELOW_ZONE_A);
                            b = true;
                        }
                        break;
                    case 2:
                        if (area == 1 && numUp == hits && mark == 1) {
                            list = getDetails(params, StatisticalViolationTypeEnum.ABOVE_ZONE_B);
                            b = true;
                        } else if (area == 2 && numDown == hits && mark == -1) {
                            list = getDetails(params, StatisticalViolationTypeEnum.BELOW_ZONE_B);
                            b = true;
                        }
                        break;
                    case 3:
                        if (area == 1 && numUp == hits && mark == 1) {
                            list = getDetails(params, StatisticalViolationTypeEnum.ABOVE_CENTER_LINE);
                            b = true;
                        } else if (area == 2 && numDown == hits && mark == -1) {
                            list = getDetails(params, StatisticalViolationTypeEnum.BELOW_CENTER_LINE);
                            b = true;
                        }
                        break;
                    default:
                        break;
                }
                if (CollectionUtils.isNotEmpty(list))
                    params.put(details, JSONArray.toJSONString(list));
            }
        }
    }


    /**
     * 15点在C区
     * 8点在C区以外
     * <p>
     * ucl             控制限上限
     * lcl             控制限下限
     * hits            要判断的点数
     * type            类型。1：在C区，2：在C区以外
     *
     * @param calculatedControlLimitList 数据点集合
     */
    public static void inOrOutZoneC(List<CalculatedControlLimit> calculatedControlLimitList, Map<String, Object> params) {
        int hits = (int) Math.round(Double.parseDouble(params.get(HITS).toString()));
        if (calculatedControlLimitList.size() < hits) return;


        int area = (int) Math.round(Double.parseDouble(params.get(AREA).toString()));


        int count = 0;
        for (CalculatedControlLimit calculatedControlLimit : calculatedControlLimitList) {
            if (calculatedControlLimit.getUCL() == null || calculatedControlLimit.getLCL() == null || calculatedControlLimit.getCL() == null)
                return;
            Double dataPoint = calculatedControlLimit.getDataPoint();
            if (dataPoint == null) dataPoint = Double.NaN;
            double value = dataPoint;
            if (Double.isNaN(value)) continue;
            double areaUpC = calculatedControlLimit.getAreaUpB(); //上C区
            double areaDownC = calculatedControlLimit.getAreaDownB(); //下C区
            switch (area) {
                case 1:
                    if (value > areaDownC && value < areaUpC) {
                        count += 1;
                    } else {
                        return;
                    }
                    break;
                case 2:
                    if (value < areaDownC || value > areaUpC) {
                        count += 1;
                    } else {
                        return;
                    }
                    break;
                default:
                    break;
            }
            if (count >= hits) {
                List<String> list;
                if (area == 1) {
                    list = getDetails(params, StatisticalViolationTypeEnum.WITHIN_ZONE_C);
                } else {
                    list = getDetails(params, StatisticalViolationTypeEnum.OUTSIDE_ZONE_C);
                }
                if (CollectionUtils.isNotEmpty(list))
                    params.put(details, JSONArray.toJSONString(list));
            }
        }
    }

    /**
     * 失效子组
     *
     * @param calculatedControlLimitList
     * @param params
     */
    public static void rule6(List<CalculatedControlLimit> calculatedControlLimitList, Map<String, Object> params) {

    }

    /**
     * 判断CP/CPK在目标下方(小于目标CP/CPK)
     */
    public static void processCapabilityBelowTarget(List<CalculatedControlLimit> calculatedControlLimitList, Map<String, Object> params) {
        String data = params.get(DATA_SUMMARY_DTO).toString();
        if (StringUtils.isEmpty(data)) return;
        DataSummaryDTO dataSummaryDTO = JSONObject.parseObject(data, DataSummaryDTO.class);
        String spec = params.get(SPEC_INF_DTO).toString();
        if (StringUtils.isEmpty(spec)) return;
        SPEC_INF_DTO specInfDto = JSONObject.parseObject(spec, SPEC_INF_DTO.class);

        if (dataSummaryDTO.getCp() < specInfDto.getF_CP() || dataSummaryDTO.getCpk() < specInfDto.getF_CPK()) {
            List<String> list = getDetails(params, StatisticalViolationTypeEnum.CPK_IS_BELOW_THE_TARGET);
            if (CollectionUtils.isNotEmpty(list))
                params.put(details, JSONArray.toJSONString(list));
        }
    }
}
