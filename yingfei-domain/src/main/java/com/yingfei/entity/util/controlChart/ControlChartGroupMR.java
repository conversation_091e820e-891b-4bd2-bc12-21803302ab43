package com.yingfei.entity.util.controlChart;

import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.SGRP_VAL_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.enums.CorrectionConstantEnum;
import com.yingfei.entity.util.CorrectionConstantUtil;
import com.yingfei.entity.vo.DataPointVO;
import com.yingfei.entity.util.ControlChartCalculateService;
import com.yingfei.entity.vo.SubgroupDataVO;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 控制图分组类型-------->移动极差图
 */
@Service
public class ControlChartGroupMR implements ControlChartCalculateService {


    /**
     * 图类型为单值-移动极差图-------> 数据点计算公式为: (当前子组的实际值-上一子组实际值)的绝对值
     * 图类型为单值-移动极差-极差件内-------> 数据点计算公式为: (当前子组均值-上一子组均值)的绝对值
     * 图类型为单值-移动极差-标准差件内-------> 数据点计算公式为: (当前子组均值-上一子组均值)的绝对值
     * 极差=最大值-最小值
     *
     * @param subgroupDataDTOList 子组测试数据,要按子组时间正序排列
     * @param chartTypeEnum       图类型
     */
    @Override
    public List<DataPointVO> dataPoint(List<SubgroupDataDTO> subgroupDataDTOList, ControlChartTypeEnum chartTypeEnum) {
        List<DataPointVO> list = new ArrayList<>();
        AtomicInteger i = new AtomicInteger(0);
        switch (chartTypeEnum) {
            case CONTROL_CHART_IX_MR:
                AtomicReference<Double> d = new AtomicReference<>(0d);
                subgroupDataDTOList.forEach(subgroupDataDTO -> {
                    Double sum = subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getAverage();
                    if (sum == null) {
                        SubgroupDataVO subgroupDataVO = new SubgroupDataVO();
                        BeanUtils.copyPropertiesIgnoreNull(subgroupDataDTO, subgroupDataVO);
                        SGRP_VAL_CHILD_DTO.getChildNum(subgroupDataVO, subgroupDataDTO.getSgrpValDto().getSgrpValChildDto());
                        sum = subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getAverage();
                        if (sum == null) {
                            return;
                        }
                    }
                    if (d.get() == 0d && i.get() == 0) {
                        d.set(sum);
                        list.add(DataPointVO.getNull());
                    } else {
                        list.add(DataPointVO.getDataPoint(subgroupDataDTO, Math.abs(sum - d.get())));
                        d.set(sum);
                    }
                    i.getAndIncrement();
                });

                break;
            case CONTROL_CHART_IX_MR_RW:
            case CONTROL_CHART_IX_MR_SDW:
                AtomicReference<Double> db = new AtomicReference<>(0d);
                subgroupDataDTOList.forEach(subgroupDataDTO -> {
                    Double aDouble = subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getAverage();
                    if (db.get() == 0d && i.get() == 0) {
                        db.set(aDouble);
                        list.add(DataPointVO.getNull());
                    } else {
                        list.add(DataPointVO.getDataPoint(subgroupDataDTO, Math.abs(aDouble - db.get())));
                        db.set(aDouble);
                    }
                });
                break;
            default:
                break;
        }
        return list;
    }

    /**
     * F_SP*d2
     *
     * @return
     */
    @Override
    public Double controlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getF_SP() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_D2);
    }

    /**
     * CL*D4
     *
     * @return
     */
    @Override
    public Double controlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_D4);
    }

    /**
     * 0
     */
    @Override
    public Double controlLimitLCL(ControlLimitDTO controlLimitDto) {
        return 0d;
    }

    /**
     * 控制限CL
     * R
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getR();
    }

    /**
     * 控制限UCL
     * CL*D4
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_D4);
    }

    /**
     * 控制限LCL
     * 0
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitLCL(ControlLimitDTO controlLimitDto) {
        return 0d;
    }
}
