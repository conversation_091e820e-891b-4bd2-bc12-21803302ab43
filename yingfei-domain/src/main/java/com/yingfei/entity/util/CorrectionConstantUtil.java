package com.yingfei.entity.util;


import com.yingfei.entity.enums.CorrectionConstantEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2023-12-07 下午 3:52
 * @description 纠偏常量计算工具类
 */
public class CorrectionConstantUtil {
    /**
     * 纠偏常量
     */
    static Map<String, Double> correctionMap = new HashMap() {{
        /**
         * c4
         */
        put("c4_1", 0.0d);
        put("c4_2", 0.797884561);
        put("c4_3", 0.886226925);
        put("c4_4", 0.921317732);
        put("c4_5", 0.939985603);
        put("c4_6", 0.951532862);
        put("c4_7", 0.959368789);
        put("c4_8", 0.965030456);
        put("c4_9", 0.9693107);
        put("c4_10", 0.972659274);
        put("c4_11", 0.975350077);
        put("c4_12", 0.977559352);
        put("c4_13", 0.979405604);
        put("c4_14", 0.980971437);
        put("c4_15", 0.982316177);
        put("c4_16", 0.983483532);
        put("c4_17", 0.984506405);
        put("c4_18", 0.985410044);
        put("c4_19", 0.986214137);
        put("c4_20", 0.986934268);
        put("c4_21", 0.987582929);
        put("c4_22", 0.988170253);
        put("c4_23", 0.988704545);
        put("c4_24", 0.989192675);
        put("c4_25", 0.989640376);
        put("c4_26", 0.990052469);
        put("c4_27", 0.990433039);
        put("c4_28", 0.99078557);
        put("c4_29", 0.991113048);
        put("c4_30", 0.991418053);
        put("c4_31", 0.991702821);
        put("c4_32", 0.991969301);
        put("c4_33", 0.992219198);
        put("c4_34", 0.992454016);
        put("c4_35", 0.992675077);
        put("c4_36", 0.992883556);
        put("c4_37", 0.993080499);
        put("c4_38", 0.993266835);
        put("c4_39", 0.9934434);
        put("c4_40", 0.993610943);
        put("c4_41", 0.993770137);
        put("c4_42", 0.993921592);
        put("c4_43", 0.994065858);
        put("c4_44", 0.994203436);
        put("c4_45", 0.994334779);
        put("c4_46", 0.994460302);
        put("c4_47", 0.994580385);
        put("c4_48", 0.994695372);
        put("c4_49", 0.994805581);
        put("c4_50", 0.994911305);
        put("c4_51", 0.995012811);
        put("c4_52", 0.995110347);
        put("c4_53", 0.995204141);
        put("c4_54", 0.995294405);
        put("c4_55", 0.995381334);
        put("c4_56", 0.99546511);
        put("c4_57", 0.995545901);
        put("c4_58", 0.995623863);
        put("c4_59", 0.995699144);
        put("c4_60", 0.995771878);
        put("c4_61", 0.995842194);
        put("c4_62", 0.995910209);
        put("c4_63", 0.995976035);
        put("c4_64", 0.996039775);
        put("c4_65", 0.996101528);
        put("c4_66", 0.996161384);
        put("c4_67", 0.996219431);
        put("c4_68", 0.996275748);
        put("c4_69", 0.996330411);
        put("c4_70", 0.996383494);
        put("c4_71", 0.996435062);
        put("c4_72", 0.996485181);
        put("c4_73", 0.99653391);
        put("c4_74", 0.996581307);
        put("c4_75", 0.996627424);
        put("c4_76", 0.996672314);
        put("c4_77", 0.996716025);
        put("c4_78", 0.996758603);
        put("c4_79", 0.99680009);
        put("c4_80", 0.996840529);
        put("c4_81", 0.996879959);
        put("c4_82", 0.996918416);
        put("c4_83", 0.996955938);
        put("c4_84", 0.996992556);
        put("c4_85", 0.997028304);
        put("c4_86", 0.997063212);
        put("c4_87", 0.99709731);
        put("c4_88", 0.997130625);
        put("c4_89", 0.997163183);
        put("c4_90", 0.997195012);
        put("c4_91", 0.997226134);
        put("c4_92", 0.997256573);
        put("c4_93", 0.997286351);
        put("c4_94", 0.99731549);
        put("c4_95", 0.997344009);
        put("c4_96", 0.997371929);
        put("c4_97", 0.997399268);
        put("c4_98", 0.997426044);
        put("c4_99", 0.997452275);
        put("c4_100", 0.997477976);
        /**
         * d2
         */
        put("d2_1", 1.128);
        put("d2_2", 1.128);
        put("d2_3", 1.693);
        put("d2_4", 2.059);
        put("d2_5", 2.326);
        put("d2_6", 2.534);
        put("d2_7", 2.704);
        put("d2_8", 2.847);
        put("d2_9", 2.97);
        put("d2_10", 3.078);
        put("d2_11", 3.173);
        put("d2_12", 3.258);
        put("d2_13", 3.336);
        put("d2_14", 3.407);
        put("d2_15", 3.472);
        put("d2_16", 3.532);
        put("d2_17", 3.588);
        put("d2_18", 3.64);
        put("d2_19", 3.689);
        put("d2_20", 3.735);
        put("d2_21", 3.778);
        put("d2_22", 3.819);
        put("d2_23", 3.858);
        put("d2_24", 3.895);
        put("d2_25", 3.931);
        put("d2_26", 3.964);
        put("d2_27", 3.997);
        put("d2_28", 4.027);
        put("d2_29", 4.057);
        put("d2_30", 4.086);
        put("d2_31", 4.113);
        put("d2_32", 4.139);
        put("d2_33", 4.165);
        put("d2_34", 4.189);
        put("d2_35", 4.213);
        put("d2_36", 4.236);
        put("d2_37", 4.259);
        put("d2_38", 4.28);
        put("d2_39", 4.301);
        put("d2_40", 4.322);
        put("d2_41", 4.341);
        put("d2_42", 4.361);
        put("d2_43", 4.379);
        put("d2_44", 4.398);
        put("d2_45", 4.415);
        put("d2_46", 4.433);
        put("d2_47", 4.45);
        put("d2_48", 4.466);
        put("d2_49", 4.482);
        put("d2_50", 4.498);
        put("d2_51", 4.50752287);
        put("d2_52", 4.52241928);
        put("d2_53", 4.53711923);
        put("d2_54", 4.55162272);
        put("d2_55", 4.56592975);
        put("d2_56", 4.58004032);
        put("d2_57", 4.59395443);
        put("d2_58", 4.60767208);
        put("d2_59", 4.62119327);
        put("d2_60", 4.634518);
        put("d2_61", 4.64764627);
        put("d2_62", 4.66057808);
        put("d2_63", 4.67331343);
        put("d2_64", 4.68585232);
        put("d2_65", 4.69819475);
        put("d2_66", 4.71034072);
        put("d2_67", 4.72229023);
        put("d2_68", 4.73404328);
        put("d2_69", 4.74559987);
        put("d2_70", 4.75696);
        put("d2_71", 4.76812367);
        put("d2_72", 4.77909088);
        put("d2_73", 4.78986163);
        put("d2_74", 4.80043592);
        put("d2_75", 4.81081375);
        put("d2_76", 4.82099512);
        put("d2_77", 4.83098003);
        put("d2_78", 4.84076848);
        put("d2_79", 4.85036047);
        put("d2_80", 4.859756);
        put("d2_81", 4.86895507);
        put("d2_82", 4.87795768);
        put("d2_83", 4.88676383);
        put("d2_84", 4.89537352);
        put("d2_85", 4.90378675);
        put("d2_86", 4.91200352);
        put("d2_87", 4.92002383);
        put("d2_88", 4.92784768);
        put("d2_89", 4.93547507);
        put("d2_90", 4.942906);
        put("d2_91", 4.95014047);
        put("d2_92", 4.95717848);
        put("d2_93", 4.96402003);
        put("d2_94", 4.97066512);
        put("d2_95", 4.97711375);
        put("d2_96", 4.98336592);
        put("d2_97", 4.98942163);
        put("d2_98", 4.99528088);
        put("d2_99", 5.00094367);
        put("d2_100", 5.00641);
        /**
         * d3
         */
        put("d3_1", 0.8525);
        put("d3_2", 0.8525);
        put("d3_3", 0.8884);
        put("d3_4", 0.8798);
        put("d3_5", 0.8641);
        put("d3_6", 0.848);
        put("d3_7", 0.8332);
        put("d3_8", 0.8198);
        put("d3_9", 0.8078);
        put("d3_10", 0.7971);
        put("d3_11", 0.7873);
        put("d3_12", 0.7785);
        put("d3_13", 0.7704);
        put("d3_14", 0.763);
        put("d3_15", 0.7562);
        put("d3_16", 0.7499);
        put("d3_17", 0.7441);
        put("d3_18", 0.7386);
        put("d3_19", 0.7335);
        put("d3_20", 0.7287);
        put("d3_21", 0.7242);
        put("d3_22", 0.7199);
        put("d3_23", 0.7159);
        put("d3_24", 0.7121);
        put("d3_25", 0.7084);
        put("d3_26", 0.70444104);
        put("d3_27", 0.70155565);
        put("d3_28", 0.69874144);
        put("d3_29", 0.69599727);
        put("d3_30", 0.693322);
        put("d3_31", 0.69071449);
        put("d3_32", 0.6881736);
        put("d3_33", 0.68569819);
        put("d3_34", 0.68328712);
        put("d3_35", 0.68093925);
        put("d3_36", 0.67865344);
        put("d3_37", 0.67642855);
        put("d3_38", 0.67426344);
        put("d3_39", 0.67215697);
        put("d3_40", 0.670108);
        put("d3_41", 0.66811539);
        put("d3_42", 0.666178);
        put("d3_43", 0.66429469);
        put("d3_44", 0.66246432);
        put("d3_45", 0.66068575);
        put("d3_46", 0.65895784);
        put("d3_47", 0.65727945);
        put("d3_48", 0.65564944);
        put("d3_49", 0.65406667);
        put("d3_50", 0.65253);
        put("d3_51", 0.65103829);
        put("d3_52", 0.6495904);
        put("d3_53", 0.64818519);
        put("d3_54", 0.64682152);
        put("d3_55", 0.64549825);
        put("d3_56", 0.64421424);
        put("d3_57", 0.64296835);
        put("d3_58", 0.64175944);
        put("d3_59", 0.64058637);
        put("d3_60", 0.639448);
        put("d3_61", 0.63834319);
        put("d3_62", 0.6372708);
        put("d3_63", 0.63622969);
        put("d3_64", 0.63521872);
        put("d3_65", 0.63423675);
        put("d3_66", 0.63328264);
        put("d3_67", 0.63235525);
        put("d3_68", 0.63145344);
        put("d3_69", 0.63057607);
        put("d3_70", 0.629722);
        put("d3_71", 0.62889009);
        put("d3_72", 0.6280792);
        put("d3_73", 0.62728819);
        put("d3_74", 0.62651592);
        put("d3_75", 0.62576125);
        put("d3_76", 0.62502304);
        put("d3_77", 0.62430015);
        put("d3_78", 0.62359144);
        put("d3_79", 0.62289577);
        put("d3_80", 0.622212);
        put("d3_81", 0.62153899);
        put("d3_82", 0.6208756);
        put("d3_83", 0.62022069);
        put("d3_84", 0.61957312);
        put("d3_85", 0.61893175);
        put("d3_86", 0.61829544);
        put("d3_87", 0.61766305);
        put("d3_88", 0.61703344);
        put("d3_89", 0.61640547);
        put("d3_90", 0.615778);
        put("d3_91", 0.61514989);
        put("d3_92", 0.61452);
        put("d3_93", 0.61388719);
        put("d3_94", 0.61325032);
        put("d3_95", 0.61260825);
        put("d3_96", 0.61195984);
        put("d3_97", 0.61130395);
        put("d3_98", 0.61063944);
        put("d3_99", 0.60996517);
        put("d3_100", 0.60928);
        /**
         * B3
         */
        put("B3_1", 0d);
        put("B3_2", 0d);
        put("B3_3", 0d);
        put("B3_4", 0d);
        put("B3_5", 0d);
        put("B3_6", 0.030363d);
        put("B3_7", 0.117685d);
        put("B3_8", 0.18509d);
        put("B3_9", 0.239133d);
        put("B3_10", 0.283705556);
        put("B3_11", 0.32128015);
        put("B3_12", 0.353511831);
        put("B3_13", 0.381555696);
        put("B3_14", 0.406245385);
        put("B3_15", 0.428199542);
        put("B3_16", 0.447888164);
        put("B3_17", 0.465675542);
        put("B3_18", 0.481848963);
        put("B3_19", 0.49663844);
        put("B3_20", 0.510230589);
        put("B3_21", 0.522778617);
        put("B3_22", 0.534409632);
        put("B3_23", 0.545230094);
        put("B3_24", 0.555329933);
        put("B3_25", 0.564785709);
        put("B3_26", 0.573663063);
        put("B3_27", 0.582018636);
        put("B3_28", 0.589901604);
        put("B3_29", 0.597354888);
        put("B3_30", 0.604416145);
        put("B3_31", 0.61111857);
        put("B3_32", 0.617491553);
        put("B3_33", 0.623561219);
        put("B3_34", 0.629350885);
        put("B3_35", 0.634881433);
        put("B3_36", 0.640171627);
        put("B3_37", 0.645238385);
        put("B3_38", 0.650097001);
        put("B3_39", 0.654761345);
        put("B3_40", 0.659244025);
        put("B3_41", 0.663556533);
        put("B3_42", 0.667709369);
        put("B3_43", 0.671712149);
        put("B3_44", 0.675573695);
        put("B3_45", 0.67930212);
        put("B3_46", 0.682904902);
        put("B3_47", 0.68638894);
        put("B3_48", 0.689760617);
        put("B3_49", 0.693025845);
        put("B3_50", 0.696190108);
        put("B3_51", 0.699258506);
        put("B3_52", 0.702235781);
        put("B3_53", 0.705126357);
        put("B3_54", 0.70793436);
        put("B3_55", 0.710663649);
        put("B3_56", 0.713317833);
        put("B3_57", 0.715900294);
        put("B3_58", 0.718414206);
        put("B3_59", 0.720862547);
        put("B3_60", 0.723248121);
        put("B3_61", 0.725573562);
        put("B3_62", 0.727841356);
        put("B3_63", 0.730053846);
        put("B3_64", 0.732213243);
        put("B3_65", 0.734321638);
        put("B3_66", 0.736381006);
        put("B3_67", 0.738393218);
        put("B3_68", 0.740360048);
        put("B3_69", 0.742283176);
        put("B3_70", 0.744164195);
        put("B3_71", 0.746004622);
        put("B3_72", 0.747805895);
        put("B3_73", 0.749569382);
        put("B3_74", 0.751296388);
        put("B3_75", 0.752988152);
        put("B3_76", 0.754645856);
        put("B3_77", 0.756270629);
        put("B3_78", 0.757863547);
        put("B3_79", 0.759425636);
        put("B3_80", 0.76095788);
        put("B3_81", 0.762461215);
        put("B3_82", 0.76393654);
        put("B3_83", 0.765384715);
        put("B3_84", 0.76680656);
        put("B3_85", 0.768202866);
        put("B3_86", 0.769574387);
        put("B3_87", 0.770921847);
        put("B3_88", 0.772245943);
        put("B3_89", 0.773547342);
        put("B3_90", 0.774826685);
        put("B3_91", 0.776084587);
        put("B3_92", 0.777321642);
        put("B3_93", 0.778538418);
        put("B3_94", 0.779735464);
        put("B3_95", 0.780913307);
        put("B3_96", 0.782072456);
        put("B3_97", 0.783213399);
        put("B3_98", 0.784336608);
        put("B3_99", 0.785442538);
        put("B3_100", 0.786531627);
        /**
         * B4
         */
        put("B4_1", 0d);
        put("B4_2", 3.266532d);
        put("B4_3", 2.56817d);
        put("B4_4", 2.266047d);
        put("B4_5", 2.088998d);
        put("B4_6", 1.969637d);
        put("B4_7", 1.882315d);
        put("B4_8", 1.81491d);
        put("B4_9", 1.760867d);
        put("B4_10", 1.716294444);
        put("B4_11", 1.67871985);
        put("B4_12", 1.646488169);
        put("B4_13", 1.618444304);
        put("B4_14", 1.593754615);
        put("B4_15", 1.571800458);
        put("B4_16", 1.552111836);
        put("B4_17", 1.534324458);
        put("B4_18", 1.518151037);
        put("B4_19", 1.50336156);
        put("B4_20", 1.489769411);
        put("B4_21", 1.477221383);
        put("B4_22", 1.465590368);
        put("B4_23", 1.454769906);
        put("B4_24", 1.444670067);
        put("B4_25", 1.435214291);
        put("B4_26", 1.426336937);
        put("B4_27", 1.417981364);
        put("B4_28", 1.410098396);
        put("B4_29", 1.402645112);
        put("B4_30", 1.395583855);
        put("B4_31", 1.38888143);
        put("B4_32", 1.382508447);
        put("B4_33", 1.376438781);
        put("B4_34", 1.370649115);
        put("B4_35", 1.365118567);
        put("B4_36", 1.359828373);
        put("B4_37", 1.354761615);
        put("B4_38", 1.349902999);
        put("B4_39", 1.345238655);
        put("B4_40", 1.340755975);
        put("B4_41", 1.336443467);
        put("B4_42", 1.332290631);
        put("B4_43", 1.328287851);
        put("B4_44", 1.324426305);
        put("B4_45", 1.32069788);
        put("B4_46", 1.317095098);
        put("B4_47", 1.31361106);
        put("B4_48", 1.310239383);
        put("B4_49", 1.306974155);
        put("B4_50", 1.303809892);
        put("B4_51", 1.300741494);
        put("B4_52", 1.297764219);
        put("B4_53", 1.294873643);
        put("B4_54", 1.29206564);
        put("B4_55", 1.289336351);
        put("B4_56", 1.286682167);
        put("B4_57", 1.284099706);
        put("B4_58", 1.281585794);
        put("B4_59", 1.279137453);
        put("B4_60", 1.276751879);
        put("B4_61", 1.274426438);
        put("B4_62", 1.272158644);
        put("B4_63", 1.269946154);
        put("B4_64", 1.267786757);
        put("B4_65", 1.265678362);
        put("B4_66", 1.263618994);
        put("B4_67", 1.261606782);
        put("B4_68", 1.259639952);
        put("B4_69", 1.257716824);
        put("B4_70", 1.255835805);
        put("B4_71", 1.253995378);
        put("B4_72", 1.252194105);
        put("B4_73", 1.250430618);
        put("B4_74", 1.248703612);
        put("B4_75", 1.247011848);
        put("B4_76", 1.245354144);
        put("B4_77", 1.243729371);
        put("B4_78", 1.242136453);
        put("B4_79", 1.240574364);
        put("B4_80", 1.23904212);
        put("B4_81", 1.237538785);
        put("B4_82", 1.23606346);
        put("B4_83", 1.234615285);
        put("B4_84", 1.23319344);
        put("B4_85", 1.231797134);
        put("B4_86", 1.230425613);
        put("B4_87", 1.229078153);
        put("B4_88", 1.227754057);
        put("B4_89", 1.226452658);
        put("B4_90", 1.225173315);
        put("B4_91", 1.223915413);
        put("B4_92", 1.222678358);
        put("B4_93", 1.221461582);
        put("B4_94", 1.220264536);
        put("B4_95", 1.219086693);
        put("B4_96", 1.217927544);
        put("B4_97", 1.216786601);
        put("B4_98", 1.215663392);
        put("B4_99", 1.214557462);
        put("B4_100", 1.213468373);
        /**
         * D3
         */
        put("D3_1", 0d);
        put("D3_2", 0d);
        put("D3_3", 0d);
        put("D3_4", 0d);
        put("D3_5", 0d);
        put("D3_6", 0d);
        put("D3_7", 0.075591716);
        put("D3_8", 0.136143309);
        put("D3_9", 0.184040404);
        put("D3_10", 0.223099415);
        put("D3_11", 0.255625591);
        put("D3_12", 0.283149171);
        put("D3_13", 0.307194245);
        put("D3_14", 0.328147931);
        put("D3_15", 0.346601382);
        put("D3_16", 0.363052095);
        put("D3_17", 0.377842809);
        put("D3_18", 0.391263736);
        put("D3_19", 0.403496883);
        put("D3_20", 0.414698795);
        put("D3_21", 0.424933827);
        put("D3_22", 0.434485467);
        put("D3_23", 0.443312597);
        put("D3_24", 0.451527599);
        put("D3_25", 0.459374205);
        put("D3_26", 0.46687106);
        put("D3_27", 0.473438341);
        put("D3_28", 0.479457581);
        put("D3_29", 0.485336009);
        put("D3_30", 0.49095301);
        put("D3_31", 0.496196579);
        put("D3_32", 0.501202996);
        put("D3_33", 0.506099743);
        put("D3_34", 0.510656157);
        put("D3_35", 0.515115654);
        put("D3_36", 0.519367252);
        put("D3_37", 0.523530019);
        put("D3_38", 0.527385439);
        put("D3_39", 0.531162309);
        put("D3_40", 0.534862564);
        put("D3_41", 0.538275473);
        put("D3_42", 0.541725751);
        put("D3_43", 0.544899733);
        put("D3_44", 0.548114379);
        put("D3_45", 0.551062911);
        put("D3_46", 0.55405515);
        put("D3_47", 0.556890258);
        put("D3_48", 0.5595727);
        put("D3_49", 0.562204371);
        put("D3_50", 0.564786572);
        put("D3_51", 0.566698844);
        put("D3_52", 0.569086571);
        put("D3_53", 0.571411843);
        put("D3_54", 0.573676317);
        put("D3_55", 0.575881615);
        put("D3_56", 0.578029322);
        put("D3_57", 0.580120987);
        put("D3_58", 0.58215813);
        put("D3_59", 0.584142234);
        put("D3_60", 0.586074755);
        put("D3_61", 0.587957117);
        put("D3_62", 0.589790715);
        put("D3_63", 0.591576919);
        put("D3_64", 0.593317068);
        put("D3_65", 0.595012478);
        put("D3_66", 0.596664438);
        put("D3_67", 0.598274215);
        put("D3_68", 0.59984305);
        put("D3_69", 0.601372163);
        put("D3_70", 0.602862753);
        put("D3_71", 0.604315995);
        put("D3_72", 0.605733047);
        put("D3_73", 0.607115045);
        put("D3_74", 0.608463108);
        put("D3_75", 0.609778335);
        put("D3_76", 0.611061809);
        put("D3_77", 0.612314595);
        put("D3_78", 0.613537741);
        put("D3_79", 0.614732282);
        put("D3_80", 0.615899234);
        put("D3_81", 0.617039602);
        put("D3_82", 0.618154375);
        put("D3_83", 0.619244528);
        put("D3_84", 0.620311024);
        put("D3_85", 0.621354813);
        put("D3_86", 0.622376834);
        put("D3_87", 0.623378013);
        put("D3_88", 0.624359266);
        put("D3_89", 0.625321497);
        put("D3_90", 0.626265602);
        put("D3_91", 0.627192464);
        put("D3_92", 0.628102961);
        put("D3_93", 0.628997958);
        put("D3_94", 0.629878313);
        put("D3_95", 0.630744877);
        put("D3_96", 0.631598492);
        put("D3_97", 0.632439993);
        put("D3_98", 0.633270208);
        put("D3_99", 0.634089958);
        put("D3_100", 0.634900058);
        /**
         * D4
         */
        put("D4_1", 3.267287234);
        put("D4_2", 3.267287234);
        put("D4_3", 2.574246899);
        put("D4_4", 2.28188441);
        put("D4_5", 2.114488392);
        put("D4_6", 2.00394633);
        put("D4_7", 1.924408284);
        put("D4_8", 1.863856691);
        put("D4_9", 1.815959596);
        put("D4_10", 1.776900585);
        put("D4_11", 1.744374409);
        put("D4_12", 1.716850829);
        put("D4_13", 1.692805755);
        put("D4_14", 1.671852069);
        put("D4_15", 1.653398618);
        put("D4_16", 1.636947905);
        put("D4_17", 1.622157191);
        put("D4_18", 1.608736264);
        put("D4_19", 1.596503117);
        put("D4_20", 1.585301205);
        put("D4_21", 1.575066173);
        put("D4_22", 1.565514533);
        put("D4_23", 1.556687403);
        put("D4_24", 1.548472401);
        put("D4_25", 1.540625795);
        put("D4_26", 1.53312894);
        put("D4_27", 1.526561659);
        put("D4_28", 1.520542419);
        put("D4_29", 1.514663991);
        put("D4_30", 1.50904699);
        put("D4_31", 1.503803421);
        put("D4_32", 1.498797004);
        put("D4_33", 1.493900257);
        put("D4_34", 1.489343843);
        put("D4_35", 1.484884346);
        put("D4_36", 1.480632748);
        put("D4_37", 1.476469981);
        put("D4_38", 1.472614561);
        put("D4_39", 1.468837691);
        put("D4_40", 1.465137436);
        put("D4_41", 1.461724527);
        put("D4_42", 1.458274249);
        put("D4_43", 1.455100267);
        put("D4_44", 1.451885621);
        put("D4_45", 1.448937089);
        put("D4_46", 1.44594485);
        put("D4_47", 1.443109742);
        put("D4_48", 1.4404273);
        put("D4_49", 1.437795629);
        put("D4_50", 1.435213428);
        put("D4_51", 1.433301156);
        put("D4_52", 1.430913429);
        put("D4_53", 1.428588157);
        put("D4_54", 1.426323683);
        put("D4_55", 1.424118385);
        put("D4_56", 1.421970678);
        put("D4_57", 1.419879013);
        put("D4_58", 1.41784187);
        put("D4_59", 1.415857766);
        put("D4_60", 1.413925245);
        put("D4_61", 1.412042883);
        put("D4_62", 1.410209285);
        put("D4_63", 1.408423081);
        put("D4_64", 1.406682932);
        put("D4_65", 1.404987522);
        put("D4_66", 1.403335562);
        put("D4_67", 1.401725785);
        put("D4_68", 1.40015695);
        put("D4_69", 1.398627837);
        put("D4_70", 1.397137247);
        put("D4_71", 1.395684005);
        put("D4_72", 1.394266953);
        put("D4_73", 1.392884955);
        put("D4_74", 1.391536892);
        put("D4_75", 1.390221665);
        put("D4_76", 1.388938191);
        put("D4_77", 1.387685405);
        put("D4_78", 1.386462259);
        put("D4_79", 1.385267718);
        put("D4_80", 1.384100766);
        put("D4_81", 1.382960398);
        put("D4_82", 1.381845625);
        put("D4_83", 1.380755472);
        put("D4_84", 1.379688976);
        put("D4_85", 1.378645187);
        put("D4_86", 1.377623166);
        put("D4_87", 1.376621987);
        put("D4_88", 1.375640734);
        put("D4_89", 1.374678503);
        put("D4_90", 1.373734398);
        put("D4_91", 1.372807536);
        put("D4_92", 1.371897039);
        put("D4_93", 1.371002042);
        put("D4_94", 1.370121687);
        put("D4_95", 1.369255123);
        put("D4_96", 1.368401508);
        put("D4_97", 1.367560007);
        put("D4_98", 1.366729792);
        put("D4_99", 1.365910042);
        put("D4_100", 1.365099942);
        /**
         * A2
         */
        put("A2_1", 2.659574468);
        put("A2_2", 1.880603141);
        put("A2_3", 1.023066041);
        put("A2_4", 0.728508985);
        put("A2_5", 0.576801714);
        put("A2_6", 0.483324732);
        put("A2_7", 0.419339282);
        put("A2_8", 0.372553625);
        put("A2_9", 0.336700337);
        put("A2_10", 0.308214197);
        put("A2_11", 0.285072182);
        put("A2_12", 0.265815041);
        put("A2_13", 0.249415556);
        put("A2_14", 0.235334231);
        put("A2_15", 0.223098119);
        put("A2_16", 0.212344281);
        put("A2_17", 0.202788984);
        put("A2_18", 0.194260105);
        put("A2_19", 0.186567417);
        put("A2_20", 0.179603854);
        put("A2_21", 0.173280485);
        put("A2_22", 0.16747896);
        put("A2_23", 0.162141846);
        put("A2_24", 0.157220138);
        put("A2_25", 0.152632918);
        put("A2_26", 0.148422908);
        put("A2_27", 0.144445902);
        put("A2_28", 0.140786369);
        put("A2_29", 0.137314768);
        put("A2_30", 0.134048595);
        put("A2_31", 0.131003138);
        put("A2_32", 0.128130004);
        put("A2_33", 0.125386067);
        put("A2_34", 0.122820663);
        put("A2_35", 0.120363768);
        put("A2_36", 0.118035883);
        put("A2_37", 0.115801118);
        put("A2_38", 0.113706604);
        put("A2_39", 0.111691342);
        put("A2_40", 0.109750497);
        put("A2_41", 0.107929345);
        put("A2_42", 0.106147684);
        put("A2_43", 0.104474928);
        put("A2_44", 0.102834701);
        put("A2_45", 0.101294133);
        put("A2_46", 0.099780255);
        put("A2_47", 0.098335949);
        put("A2_48", 0.096957614);
        put("A2_49", 0.095620578);
        put("A2_50", 0.094322825);
        put("A2_51", 0.093196205);
        put("A2_52", 0.091991724);
        put("A2_53", 0.090824523);
        put("A2_54", 0.089692911);
        put("A2_55", 0.0885953);
        put("A2_56", 0.087530204);
        put("A2_57", 0.086496223);
        put("A2_58", 0.085492043);
        put("A2_59", 0.084516425);
        put("A2_60", 0.083568202);
        put("A2_61", 0.082646272);
        put("A2_62", 0.081749597);
        put("A2_63", 0.080877193);
        put("A2_64", 0.08002813);
        put("A2_65", 0.079201528);
        put("A2_66", 0.078396552);
        put("A2_67", 0.077612412);
        put("A2_68", 0.076848355);
        put("A2_69", 0.076103668);
        put("A2_70", 0.075377675);
        put("A2_71", 0.074669728);
        put("A2_72", 0.073979215);
        put("A2_73", 0.07330555);
        put("A2_74", 0.072648177);
        put("A2_75", 0.072006563);
        put("A2_76", 0.071380201);
        put("A2_77", 0.070768607);
        put("A2_78", 0.070171319);
        put("A2_79", 0.069587894);
        put("A2_80", 0.069017909);
        put("A2_81", 0.068460959);
        put("A2_82", 0.067916657);
        put("A2_83", 0.067384632);
        put("A2_84", 0.066864527);
        put("A2_85", 0.066356003);
        put("A2_86", 0.065858731);
        put("A2_87", 0.065372399);
        put("A2_88", 0.064896704);
        put("A2_89", 0.064431359);
        put("A2_90", 0.063976083);
        put("A2_91", 0.063530611);
        put("A2_92", 0.063094686);
        put("A2_93", 0.062668061);
        put("A2_94", 0.062250497);
        put("A2_95", 0.061841766);
        put("A2_96", 0.061441649);
        put("A2_97", 0.061049932);
        put("A2_98", 0.060666411);
        put("A2_99", 0.06029089);
        put("A2_100", 0.059923178);
        /**
         * A3
         */
        put("A3_1", 0d);
        put("A3_2", 2.658680776);
        put("A3_3", 1.954410048);
        put("A3_4", 1.628102823);
        put("A3_5", 1.427299293);
        put("A3_6", 1.287128296);
        put("A3_7", 1.181916102);
        put("A3_8", 1.099095023);
        put("A3_9", 1.031660953);
        put("A3_10", 0.975350077);
        put("A3_11", 0.92739423);
        put("A3_12", 0.885905702);
        put("A3_13", 0.849546185);
        put("A3_14", 0.817336464);
        put("A3_15", 0.78854109);
        put("A3_16", 0.762595383);
        put("A3_17", 0.739057533);
        put("A3_18", 0.717576186);
        put("A3_19", 0.697867913);
        put("A3_20", 0.679701187);
        put("A3_21", 0.662884758);
        put("A3_22", 0.64725906);
        put("A3_23", 0.632689761);
        put("A3_24", 0.619062849);
        put("A3_25", 0.606280842);
        put("A3_26", 0.594259824);
        put("A3_27", 0.58292711);
        put("A3_28", 0.572219385);
        put("A3_29", 0.562081203);
        put("A3_30", 0.552463772);
        put("A3_31", 0.543323962);
        put("A3_32", 0.534623486);
        put("A3_33", 0.526328223);
        put("A3_34", 0.518407651);
        put("A3_35", 0.510834375);
        put("A3_36", 0.503583725);
        put("A3_37", 0.496633418);
        put("A3_38", 0.489963267);
        put("A3_39", 0.483554938);
        put("A3_40", 0.477391732);
        put("A3_41", 0.471458407);
        put("A3_42", 0.465741014);
        put("A3_43", 0.460226762);
        put("A3_44", 0.454903897);
        put("A3_45", 0.449761594);
        put("A3_46", 0.44478987);
        put("A3_47", 0.439979494);
        put("A3_48", 0.435321923);
        put("A3_49", 0.430809232);
        put("A3_50", 0.426434062);
        put("A3_51", 0.422189564);
        put("A3_52", 0.418069361);
        put("A3_53", 0.414067501);
        put("A3_54", 0.410178424);
        put("A3_55", 0.406396929);
        put("A3_56", 0.402718146);
        put("A3_57", 0.399137505);
        put("A3_58", 0.39565072);
        put("A3_59", 0.39225376);
        put("A3_60", 0.388942832);
        put("A3_61", 0.385714365);
        put("A3_62", 0.382564992);
        put("A3_63", 0.379491534);
        put("A3_64", 0.376490989);
        put("A3_65", 0.373560519);
        put("A3_66", 0.370697438);
        put("A3_67", 0.367899202);
        put("A3_68", 0.365163398);
        put("A3_69", 0.36248774);
        put("A3_70", 0.359870055);
        put("A3_71", 0.357308279);
        put("A3_72", 0.35480045);
        put("A3_73", 0.3523447);
        put("A3_74", 0.349939251);
        put("A3_75", 0.347582409);
        put("A3_76", 0.345272559);
        put("A3_77", 0.343008159);
        put("A3_78", 0.340787739);
        put("A3_79", 0.338609891);
        put("A3_80", 0.336473274);
        put("A3_81", 0.334376602);
        put("A3_82", 0.332318646);
        put("A3_83", 0.330298228);
        put("A3_84", 0.328314222);
        put("A3_85", 0.326365546);
        put("A3_86", 0.324451164);
        put("A3_87", 0.322570081);
        put("A3_88", 0.320721344);
        put("A3_89", 0.318904036);
        put("A3_90", 0.317117276);
        put("A3_91", 0.315360218);
        put("A3_92", 0.313632048);
        put("A3_93", 0.311931982);
        put("A3_94", 0.310259268);
        put("A3_95", 0.308613179);
        put("A3_96", 0.306993017);
        put("A3_97", 0.305398108);
        put("A3_98", 0.303827803);
        put("A3_99", 0.302281475);
        put("A3_100", 0.30075852);

    }};
    private static final String CONSTANT_C4 = "c4_";
    private static final String CONSTANT_D2 = "d2_";

    /**
     * 直方图，短期标准差
     * 纠偏常量计算
     *
     * @param n 样本量
     *          在计算Cp和Cpk时，样本量可能为小数，此时需要取前后两个c4或d2使用插值法计算出对应结果
     *          1. 样本量>9时，使用公式1   1. S/c4， S 所有子组标准差的平均值(用长期标准差算)
     *          2. 样本量<9时，使用公式2  2. R/d2  R 每个子组最大测试值减最小值 的差值 的相加的平均值
     *          注：c4和d2为纠偏系数，跟样本量有关。如果样本量为整数，可通过查表获得；如果样本量为小数，则通过插值法计算出来
     * @return -1代表传入的n样本量参数错误
     */
    public static Double getCorrectionData(double n) {
        if (n > 9 & n <= 100) {
            //使用c4
            if (n % 1 == 0) {
                //样本量是整数
                return correctionMap.get(CorrectionConstantUtil.CONSTANT_C4 + (int) n);
            } else {
                //样本量是小数，此时需要取前后两个c4使用插值法计算出对应结果
                int x = (int) n;
                double y = n % 1;
                return (correctionMap.get(CorrectionConstantUtil.CONSTANT_C4 + x) +
                        (correctionMap.get(CorrectionConstantUtil.CONSTANT_C4 + (x + 1)) -
                                correctionMap.get(CorrectionConstantUtil.CONSTANT_C4 + x)) * y);
            }
        } else if (n >= 1 & n <= 9) {
            //使用d2
            if (n % 1 == 0) {
                //样本量是整数
                return correctionMap.get(CorrectionConstantUtil.CONSTANT_D2 + (int) n);
            } else {
                //样本量是小数，此时需要取前后两个d2使用插值法计算出对应结果

                int x = (int) n;
                double y = n % 1;
                return (correctionMap.get(CorrectionConstantUtil.CONSTANT_D2 + x) +
                        (correctionMap.get(CorrectionConstantUtil.CONSTANT_D2 + (x + 1)) -
                                correctionMap.get(CorrectionConstantUtil.CONSTANT_D2 + x)) * y);
            }
        } else {
            return Double.parseDouble("-1");
        }
    }

    /**
     * 控制图
     * 控制限计算（基于数据库中控制限记录）
     * 纠偏常量计算
     *
     * @param n
     * @param constantName,从本类中定义的常量选取
     * @return
     */
    public static Double getCorrectionData(double n, CorrectionConstantEnum constantName) {
        if (n % 1 == 0) {
            //样本量是整数
            return correctionMap.get(constantName.getDescription() + (int) n);
        } else {
            //样本量是小数，此时需要取前后两个单元格使用插值法计算出对应结果

            int x = (int) n;
            double y = n % 1;
            return (correctionMap.get(constantName.getDescription() + x) +
                    (correctionMap.get(constantName.getDescription() + (x + 1)) -
                            correctionMap.get(constantName.getDescription() + x)) * y);
        }
    }

}
