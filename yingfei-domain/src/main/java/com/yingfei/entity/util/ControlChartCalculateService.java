package com.yingfei.entity.util;

import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.vo.DataPointVO;
import com.yingfei.entity.vo.SubgroupDataVO;

import java.util.List;

/**
 * 控制限计算（基于数据库中控制限记录）
 */
public interface ControlChartCalculateService {
    /**
     * 数据点
     */
    List<DataPointVO> dataPoint(List<SubgroupDataDTO> subgroupDataDTOList, ControlChartTypeEnum chartTypeEnum);


    /**
     * 控制限CL
     */
    Double controlLimitCL(ControlLimitDTO controlLimitDto);

    /**
     * 控制限UCL
     */
    Double controlLimitUCL(ControlLimitDTO controlLimitDto);

    /**
     * 控制限LCL
     */
    Double controlLimitLCL(ControlLimitDTO controlLimitDto);

    /**
     * 历史控制限CL
     */
    Double historyControlLimitCL(ControlLimitDTO controlLimitDto);

    /**
     * 历史控制限UCL
     */
    Double historyControlLimitUCL(ControlLimitDTO controlLimitDto);

    /**
     * 历史控制限LCL
     */
    Double historyControlLimitLCL(ControlLimitDTO controlLimitDto);

}
