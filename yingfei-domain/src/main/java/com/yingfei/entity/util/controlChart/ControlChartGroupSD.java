package com.yingfei.entity.util.controlChart;

import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.SGRP_VAL_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.enums.CorrectionConstantEnum;
import com.yingfei.entity.util.ControlChartCalculateService;
import com.yingfei.entity.util.CorrectionConstantUtil;
import com.yingfei.entity.vo.DataPointVO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 控制图分组类型-------->标准差
 * <p>
 * c4、B3和B4为纠偏常量，具体值跟样本量有关。参考《纠偏常量计算》
 */
@Service
public class ControlChartGroupSD implements ControlChartCalculateService {


    /**
     * 图类型为均值-标准差-------> 数据点计算公式为: 子组内实际值的标准差
     * 图类型为均值-标准差-极差件内-------> 数据点计算公式为: 子组内每个样本子测试均值的标准差
     * 图类型为均值-标准差-标准差件内-------> 数据点计算公式为: 子组内每个样本子测试均值标准差
     *
     * @param subgroupDataDTOList 子组测试数据,要按子组时间正序排列
     * @param chartTypeEnum       图类型
     */
    @Override
    public List<DataPointVO> dataPoint(List<SubgroupDataDTO> subgroupDataDTOList, ControlChartTypeEnum chartTypeEnum) {
        List<DataPointVO> list = new ArrayList<>();
        switch (chartTypeEnum) {
            case CONTROL_CHART_X_SD:
            case CONTROL_CHART_X_SD_RW:
            case CONTROL_CHART_X_SD_SDW:
                subgroupDataDTOList.forEach(subgroupDataDTO -> {
                    /*子测试均值在数据监控中同步为了实际值*/
                    list.add(DataPointVO.getDataPoint(subgroupDataDTO, subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getSd()));
                });
                break;
            default:
                break;
        }
        return list;
    }

    /**
     * F_SP*c4
     *
     * @return
     */
    @Override
    public Double controlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getF_SP() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_C4);
    }

    /**
     * CL * B4
     *
     * @return
     */
    @Override
    public Double controlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_B4);
    }

    /**
     * CL*B3
     */
    @Override
    public Double controlLimitLCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_B3);
    }

    /**
     * 历史控制限CL
     * R  R为所有历史子组极差的均值
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getR();
    }

    /**
     * 历史控制限UCL
     * CL*B4
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_B4);
    }

    /**
     * 历史控制限LCL
     * CL*B3
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitLCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_B3);
    }
}
