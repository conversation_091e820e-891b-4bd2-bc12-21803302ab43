package com.yingfei.entity.util;

import org.apache.commons.math3.distribution.NormalDistribution;

/**
 * <AUTHOR>
 * @createTime 2023-12-14 上午 11:18
 * @description 能力矩阵图计算方式
 */
public class CapabilityMatrixUtil {
    /**
     * Potential
     * (USL-ProcessMean)/SD(st)
     *
     * @param USL
     * @param processMean
     * @param shortTermStandardDeviation
     * @return
     */
    public static Double getZ_USL(Double USL, Double processMean, Double shortTermStandardDeviation) {
        if (USL == null || processMean == null || shortTermStandardDeviation == null || shortTermStandardDeviation == 0d) {
            return null;
        }
        return (USL - processMean) / shortTermStandardDeviation;
    }

    /**
     * Potential
     * (( ProcessMean -LSL)/SD(st))*-1
     *
     * @param LSL
     * @param processMean
     * @param shortTermStandardDeviation
     * @return
     */
    public static Double getZ_LSL(Double LSL, Double processMean, Double shortTermStandardDeviation) {
        if (LSL == null || processMean == null || shortTermStandardDeviation == null || shortTermStandardDeviation == 0d) {
            return null;
        }
        return ((processMean - LSL) / shortTermStandardDeviation) * -1;
    }

    /**
     * Potential
     * 1-NORM.DIST(ZUSL,O,1,TRUE)
     *
     * @param Z_USL
     * @return
     */
    public static Double getFractionGreaterThanUSL(Double Z_USL) {
        if (Z_USL == null) {
            return null;
        }
        NormalDistribution normalDistribution = new NormalDistribution();
        return 1 - normalDistribution.cumulativeProbability(Z_USL);
    }

    /**
     * Potential
     * NORM.DIST(ZLSL,O,1,TRUE)
     *
     * @param Z_LSL
     * @return
     */
    public static Double getFractionLessThanLSL(Double Z_LSL) {
        if (Z_LSL == null) {
            return null;
        }
        NormalDistribution normalDistribution = new NormalDistribution();
        return normalDistribution.cumulativeProbability(Z_LSL);
    }

    /**
     * Potential
     * Fraction < LSL * subgroupcount
     *
     * @param fraction_LessThan_LSL Fraction < LSL
     * @param subGroupCount         子组数量
     * @return
     */
    public static Double getWeightedFractionLessThanLSL(Double fraction_LessThan_LSL, Integer subGroupCount) {
        if (fraction_LessThan_LSL == null || subGroupCount == null) {
            return null;
        }
        return fraction_LessThan_LSL * subGroupCount;
    }

    /**
     * Potential
     * Fraction >USL * subgroupcount
     *
     * @param fraction_GreaterThan_USL Fraction >USL
     * @param subGroupCount
     * @return
     */
    public static Double getWeightedFractionGreaterThanUSL(Double fraction_GreaterThan_USL, Integer subGroupCount) {
        if (fraction_GreaterThan_USL == null || subGroupCount == null) {
            return null;
        }
        return fraction_GreaterThan_USL * subGroupCount;
    }

    /**
     * Potential
     * (Fraction < LSL+ Fracktion > USL) * 1000000
     *
     * @param fraction_GreaterThan_USL Fraction >USL
     * @param fraction_LessThan_LSL    Fraction < LSL
     * @return
     */
    public static Double getPDPM(Double fraction_GreaterThan_USL, Double fraction_LessThan_LSL) {
        if (fraction_GreaterThan_USL == null && fraction_LessThan_LSL == null) {
            return null;
        } else if (fraction_GreaterThan_USL == null) {
            return fraction_LessThan_LSL * 1000000;
        } else if (fraction_LessThan_LSL == null) {
            return fraction_GreaterThan_USL * 1000000;
        }
        return (fraction_GreaterThan_USL + fraction_LessThan_LSL) * 1000000;
    }

    /**
     * Potential
     * 100 -(Fraction > USL + Fraction < LSL) * 100
     *
     * @param fraction_GreaterThan_USL Fraction >USL
     * @param fraction_LessThan_LSL    Fraction < LSL
     * @return
     */
    public static Double getYield(Double fraction_GreaterThan_USL, Double fraction_LessThan_LSL) {
        if (fraction_GreaterThan_USL == null && fraction_LessThan_LSL == null) {
            return null;
        } else if (fraction_GreaterThan_USL == null) {
            return 100 - fraction_LessThan_LSL * 100;
        } else if (fraction_LessThan_LSL == null) {
            return 100 - fraction_GreaterThan_USL * 100;
        }
        return 100 - (fraction_GreaterThan_USL + fraction_LessThan_LSL) * 100;
    }

    /**
     * Potential(Centered Process)
     * TAR / SD(lt)
     *
     * @param TAR
     * @param longTermStandardDeviation
     * @return
     */
//    public static Double getSpecZ(Double TAR, Double longTermStandardDeviation) {
//        if (TAR == null || longTermStandardDeviation == null || longTermStandardDeviation == 0d) {
//            return null;
//        }
//        return TAR / longTermStandardDeviation;
//    }

    /**
     * Potential(Centered Process)
     * (USL-TAR) / shortTermStandardDeviation
     * (TAR - LSL) / shortTermStandardDeviation;
     *
     * @param TAR
     * @param shortTermStandardDeviation
     * @return
     */
    public static Double getSpecZ(Double USL, Double LSL, Double TAR, Double shortTermStandardDeviation) {
        if (USL == null && LSL == null) return null;
        if (TAR == null || shortTermStandardDeviation == 0d) {
            return null;
        }
        if (USL != null) return (USL - TAR) / shortTermStandardDeviation;
        else return (TAR - LSL) / shortTermStandardDeviation;
    }

    /**
     * Potential(Centered Process)
     * Fraction OOS
     * <p>
     * SD(lt) = 长期标准差
     * ZLSLTAR = (TAR-LSL)/SD(lt)
     * ZUSLTAR = (USL-TAR)/SD(lt)
     * <p>
     * FactionOOS =1+NORM.DIST(-ZLSLTAR,0,1,TRUE) - NORM.DIST(ZUSLTAR,0,1,TRUE)
     *
     * @param TAR                       目标值
     * @param USL                       上公差
     * @param LSL                       下公差
     * @param longTermStandardDeviation 长期标准差
     * @return
     */
    public static Double getFractionOOS(Double TAR, Double USL, Double LSL, Double longTermStandardDeviation) {
        if (TAR == null || USL == null || LSL == null || longTermStandardDeviation == null || longTermStandardDeviation == 0d) {
            return null;
        }
        Double ZUSL = (USL - TAR) / longTermStandardDeviation;
        Double ZLSL = (TAR - LSL) / longTermStandardDeviation;
        NormalDistribution normalDistribution1 = new NormalDistribution();
        NormalDistribution normalDistribution2 = new NormalDistribution();
        return 1 + normalDistribution1.cumulativeProbability(-ZLSL) - normalDistribution2.cumulativeProbability(ZUSL);
    }

    /**
     * Potential(Centered Process)
     * Weighted Fraction OOS
     * FactionOOS x 3
     *
     * @param fractionOOS
     * @return
     */
    public static Double getWeightedFractionOOS(Double fractionOOS) {
        if (fractionOOS == null) {
            return null;
        }
        return fractionOOS * 3;
    }

    /**
     * Potential(Centered Process)
     * PDPM
     * FactionOOSx1000000
     *
     * @param fractionOOS
     * @return
     */
    public static Double getPDPM(Double fractionOOS) {
        if (fractionOOS == null) {
            return null;
        }
        return fractionOOS * 1000000;
    }

    /**
     * Potential(Centered Process)
     * Yield
     * (1- FactionsOOS)X 100
     *
     * @param fractionOOS
     * @return
     */
    public static Double getYield(Double fractionOOS) {
        if (fractionOOS == null) {
            return null;
        }
        return (1 - fractionOOS) * 100;
    }

    /**
     * Expect
     * (USL-ProcessMean)/SD(lt)
     *
     * @param USL
     * @param processMean
     * @param longTermStandardDeviation
     * @return
     */
    public static Double getUSL_Z(Double USL, Double processMean, Double longTermStandardDeviation) {
        if (USL == null || processMean == null || longTermStandardDeviation == null || longTermStandardDeviation == 0d) {
            return null;
        }
        return (USL - processMean) / longTermStandardDeviation;
    }

    /**
     * Expect
     * (( ProcessMean -LSL)/SD(lt))*-1
     *
     * @param LSL
     * @param processMean
     * @param longTermStandardDeviation
     * @return
     */
    public static Double getLSL_Z(Double LSL, Double processMean, Double longTermStandardDeviation) {
        if (LSL == null || processMean == null || longTermStandardDeviation == null || longTermStandardDeviation == 0d) {
            return null;
        }
        return ((processMean - LSL) / longTermStandardDeviation) * -1;
    }

    /**
     * Expect
     * 1-NORM.DIST(USL_Z,O,1,TRUE)
     *
     * @param USL_Z
     * @return
     */
    public static Double getFraction_GreaterThan_USL(Double USL_Z) {
        if (USL_Z == null) {
            return null;
        }
        NormalDistribution normalDistribution = new NormalDistribution();
        return 1 - normalDistribution.cumulativeProbability(USL_Z);
    }

    /**
     * Expect
     * NORM.DIST(LSL_Z,O,1,TRUE)
     *
     * @param LSL_Z
     * @return
     */
    public static Double getFraction_LessThan_LSL(Double LSL_Z) {
        if (LSL_Z == null) {
            return null;
        }
        NormalDistribution normalDistribution = new NormalDistribution();
        return normalDistribution.cumulativeProbability(LSL_Z);
    }

    /**
     * Expect
     * Fraction < LSL * subgroupcount
     *
     * @param fraction_LessThan_LSL Fraction < LSL
     * @param subGroupCount         子组数量
     * @return
     */
    public static Double getWeightedFraction_LessThan_LSL(Double fraction_LessThan_LSL, Integer subGroupCount) {
        if (fraction_LessThan_LSL == null || subGroupCount == null) {
            return null;
        }
        return fraction_LessThan_LSL * subGroupCount;
    }

    /**
     * Expect
     * Fraction >USL * subgroupcount
     *
     * @param fraction_GreaterThan_USL Fraction >USL
     * @param subGroupCount
     * @return
     */
    public static Double getWeightedFraction_GreaterThan_USL(Double fraction_GreaterThan_USL, Integer subGroupCount) {
        if (fraction_GreaterThan_USL == null || subGroupCount == null) {
            return null;
        }
        return fraction_GreaterThan_USL * subGroupCount;
    }

    /**
     * Expect
     * (Fraction < LSL+ Fracktion > USL) * 1000000
     *
     * @param fraction_GreaterThan_USL Fraction >USL
     * @param fraction_LessThan_LSL    Fraction < LSL
     * @return
     */
    public static Double getDPM(Double fraction_GreaterThan_USL, Double fraction_LessThan_LSL) {
        if (fraction_GreaterThan_USL == null && fraction_LessThan_LSL == null) {
            return null;
        } else if (fraction_GreaterThan_USL == null) {
            return fraction_LessThan_LSL * 1000000;
        } else if (fraction_LessThan_LSL == null) {
            return fraction_GreaterThan_USL * 1000000;
        }
        return (fraction_GreaterThan_USL + fraction_LessThan_LSL) * 1000000;
    }

    /**
     * Expect
     * 100 -(Fraction > USL + Fraction < LSL) * 100
     *
     * @param fraction_GreaterThan_USL Fraction >USL
     * @param fraction_LessThan_LSL    Fraction < LSL
     * @return
     */
    public static Double getExpect_Yield(Double fraction_GreaterThan_USL, Double fraction_LessThan_LSL) {
        if (fraction_GreaterThan_USL == null && fraction_LessThan_LSL == null) {
            return null;
        } else if (fraction_GreaterThan_USL == null) {
            return 100 - fraction_LessThan_LSL * 100;
        } else if (fraction_LessThan_LSL == null) {
            return 100 - fraction_GreaterThan_USL * 100;
        }
        return 100 - (fraction_GreaterThan_USL + fraction_LessThan_LSL) * 100;
    }
}
