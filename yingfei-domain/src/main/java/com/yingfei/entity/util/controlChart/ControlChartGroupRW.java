package com.yingfei.entity.util.controlChart;

import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.SGRP_VAL_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.enums.CorrectionConstantEnum;
import com.yingfei.entity.util.CorrectionConstantUtil;
import com.yingfei.entity.vo.DataPointVO;
import com.yingfei.entity.util.ControlChartCalculateService;
import com.yingfei.entity.vo.SubgroupDataVO;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 控制图分组类型-------->极差件内图
 */
@Service
public class ControlChartGroupRW implements ControlChartCalculateService {


    /**
     * 图类型为单值-移动极差-极差件内-------> 数据点计算公式为: 子测试最大值-最小值
     * 图类型为均值-极差-极差件内-------> 数据点计算公式为: 子组内每个样本子测试极差的最大值
     * 图类型为均值-标准差-极差件内-------> 数据点计算公式为: 子组内每个样本子测试极差的最大值
     * 极差=最大值-最小值
     *
     * @param subgroupDataDTOList 子组测试数据,要按子组时间正序排列
     * @param chartTypeEnum       图类型
     */
    @Override
    public List<DataPointVO> dataPoint(List<SubgroupDataDTO> subgroupDataDTOList, ControlChartTypeEnum chartTypeEnum) {
        List<DataPointVO> list = new ArrayList<>();
        switch (chartTypeEnum) {
            case CONTROL_CHART_IX_MR_RW:
            case CONTROL_CHART_X_R_RW:
            case CONTROL_CHART_X_SD_RW:
                subgroupDataDTOList.forEach(subgroupDataDTO -> {
                    list.add(DataPointVO.getDataPoint(subgroupDataDTO, subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getWithinPieceRangeMax()));
                });
                break;
            default:
                break;
        }
        return list;
    }

    /**
     * F_SW*d2
     *
     * @return
     */
    @Override
    public Double controlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getF_SW() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_D2);
    }

    /**
     * CL*D4
     *
     * @return
     */
    @Override
    public Double controlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_D4);
    }

    /**
     * CL*D3
     */
    @Override
    public Double controlLimitLCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_D3_U);
    }

    /**
     * 历史控制限CL
     * Mean
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getMean();
    }

    /**
     * 历史控制限UCL
     * CL*D4
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_D4);
    }

    /**
     * 历史控制限LCL
     * CL*D3
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitLCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_D3_U);
    }
}
