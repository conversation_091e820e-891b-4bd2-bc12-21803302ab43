package com.yingfei.entity.util;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.utils.StringUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.math3.distribution.NormalDistribution;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class JohnSonConversionBaseUtil {
    private double gamma;
    private double epsilon;
    private double eta;
    private double lambda;
    private List<Double> values;
    private double correctuibFactor = 0.5;
    private double p;
    private double m;
    private double n;
    private double z;
    private double sampleValue1;
    private double sampleValue2;
    private double sampleValue3;
    private double sampleValue4;
    List<Double> transformValues;
    double qrValue;
    TransformedResult transformedResult;
    private String pythonScriptPath;

    public static final String pythonFileName = "johnson.py";
    public static final String pythonFileNameTwo = "Histogram.py";
    public static final String python = "python";

    public JohnSonConversionBaseUtil(List<Double> valueList, double z, String pythonScriptPath) {
        //升序排列
        values = valueList.stream().sorted(Comparator.comparing(Double::doubleValue)).collect(Collectors.toList());
        transformedResult = new TransformedResult();
        this.z = z;
        this.pythonScriptPath = pythonScriptPath;
    }

    public TransformedResult TransformData() {
        CalculateQRValue();
        if (Double.isNaN(qrValue)) return null;
        getDistributionType(qrValue);
        getInitParameter();
        //处理原始值
        getTranformedValues();
        if (transformValues != null) {
            TransformedResult result = new TransformedResult();
            result.setTransformedValues(transformValues);
            result.setZValue(z);
            result.setDistributionType(transformedResult.getDistributionType());
            return result;
        } else return null;
    }

    /// <summary>
    /// 计算QR,m,n,p的值，用于评估分布类型
    /// </summary>
    /// <returns></returns>
    private void CalculateQRValue() {
        //基于给定的z值计算4个点的累计概率
        //p-3z
        double p1 = getCumulativeProbability(-3 * z);
        //p-z
        double p2 = getCumulativeProbability(-1 * z);
        //p+z
        double p3 = getCumulativeProbability(z);
        //p+3z
        double p4 = getCumulativeProbability(3 * z);
        //计算4个累积概率分布对应的分位数
        double x1 = getEstimatePoint(p1);
        double x2 = getEstimatePoint(p2);
        double x3 = getEstimatePoint(p3);
        double x4 = getEstimatePoint(p4);
        //计算4个分位数对应的整数序号（向下取整）
        int index1 = (int) Math.floor(x1) - 1;
        int index2 = (int) Math.floor(x2) - 1;
        int index3 = (int) Math.floor(x3) - 1;
        int index4 = (int) Math.floor(x4) - 1;
        if (index1 < 0) index1 = 0;
        else if (index1 >= values.size() - 1) index1 = values.size() - 1;
        if (index2 < 0) index2 = 0;
        else if (index2 >= values.size() - 1) index2 = values.size() - 1;
        if (index3 < 0) index3 = 0;
        else if (index3 >= values.size() - 1) index3 = values.size() - 1;
        if (index4 < 0) index4 = 0;
        else if (index4 >= values.size() - 2) {
            index4 = values.size() - 2;
        }
        if (index1 + 1 >= values.size() || index2 + 1 >= values.size() || index3 + 1 >= values.size() || index4 + 1 >= values.size()) {
            qrValue = Double.NaN;
            return;
        }
        /*
         * 从原始数据list中取对应位置及后面位置的值，结合分位数和序号计算四个样本点
         */
        //x-3z
        sampleValue1 = values.get(index1) + (values.get(index1 + 1) - values.get(index1)) * (x1 - index1 - 1);
        //x-z
        sampleValue2 = values.get(index2) + (values.get(index2 + 1) - values.get(index2)) * (x2 - index2 - 1);
        //x+z
        sampleValue3 = values.get(index3) + (values.get(index3 + 1) - values.get(index3)) * (x3 - index3 - 1);
        //x+3z
        sampleValue4 = values.get(index4) + (values.get(index4 + 1) - values.get(index4)) * (x4 - index4 - 1);

        m = sampleValue4 - sampleValue3;
        n = sampleValue2 - sampleValue1;
        p = sampleValue3 - sampleValue2;
        if (p == 0) {
            qrValue = Double.NaN;
            return;
        } else qrValue = (m * n) / (p * p);
    }

    /// <summary>
    /// 使用johnson转换处理原始值和P值
    /// </summary>
    /// <returns></returns>
    private void getTranformedValues() {
        transformValues = new ArrayList<>();
        if (transformedResult.getDistributionType() == DistributionType.SB.ordinal()) {
            //SB
            for (Double val : values) {
                {
                    double v1 = val - epsilon;
                    double v2 = lambda + epsilon - val;
                    double v3 = gamma + eta * Math.log(v1 / v2);
                    if (!Double.isNaN(v3)) {
                        transformValues.add(v3);
                    } else {
                        transformValues = null;
                        return;
                    }
                }
            }
        } else if (transformedResult.getDistributionType() == DistributionType.SU.ordinal()) {
            //SU
            for (Double val : values) {
                {
                    double v1 = (val - epsilon) / lambda;
                    double v3 = gamma + eta * getArcSinh(v1);
                    if (!Double.isNaN(v3)) {
                        transformValues.add(v3);
                    } else {
                        transformValues = null;
                        return;
                    }
                }

            }
        } else {
            //SL
            for (Double val : values) {
                double v3 = gamma + eta * Math.log(val - epsilon);
                if (!Double.isNaN(v3)) {
                    transformValues.add(v3);
                } else {
                    transformValues = null;
                    return;
                }
            }
        }

    }

    /// <summary>
    /// 计算指定值的累积概率分布
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private double getCumulativeProbability(double val) {
        NormalDistribution normalDistribution = new NormalDistribution();
        return normalDistribution.cumulativeProbability(val);
    }

    /// <summary>
    /// 计算指定累积概率分布的分位数
    /// </summary>
    /// <param name="p"></param>
    /// <returns></returns>
    private double getEstimatePoint(double p) {
        return values.size() * p + correctuibFactor;
    }

    /// <summary>
    /// 根据QR值计算分布类型
    /// </summary>
    /// <param name="QRValue"></param>
    private void getDistributionType(double QRValue) {
            /*
              参考资料：https://zhuanlan.zhihu.com/p/26869997
            */

        if (QRValue < 1) {
            //SB分布
            transformedResult.setDistributionType(DistributionType.SB.ordinal());

        } else if (QRValue > 1) {
            //SU分布
            transformedResult.setDistributionType(DistributionType.SU.ordinal());
        } else {
            //SL分布
            transformedResult.setDistributionType(DistributionType.SL.ordinal());
        }
    }

    /// <summary>
    /// 计算η，γ，λ和ε的值
    /// </summary>
    private void getInitParameter() {
        //计算初始参数
        eta = getEtaValue(p, m, n, z);
        gamma = getGammaValue(p, m, n, eta);
        lambda = getLambdaValue(p, m, n);
        epsilon = getEpsilonValue(p, m, n, sampleValue3, sampleValue2, lambda);
    }

    /// <summary>
    /// 计算η的值
    /// </summary>
    /// <param name="type">0：SB分布；1：SU分布；2：SL分布</param>
    /// <param name="p"></param>
    /// <param name="m"></param>
    /// <param name="n"></param>
    /// <param name="z"></param>
    /// <returns></returns>
    private double getEtaValue(double p, double m, double n, double z) {
        if (transformedResult.getDistributionType() == DistributionType.SB.ordinal()) {

            //SB分布
            if (m == 0 || n == 0) return Double.NaN;
            //SQRT(0.5*(1+p/m)*(1+p/n))
            double v1 = Math.sqrt(0.5 * (1 + p / m) * (1 + p / n));
            //计算反双曲余弦 arcosh x= ln(x + Sqrt(x*x-1))
            double v2 = getArcCosh(v1);
            if (v2 == 0) return Double.NaN;
            else return z * (1.0 / v2);
        } else if (transformedResult.getDistributionType() == DistributionType.SU.ordinal()) {
            if (p == 0) return Double.NaN;
            //SU分布
            double v1 = 0.5 * (m / p + n / p);
            double v2 = getArcCosh(v1);
            if (v2 == 0) return Double.NaN;
            else return 2.0 * z * (1.0 / v2);
        } else {
            //SL分布
            if (p == 0) return Double.NaN;
            else {
                return (2 * z) / Math.log(m / p);
            }
        }
    }

    /// <summary>
    /// 计算γ值
    /// </summary>
    /// <param name="type">0：SB分布；1：SU分布；2：SL分布</param>
    /// <param name="p"></param>
    /// <param name="m"></param>
    /// <param name="n"></param>
    /// <param name="eta"></param>
    /// <returns></returns>
    private double getGammaValue(double p, double m, double n, double eta) {
        if (transformedResult.getDistributionType() == DistributionType.SB.ordinal()) {
            //SB分布
            if (n == 0 || m == 0 || p == 0) return Double.NaN;
            double v1 = (p / n - p / m);
            double v2 = Math.sqrt((1 + p / m) * (1 + p / n) - 4);
            double v3 = 2 / ((p * p) / (m * n) - 1);
            double v4 = getArcSinh(v1 * v2 * v3);
            return eta * v4;
        } else if (transformedResult.getDistributionType() == DistributionType.SU.ordinal()) {
            //SU
            if (m == 0 || p == 0 || n == 0) return Double.NaN;
            double v1 = n / p - m / p;
            double v2 = Math.sqrt((m * n) / (p * p) - 1);
            if (v2 == 0) return Double.NaN;
            double v3 = 1.0 / 2.0 * v2;
            double v4 = getArcSinh(v1 * v3);
            return eta * v4;
        } else {
            //SL
            if (m == 0 || p == 0 || p == 1) return Double.NaN;
            double v1 = m / (p - 1);
            double v2 = p * Math.sqrt(m / p);
            double v3 = eta * Math.log(v1 / v2);
            return v3;
        }
    }

    /// <summary>
    /// 计算λ的值（SL分布不用计算）
    /// </summary>
    /// <param name="p"></param>
    /// <param name="m"></param>
    /// <param name="n"></param>
    /// <returns></returns>
    private double getLambdaValue(double p, double m, double n) {
        if (transformedResult.getDistributionType() == DistributionType.SB.ordinal()) {
            if (m == 0 || n == 0) return Double.NaN;
            double v1 = Math.pow(((1 + p / m) * (1 + p / n) - 2), 2) - 4;
            double v2 = Math.sqrt(v1);
            double v3 = (p * p) / (m * n) - 1;
            if (v3 == 0) return Double.NaN;
            double v4 = p * v2 * (1 / v3);
            return v4;
        } else if (transformedResult.getDistributionType() == DistributionType.SU.ordinal()) {
            if (p == 0) return Double.NaN;
            double v1 = Math.sqrt((m * n) / (p * p) - 1);
            double v2 = Math.pow((m / p + n / p - 2) * (m / p + n / p + 2), -0.5);
            double v3 = 2 * p * v1 * v2;
            return v3;
        } else return Double.NaN;
    }

    /// <summary>
    /// 计算ε的值
    /// </summary>
    /// <param name="p"></param>
    /// <param name="m"></param>
    /// <param name="n"></param>
    /// <param name="sampleValue2">x+z</param>
    /// <param name="sampleValue3">x-z</param>
    /// <returns></returns>
    private double getEpsilonValue(double p, double m, double n, double sampleValue2, double sampleValue3,
                                   double lambda) {
        if (transformedResult.getDistributionType() == DistributionType.SB.ordinal()) {
            if (m == 0 || n == 0) return Double.NaN;
            //SB分布
            double v1 = (sampleValue2 + sampleValue3) / 2.0 - 0.5 * lambda;
            double v2 = p * (p / n - p / m);
            double v3 = 2.0 * ((p * p) / (m * n) - 1);
            if (v3 == 0) return Double.NaN;
            double v4 = 1.0 / v3;
            return v1 + v2 * v4;
        } else if (transformedResult.getDistributionType() == DistributionType.SU.ordinal()) {
            if (m == 0 || n == 0 || p == 0) return Double.NaN;
            //SU分布
            double v1 = (sampleValue2 + sampleValue3) / 2.0;
            double v2 = p * (n / p - m / p);
            double v3 = 2.0 * (m / p + n / p - 2);
            if (v3 == 0) return Double.NaN;
            double v4 = Math.pow(v3, -1);
            return v1 + v2 * v4;
        } else {
            if (p == 0 || m == p) return Double.NaN;
            //SL分布
            double v1 = (sampleValue2 + sampleValue3) / 2.0;
            double v2 = 0.5 * p * (m / p + 1) / (m / p - 1);
            return v1 + v2;
        }
    }

    /// <summary>
    /// 计算指定值的反双曲线余弦值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private double getArcCosh(double val) {
        return Math.log(val + Math.sqrt(val * val - 1));
    }

    /// <summary>
    /// 计算指定值的反双曲线正弦值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private double getArcSinh(double val) {
        return Math.log(val + Math.sqrt(val * val + 1));
    }

    public static void javaCallPython(List<TransformedResult> list, String pythonScriptPath, TransformedResult result) {
        String fileName = System.currentTimeMillis()+UUID.randomUUID().toString() + ".txt";
        String filePath = pythonScriptPath.replace(pythonFileName, fileName);
        try (BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(filePath))) {
            bufferedWriter.write(JSONObject.toJSONString(list));
        } catch (IOException e) {
            e.printStackTrace();
        }
        ProcessBuilder processBuilder = new ProcessBuilder(python, pythonScriptPath, filePath);
        processBuilder.redirectErrorStream(true);

        try {
            Process process = processBuilder.start();

            // 读取Python脚本的输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder s = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isEmpty(s.toString())) {
                    s.append(line);
                } else {
                    s.append(Constants.COMMA).append(line);
                }

            }

            // 等待Python脚本执行完成
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                System.out.println("Python script exited with error code: " + exitCode);
            }

            reader.close();
            if (StringUtils.isNotEmpty(s.toString()) && s.toString().contains("p=")) {
                String[] split = s.toString().split(Constants.COMMA);
                for (String value : split) {
                    if (value.contains("p=")) {
                        String[] pValue = value.split("p=");
                        if (StringUtils.isNumeric(pValue[1].trim())) {
                            result.setPValue(Double.parseDouble(pValue[1]));
                        }
                    } else if (value.contains("z=")) {
                        String[] zValue = value.split("z=");
                        if (StringUtils.isNumeric(zValue[1].trim())) {
                            result.setZValue(Double.parseDouble(zValue[1]));
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(result.getTransformedValues())) {
                Map<Double, TransformedResult> collect = list.stream().collect(Collectors.toMap(TransformedResult::getZValue, each -> each, (value1, value2) -> value1));
                TransformedResult t = collect.get(result.getZValue());
                if (t != null) {
                    result.setTransformedValues(t.getTransformedValues());
                    result.setDistributionType(t.getDistributionType());
                }
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static TransformedResult johnson(List<Double> list, Integer dataTransformType, String pythonScriptPath) {
        TransformedResult transformedResult = null;
        if (dataTransformType == 0) {
            transformedResult = new TransformedResult();
            transformedResult.setTransformedValues(list);
            JohnSonConversionBaseUtil.javaCallPython(Collections.singletonList(transformedResult), pythonScriptPath, transformedResult);
            return transformedResult;
        } else if (dataTransformType == 1) {
            double startZ = 0.25d;
            double intervalZ = 0.01d;
            List<TransformedResult> arrayList = new ArrayList<>();
            for (int i = 0; i < 100; i++) {
                double z = BigDecimal.valueOf(startZ).add(BigDecimal.valueOf(intervalZ).multiply(BigDecimal.valueOf(i))).doubleValue();
                JohnSonConversionBaseUtil johnSonConverionBase = new JohnSonConversionBaseUtil(list, z, pythonScriptPath);
                TransformedResult result = johnSonConverionBase.TransformData();
                if (result != null) {
                    arrayList.add(result);
                }
            }
            transformedResult = new TransformedResult();
            javaCallPython(arrayList, pythonScriptPath, transformedResult);
            return transformedResult;
        } else {
            return null;
        }
    }

    /**
     * 直方图计算拟合曲线和分组
     */
    public static Map<String, Object> Histogram(List<Double> list, String pythonScriptPath) {
        String fileName = System.currentTimeMillis()+UUID.randomUUID().toString() + ".txt";
        //   String filePath = pythonScriptPath.replace(pythonFileNameTwo, fileName);
        //获取文件所在路径
        Path path = Paths.get(pythonScriptPath);
        Path parent = path.getParent();
        String filePath = parent.resolve(fileName).toString();

        // 输出新文件地址
        System.out.println("新文件输出地址: " + filePath);

        try (BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(filePath))) {
            bufferedWriter.write(JSONObject.toJSONString(list));
        } catch (IOException e) {
            e.printStackTrace();
        }
//        ProcessBuilder processBuilder = new ProcessBuilder("C:/ProgramData/anaconda3/envs/spc/python", pythonScriptPath, filePath);
        ProcessBuilder processBuilder = new ProcessBuilder(python, pythonScriptPath, filePath);
        processBuilder.redirectErrorStream(true);
        Map<String, Object> map = new HashMap<>();
        try {
            Process process = processBuilder.start();

            // 读取Python脚本的输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder s = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isEmpty(s.toString())) {
                    s.append(line);
                } else {
                    s.append(Constants.COMMA).append(line);
                }

            }

            // 等待Python脚本执行完成
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                System.out.println("Python script exited with error code: " + exitCode);
            }

            reader.close();
            if (StringUtils.isNotEmpty(s.toString())) {
                String[] split = s.toString().split(Constants.SEPARATION);
                for (String value : split) {
                    if (value.contains("bins_custom=")) {
                        String[] binsCustom = value.split("bins_custom=");
                        List<BigDecimal> content = JSONArray.parseArray(binsCustom[1].trim(), BigDecimal.class);
                        map.put("groupDetail", content);
                    } else if (value.contains("counts=")) {
                        String[] counts = value.split("counts=");
                        List<String> content = JSONArray.parseArray(counts[1].trim(), String.class);
                        map.put("groupNum", content);
                    } else if (value.contains("points=")) {
                        String[] key = value.split("points=");
                        List<BigDecimal> content = JSONArray.parseArray(key[1].trim(), BigDecimal.class);
                        map.put("curveMapKey", content);
                    } else if (value.contains("values=")) {
                        String[] val = value.split("values=");
                        List<BigDecimal> content = JSONArray.parseArray(val[1].trim(), BigDecimal.class);
                        map.put("curveMapValue", content);
                    }else if (value.contains("AD=")) {
                        String[] val = value.split("AD=");
                        if(val.length>0)
                        {
                            for (String str : val) {
                                if(StringUtils.isNotEmpty(str.trim()))
                                {
                                    try
                                    {
                                        map.put("adValue", Double.parseDouble(str.trim()));
                                    }catch (Exception e)
                                    {
                                        System.out.println(e.getMessage());
                                    }
                                }
                            }
                        }
                    }else if (value.contains("pValue=")) {
                        String[] val = value.split("pValue=");
                        if(val.length>0) {
                            for (String str : val) {
                                if (StringUtils.isNotEmpty(str.trim())) {
                                    try
                                    {
                                        map.put("pValue", Double.parseDouble(str.trim()));
                                    }catch (Exception e)
                                    {
                                        System.out.println(e.getMessage());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return map;
    }


//    public static void main(String[] args) {
//        String fileName = System.currentTimeMillis() + ".txt";
//            // Windows 路径示例
//            String windowsPath = "C:\\Users\\<USER>\\Documents\\example.txt";
//            Path path = Paths.get(windowsPath);
//            Path parent = path.getParent();
//            String filePath = parent.resolve(fileName).toString();
//            System.out.println("Windows 父目录: " + filePath);
//
//            // Linux 路径示例
//                String linuxPath = "/home/<USER>/documents/example.txt";
//                Path path1 = Paths.get(linuxPath);
//                Path parent1 = path1.getParent();
//                String filePath1 = parent1.resolve(fileName).toString();
//
//            System.out.println("Linux 父目录: " + filePath1);
//    }
}



