package com.yingfei.entity.util;

import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.MultipartFileToFile;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.entity.dto.autoCollect.DataFileAutoCollectConfigDTO;
import com.yingfei.entity.dto.dataImport.DataColumnMappingDTO;
import com.yingfei.entity.enums.EvaluationTypeEnum;
import com.yingfei.entity.enums.ScreenConditionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.LineIterator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 解析文件工具类
 */
@Slf4j
public class ParseDataFileUtil {

    /**
     * 读取导入文件
     *
     * @param dataFileAutoCollectConfigDTO
     * @param multipartFile
     * @return
     */
    public static List<List<String>> getDataColumn(DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, MultipartFile multipartFile) {
        if (StringUtils.isEmpty(dataFileAutoCollectConfigDTO.getF_PLNT())) {
            throw new BusinessException(DataManagementExceptionEnum.PLEASE_SELECT_FACTORY);
        }
        Long startTime = System.currentTimeMillis();
        String suffix = multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1);
        List<List<String>> list = new ArrayList<>();
        if (!suffix.equals("csv") && !suffix.equals("txt") && !suffix.equals("xlsx")) {
            throw new BusinessException(CommonExceptionEnum.THE_IMPORTED_FILE_TYPE_IS_NOT_SUPPORTED);
        }
        if (suffix.equals("xlsx")) {
            try (InputStream inputStream = multipartFile.getInputStream()) {
                Workbook workbook = new XSSFWorkbook(inputStream);
                Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
                int i = 1;
                for (Row row : sheet) {
                    if (i < dataFileAutoCollectConfigDTO.getStartLine()) {
                        i++;
                        continue;
                    }
                    List<String> line = new ArrayList<>();
                    for (Cell cell : row) {
                        String name = cell.toString();// 打印单元格内容
                        /*处理行排除逻辑*/
                        if (skipLine(dataFileAutoCollectConfigDTO, name)) {
                            continue;
                        }
                        line.add(name);
                    }
                    if (CollectionUtils.isNotEmpty(line))
                        list.add(line);
                    i++;
                }
            } catch (Exception e) {
                log.error("读取xlsx失败");
                e.printStackTrace();
            }
        } else {
            LineIterator lineIterator = null;
            File file = null;
            try {
                file = MultipartFileToFile.multipartFileToFile(multipartFile);
                lineIterator = FileUtils.lineIterator(file, dataFileAutoCollectConfigDTO.getCharsetName());
                readTxtOrCsv(dataFileAutoCollectConfigDTO, list, lineIterator);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                IOUtils.closeQuietly(lineIterator);
                if (file != null)
                    MultipartFileToFile.delteTempFile(file);
            }
        }
        Long endTime = System.currentTimeMillis();
        log.info("解析耗时--------->{}", endTime - startTime);
        return list;
    }

    /**
     * 读取txt和csv
     *
     * @param dataFileAutoCollectConfigDTO
     * @param list
     * @param lineIterator
     */
    private static void readTxtOrCsv(DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, List<List<String>> list, LineIterator lineIterator) {
        int i = 1;
        while (lineIterator.hasNext()) {
            if (i < dataFileAutoCollectConfigDTO.getStartLine()) {
                String s = lineIterator.nextLine();
                i++;
                continue;
            }
            /*能够自动处理不同操作系统的换行符（\r\n、\n 或 \r）*/
            String line = lineIterator.nextLine();
            if (StringUtils.isEmpty(line)) continue;
            if (StringUtils.isEmpty(dataFileAutoCollectConfigDTO.getLineSeparator())) {
                if (StringUtils.isEmpty(line)) continue;
                /*处理行排除逻辑*/
                if (skipLine(dataFileAutoCollectConfigDTO, line)) {
                    continue;
                }
                String[] split = line.split(dataFileAutoCollectConfigDTO.getColumnSeparator(),-1);
                List<String> dataLine = Arrays.asList(split);
                if (dataExclude(dataFileAutoCollectConfigDTO, dataLine)) continue;
                if (CollectionUtils.isNotEmpty(dataLine))
                    list.add(dataLine);
                i++;
            } else {
                /*按手动指定换行符处理*/
                String[] lineList = line.split(dataFileAutoCollectConfigDTO.getLineSeparator(),-1);
                for (String s : lineList) {
                    if (i < dataFileAutoCollectConfigDTO.getStartLine()) {
                        i++;
                        continue;
                    }
                    /*处理行排除逻辑*/
                    if (skipLine(dataFileAutoCollectConfigDTO, line)) {
                        continue;
                    }
                    String[] split = s.split(dataFileAutoCollectConfigDTO.getColumnSeparator(),-1);
                    List<String> dataLine = Arrays.asList(split);
                    if (dataExclude(dataFileAutoCollectConfigDTO, dataLine)) continue;
                    if (CollectionUtils.isNotEmpty(dataLine))
                        list.add(dataLine);
                    i++;
                }
            }
        }
    }

    /**
     * 内部读取文件
     *
     * @param dataFileAutoCollectConfigDTO
     * @param file
     * @return
     */
    public static List<List<String>> getDataColumn(DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, File file) {
        if (StringUtils.isEmpty(dataFileAutoCollectConfigDTO.getF_PLNT()) && dataFileAutoCollectConfigDTO.getCollectType() == 1) {
            throw new BusinessException(DataManagementExceptionEnum.PLEASE_SELECT_FACTORY);
        }
        Long startTime = System.currentTimeMillis();
        String suffix = file.getName().substring(file.getName().lastIndexOf(".") + 1);
        if (!suffix.equals("csv") && !suffix.equals("txt") && !suffix.equals("xlsx")) {
            throw new BusinessException(CommonExceptionEnum.THE_IMPORTED_FILE_TYPE_IS_NOT_SUPPORTED);
        }
        List<List<String>> list = new ArrayList<>();
        if (suffix.equals("xlsx")) {
            try (InputStream inputStream = new FileInputStream(file)) {
                Workbook workbook = new XSSFWorkbook(inputStream);
                Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
                int i = 1;
                for (Row row : sheet) {
                    if (i < dataFileAutoCollectConfigDTO.getStartLine()) {
                        i++;
                        continue;
                    }
                    List<String> line = new ArrayList<>();
                    for (Cell cell : row) {
                        String name = cell.toString();// 打印单元格内容
                        /*处理行排除逻辑*/
                        if (skipLine(dataFileAutoCollectConfigDTO, name)) {
                            continue;
                        }
                        line.add(name);
                    }
                    if (CollectionUtils.isNotEmpty(line))
                        list.add(line);
                    i++;
                }
            } catch (Exception e) {
                log.error("读取xlsx失败");
                e.printStackTrace();
            }
        } else {
            LineIterator lineIterator = null;
            try {
                lineIterator = FileUtils.lineIterator(file, dataFileAutoCollectConfigDTO.getCharsetName());
                readTxtOrCsv(dataFileAutoCollectConfigDTO, list, lineIterator);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                IOUtils.closeQuietly(lineIterator);
            }
        }
        Long endTime = System.currentTimeMillis();
        log.info("解析耗时--------->{}", endTime - startTime);
        return list;
    }

    /**
     * 是否跳过该行
     *
     * @param dataFileAutoCollectConfigDTO 映射配置
     * @param line                         行
     */
    public static boolean skipLine(DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, String line) {
        boolean b = false;
        if (dataFileAutoCollectConfigDTO.getSkipCondition() == null)
            return b;
        switch (EvaluationTypeEnum.getType(dataFileAutoCollectConfigDTO.getSkipCondition())) {
            case START:
                if (line.startsWith(dataFileAutoCollectConfigDTO.getSkipValue())) {
                    b = true;
                }
                break;
            case END:
                if (line.endsWith(dataFileAutoCollectConfigDTO.getSkipValue())) {
                    b = true;
                }
                break;
            case CONTAIN:
                if (line.contains(dataFileAutoCollectConfigDTO.getSkipValue())) {
                    b = true;
                }
                break;
        }
        return b;
    }

    /**
     * 数据排除
     */
    public static boolean dataExclude(DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, List<String> line) {
        boolean isExclude = false;
        for (DataColumnMappingDTO.AnalyticRule analyticRule : dataFileAutoCollectConfigDTO.getAnalyticRuleList()) {
            int index = analyticRule.getNumber() - 1;

            // 检查索引是否越界
            if (index < 0 || index >= line.size()) {
                System.err.println("Invalid index: " + index + ", line size: " + line.size());
                continue; // 跳过当前规则，处理下一个规则
            }

            String s = line.get(index);
            String value = ParseDataFileUtil.truncateAndDefault(
                    analyticRule.getDefaultValue(),
                    analyticRule.getStartPosition(),
                    analyticRule.getTruncationLength(),
                    analyticRule.getIsTruncation(),
                    s
            );

            /*判断排除条件*/
            if (ParseDataFileUtil.evaluation(dataFileAutoCollectConfigDTO, analyticRule.getNumber(), value)) {
                isExclude = true;
                break;
            }

            if (!s.equals(value)) {
                line.set(index, value);
            }
        }
        return isExclude;
    }

    /**
     * 字段评估
     */
    public static boolean evaluation(DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, Integer fileType, String value) {
        boolean b = false;
        if (MapUtils.isEmpty(dataFileAutoCollectConfigDTO.getExcludeMap())) return b;
        DataFileAutoCollectConfigDTO.ExcludeCondition excludeCondition = dataFileAutoCollectConfigDTO.getExcludeMap().get(String.valueOf(fileType));
        if (excludeCondition == null) return b;
        switch (ScreenConditionEnum.getType(excludeCondition.getScreenCondition())) {
            case VALUE:
                b = isB(value, b, excludeCondition.getEvaluationType(), excludeCondition.getEvaluationValue());
                /*与上面是且关系 不满足改为false*/
                if (excludeCondition.getEvaluationTypeTwo() != null) {
                    isB(value, b, excludeCondition.getEvaluationTypeTwo(), excludeCondition.getEvaluationValueTwo());
                }
                break;
            case NULL:
                if (value == null) b = true;
                break;
            case NOT_NUMBER:
                if (!StringUtils.isNumeric(value)) b = true;
                break;
            case NOT_DATE:
                try {
                    DateUtils.parseDate(value);
                } catch (Exception e) {
                    b = true;
                }
                break;
            case LENGTH:
                if (value.length() == excludeCondition.getLength()) b = true;
                break;
        }
        return b;
    }

    /**
     * 映射列评估排除
     *
     * @param value           实际读取值
     * @param b               判断条件
     * @param evaluationType  评估类型
     * @param evaluationValue 评估值
     * @return
     */
    private static boolean isB(String value, boolean b, Integer evaluationType, String evaluationValue) {
        switch (EvaluationTypeEnum.getType(evaluationType)) {
            case LESS:
                b = Double.parseDouble(value) < Double.parseDouble(evaluationValue);
                break;
            case LESS_OR_EQUAL:
                b = Double.parseDouble(value) <= Double.parseDouble(evaluationValue);
                break;
            case EQUAL:
                b = Double.parseDouble(value) == Double.parseDouble(evaluationValue);
                break;
            case NOT_EQUAL:
                b = Double.parseDouble(value) != Double.parseDouble(evaluationValue);
                break;
            case GREATER_OR_EQUAL:
                b = Double.parseDouble(value) >= Double.parseDouble(evaluationValue);
                break;
            case GREATER:
                if (Double.parseDouble(value) > Double.parseDouble(evaluationValue)) {
                    b = true;
                }
                break;
            case START:
                b = value.startsWith(evaluationValue);
                break;
            case END:
                b = value.endsWith(evaluationValue);
                break;
            case CONTAIN:
                b = value.contains(evaluationValue);
                break;
        }
        return b;
    }

    /**
     * 字段截断和空默认
     */
    public static String truncateAndDefault(String defaultValue, Integer startPosition, Integer truncationLength, boolean isTruncation, String value) {
        if (StringUtils.isEmpty(value) && defaultValue != null) {
            return defaultValue;
        }
        if (isTruncation) {
            if (startPosition > 0) {
                startPosition = startPosition - 1;
            }

            if ((startPosition + truncationLength) > value.length()) {
                truncationLength = value.length();
            } else {
                truncationLength = startPosition + truncationLength;
            }
            return value.substring(Math.min(startPosition,
                    value.length()), truncationLength);
        }
        return value;
    }

    /**
     * 解析项字段
     *
     * @param type 1:指定数据文件映射(从解析规则选取) 2:从数据库选择或输入的名称
     * @return
     */
    public static String analyzeField(Integer type, List<DataColumnMappingDTO.AnalyticRule> analyticRuleList, Integer column, String columnName, List<String> dataLine) {
        // 处理参数为空的情况
        if (type == null) {
            return columnName;
        }
        String value;
        if (type == 1) {
            if (CollectionUtils.isNotEmpty(analyticRuleList)) {
                StringBuilder sb = new StringBuilder();
                for (DataColumnMappingDTO.AnalyticRule analyticRule : analyticRuleList) {
                    if (StringUtils.isNotEmpty(analyticRule.getConnector())) {
                        sb.append(analyticRule.getConnector());
                    }
                    // 添加数组越界检查
                    int index = analyticRule.getNumber() - 1;
                    if (index >= 0 && index < dataLine.size()) {
                        sb.append(dataLine.get(index));
                    }
                }
                value = sb.toString();
            } else {
                // 添加数组越界检查
                if (column != null && column > 0 && column - 1 < dataLine.size()) {
                    value = dataLine.get(column - 1);
                } else {
                    value = "";
                }
            }
        } else {
            value = columnName;
        }
        return StringUtils.isNotBlank(value) ? value.trim() : null;
    }


    /**
     * 获取保存条件
     *
     * @param type                         任务类型(1:数据文件  2:数据库)
     * @param dataFileAutoCollectConfigDTO
     * @param saveMapCache
     * @param dataLine
     * @return
     */
    public static boolean getSaveCondition(Integer type, DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, Map<String, String> saveMapCache, List<String> dataLine, Map<String, Object> map) {
        boolean b = false;
        Map<String, DataColumnMappingDTO> saveMap = dataFileAutoCollectConfigDTO.getSaveMap();
        if (MapUtils.isEmpty(saveMap)) return b;
        for (String key : dataFileAutoCollectConfigDTO.getSaveMap().keySet()) {
            if (key.equals(Constants.EOF)) {
                continue;
            }
            DataColumnMappingDTO dataColumnMappingDTO = saveMap.get(key);
            List<DataColumnMappingDTO.AnalyticRule> analyticRuleList = dataColumnMappingDTO.getAnalyticRuleList();
            /*按条件筛选*/
            StringBuilder sb = new StringBuilder();
            if (CollectionUtils.isEmpty(analyticRuleList)) return b;
            for (DataColumnMappingDTO.AnalyticRule analyticRule : analyticRuleList) {
                if (type == 1) {
                    if (StringUtils.isNotEmpty(analyticRule.getConnector())) {
                        sb.append(analyticRule.getConnector());
                    }
                    sb.append(dataLine.get(analyticRule.getNumber() - 1));
                } else {
                    if (StringUtils.isNotEmpty(analyticRule.getConnector())) {
                        sb.append(analyticRule.getConnector());
                    }
                    sb.append(map.get(analyticRule.getName()));
                }
            }
            switch (EvaluationTypeEnum.getType(dataColumnMappingDTO.getEvaluationType())) {
                case NEW:
                    if (saveMapCache.get(key) == null) {
                        saveMapCache.put(key, sb.toString());
                    } else {
                        if (!saveMapCache.get(key).contentEquals(sb)) {
                            b = true;
                            saveMapCache.put(key, sb.toString());
                        }
                    }
                    break;
                case LESS:
                    b = Double.parseDouble(sb.toString()) < Double.parseDouble(dataColumnMappingDTO.getEvaluationValue());
                    break;
                case LESS_OR_EQUAL:
                    b = Double.parseDouble(sb.toString()) <= Double.parseDouble(dataColumnMappingDTO.getEvaluationValue());
                    break;
                case EQUAL:
                    b = Double.parseDouble(sb.toString()) == Double.parseDouble(dataColumnMappingDTO.getEvaluationValue());
                    break;
                case NOT_EQUAL:
                    b = Double.parseDouble(sb.toString()) != Double.parseDouble(dataColumnMappingDTO.getEvaluationValue());
                    break;
                case GREATER_OR_EQUAL:
                    b = Double.parseDouble(sb.toString()) >= Double.parseDouble(dataColumnMappingDTO.getEvaluationValue());
                    break;
                case GREATER:
                    if (Double.parseDouble(sb.toString()) > Double.parseDouble(dataColumnMappingDTO.getEvaluationValue())) {
                        b = true;
                    }
                    break;
                case START:
                    b = sb.toString().startsWith(dataColumnMappingDTO.getEvaluationValue());
                    break;
                case END:
                    b = sb.toString().endsWith(dataColumnMappingDTO.getEvaluationValue());
                    break;
                case CONTAIN:
                    b = sb.toString().contains(dataColumnMappingDTO.getEvaluationValue());
                    break;
                case NULL:
                    b = StringUtils.isEmpty(sb.toString());
                    break;
            }
        }
        return b;
    }

    /**
     * 判断名称是否为空
     *
     * @param total     行数
     * @param name      字段名称
     * @param filedName 映射名称
     * @return
     */
    public static boolean judgeName(int total, String name, String filedName) {
        if (StringUtils.isEmpty(name)) {
            log.info("第{}行,映射名称-------->{} 为空", total, filedName);
            return true;
        }
        return false;
    }
}
