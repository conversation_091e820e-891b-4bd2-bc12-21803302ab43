package com.yingfei.entity.util.controlChart;

import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.SGRP_VAL_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.enums.CorrectionConstantEnum;
import com.yingfei.entity.util.CorrectionConstantUtil;
import com.yingfei.entity.util.HistogramUtil;
import com.yingfei.entity.vo.DataPointVO;
import com.yingfei.entity.util.ControlChartCalculateService;
import com.yingfei.entity.vo.SubgroupDataVO;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 控制图分组类型-------->标准差件内图
 */
@Service
public class ControlChartGroupSDW implements ControlChartCalculateService {


    /**
     * 图类型为单值-移动极差-标准差件内-------> 数据点计算公式为: 子测试的标准差
     * 图类型为均值-极差-标准差件内-------> 数据点计算公式为: 子组内每个样本子测试标准差的最大值
     * 图类型为均值-标准差-标准差件内-------> 数据点计算公式为: 子组内每个样本子测试标准差的最大值
     *
     * @param subgroupDataDTOList   子组测试数据,要按子组时间正序排列
     * @param chartTypeEnum 图类型
     */
    @Override
    public List<DataPointVO> dataPoint(List<SubgroupDataDTO> subgroupDataDTOList, ControlChartTypeEnum chartTypeEnum) {
        List<DataPointVO> list = new ArrayList<>();
        switch (chartTypeEnum) {
            case CONTROL_CHART_IX_MR_SDW:
            case CONTROL_CHART_X_R_SDW:
            case CONTROL_CHART_X_SD_SDW:
                subgroupDataDTOList.forEach(subgroupDataDTO -> {
                    list.add(DataPointVO.getDataPoint(subgroupDataDTO, subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getWithinPieceSdMax()));
                });
                break;
            default:
                break;
        }
        return list;
    }

    /**
     * F_SW*c4
     *
     * @return
     */
    @Override
    public Double controlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getF_SW() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_C4);
    }

    /**
     * CL * B4
     *
     * @return
     */
    @Override
    public Double controlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_B4);
    }

    /**
     * CL*B3
     */
    @Override
    public Double controlLimitLCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_B3);
    }

    /**
     * 控制限CL
     * Mean
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getMean();
    }

    /**
     * 控制限UCL
     * CL*B4
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_B4);
    }

    /**
     * 控制限LCL
     * CL*B3
     *
     * @param controlLimitDto
     */
    @Override
    public Double historyControlLimitLCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getCL() * CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_B3);
    }
}
