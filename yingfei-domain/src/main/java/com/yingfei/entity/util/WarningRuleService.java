package com.yingfei.entity.util;

import com.alibaba.fastjson2.JSONArray;
import com.yingfei.entity.domain.RULE_INF;
import com.yingfei.entity.dto.AlarmMessageDTO;
import com.yingfei.entity.dto.DataSummaryDTO;
import com.yingfei.entity.dto.SPEC_INF_DTO;
import com.yingfei.entity.enums.ControlChartSingleEnum;
import com.yingfei.entity.vo.ControlLimitVO;
import com.yingfei.entity.vo.SubgroupDataVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 报警规则Service接口
 */
public interface WarningRuleService {

    /**
     * 判断报警规则是否触发
     *
     * @param alrRuleList        报警规则
     * @param controlLimitVOList 控制限返回对象
     */
    List<String> judgeWarningRule(List<RULE_INF> alrRuleList, List<ControlLimitVO> controlLimitVOList, DataSummaryDTO dataSummaryDTO, SPEC_INF_DTO specInfDto);

    /**
     * 数据监控报警规则触发
     *
     * @return 返回对应的报警详情
     */
    List<AlarmMessageDTO> monitorJudgeWarningRule(List<RULE_INF> alrRuleList, List<SubgroupDataVO> subgroupDataVOList, ControlChartSingleEnum chartSingleEnum, SPEC_INF_DTO specInfDto, Integer num);
}
