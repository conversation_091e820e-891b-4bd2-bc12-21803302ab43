package com.yingfei.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 员工责任信息VO
 * <AUTHOR>
 */
@Data
public class EMPL_RESPONSIBLE_INF_VO extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RESP;

    /**
     * 业务类型 0=员工与测试映射
     */
    @ApiModelProperty(value = "业务类型 0=员工与测试映射")
    private Integer F_TYPE;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPL;

    /**
     * 映射业务JSON
     */
    @ApiModelProperty(value = "映射业务JSON 如果是F_TYPE=0，则为测试ID")
    private String F_DATA;



    /**
     * 记录创建用户ID
     */
    @ApiModelProperty(value = "记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty(value = "记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;



    // ========== 查询条件字段 ==========

    /**
     * 员工ID列表（用于批量查询）
     */
    @ApiModelProperty(value = "员工ID列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> emplIds;



    /**
     * 测试ID列表（用于批量查询）
     */
    @ApiModelProperty(value = "测试ID列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> testIds;

    /**
     * 主键ID列表（用于批量删除）
     */
    @ApiModelProperty(value = "主键ID列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> ids;
}
