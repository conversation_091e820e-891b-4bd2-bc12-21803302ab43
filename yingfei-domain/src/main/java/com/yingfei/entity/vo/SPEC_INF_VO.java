package com.yingfei.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.dto.SPEC_ACTIVATION_ALARM_DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 公差限表
 *
 * @TableName SPEC_INF
 */
@Data
@Accessors(chain = true)
public class SPEC_INF_VO extends BaseEntity {

    /**
     * 记录主键
     */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SPEC;
    /**
     * 产品ID
     */
    @ApiModelProperty("产品ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;
    /**
     * 测试ID
     */
    @ApiModelProperty("测试ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;
    /**
     * 过程ID
     */
    @ApiModelProperty("过程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;
    /**
     * 工作ID
     */
    @ApiModelProperty("工作ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_JOB;
    /**
     * 公差上限
     */
    @NotNull(message = "[公差上限]不能为空")
    @ApiModelProperty("公差上限")
    private Double F_USL;
    /**
     * 目标值
     */
    @NotNull(message = "[目标值]不能为空")
    @ApiModelProperty("目标值")
    private Double F_TAR;
    /**
     * 公差下限
     */
    @NotNull(message = "[公差下限]不能为空")
    @ApiModelProperty("公差下限")
    private Double F_LSL;
    /**
     * 合理上限
     */
    @NotNull(message = "[合理上限]不能为空")
    @ApiModelProperty("合理上限")
    private Double F_URL;
    /**
     * 合理下限
     */
    @NotNull(message = "[合理下限]不能为空")
    @ApiModelProperty("合理下限")
    private Double F_LRL;
    /**
     * 报警上限
     */
    @NotNull(message = "[报警上限]不能为空")
    @ApiModelProperty("报警上限")
    private Double F_UWL;
    /**
     * 报警下限
     */
    @NotNull(message = "[报警下限]不能为空")
    @ApiModelProperty("报警下限")
    private Double F_LWL;
    /**
     * 件内上限
     */
    @NotNull(message = "[件内上限]不能为空")
    @ApiModelProperty("件内上限")
    private Double F_UWP;
    /**
     * 件内下限
     */
    @NotNull(message = "[件内下限]不能为空")
    @ApiModelProperty("件内下限")
    private Double F_LWP;
    /**
     * 子组均值上限
     */
    @NotNull(message = "[子组均值上限]不能为空")
    @ApiModelProperty("子组均值上限")
    private Double F_UAL;
    /**
     * 子组均值下限
     */
    @NotNull(message = "[子组均值下限]不能为空")
    @ApiModelProperty("子组均值下限")
    private Double F_LAL;
    /**
     * 目标Cp
     */
    @NotNull(message = "[目标Cp]不能为空")
    @ApiModelProperty("目标Cp")
    private Double F_CP = 1.67d;
    /**
     * 目标Cpk
     */
    @NotNull(message = "[目标Cpk]不能为空")
    @ApiModelProperty("目标Cpk")
    private Double F_CPK = 1.33d;
    /**
     * 目标Pp
     */
    @NotNull(message = "[目标Pp]不能为空")
    @ApiModelProperty("目标Pp")
    private Double F_PP = 1.67d;
    /**
     * 目标Ppk
     */
    @NotNull(message = "[目标Ppk]不能为空")
    @ApiModelProperty("目标Ppk")
    private Double F_PPK = 1.33d;
    /**
     * 各界限是否报警标识
     */
    @ApiModelProperty("各界限是否报警标识")
    private String F_AFLAG = "0";
    /**
     * 各界限是否激活标识
     */
    @ApiModelProperty("各界限是否激活标识")
    private String F_EFLAG="0";
    /**
     * 因子，默认为1
     */
    @ApiModelProperty("因子，默认为1")
    private Double F_FACTOR = 1d;
    /**
     * 是否删除标记，默认值为0
     */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
     * 记录最后编辑用户ID
     */
    @ApiModelProperty("记录最后编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;
    /**
     * 产品版本ID
     */
    @ApiModelProperty("产品版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PTRV;

    /**
     * 报警激活标识
     */
    @ApiModelProperty("报警激活标识")
    private SPEC_ACTIVATION_ALARM_DTO specActivationAlarmDto;

    /**
     * 产品id列表
     */
    private List<Long> partIds;

    /**
     * 过程id列表
     */
    private List<Long> prcsIds;

    /**
     * 测试id列表
     */
    private List<Long> testIds;

    /**
     * 数据隔离字段
     */
    @ApiModelProperty("数据隔离字段")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> hierarchyInfIds;


    // 自定义验证方法
    public void validateLimits() {
        boolean allNull = F_USL == null && F_TAR == null && F_LSL == null &&
                F_URL == null && F_LRL == null &&
                F_UWL == null && F_LWL == null &&
                F_UWP == null && F_LWP == null &&
                F_UAL == null && F_LAL == null;

        if (allNull) {
            throw new BusinessException(DataManagementExceptionEnum.EFFECTIVE_TOLERANCE_LIMIT);
        }
    }
}
