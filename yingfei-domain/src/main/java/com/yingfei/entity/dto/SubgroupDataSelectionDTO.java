package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.enums.TimeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class SubgroupDataSelectionDTO extends BaseEntity {

    @ApiModelProperty("子组ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SGRP;
    /**
     * 产品ID
     */
    @ApiModelProperty("产品ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;

    @ApiModelProperty("产品版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_REV;

    /**
     * 过程ID
     */
    @ApiModelProperty("过程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;
    /**
     * 测试id
     */
    @ApiModelProperty("测试ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;
    /**
     * 产品批次
     */
    @ApiModelProperty("产品批次")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_LOT;
    /**
     * 工作ID
     */
    @ApiModelProperty("工作ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_JOB;
    /**
     * 班次ID
     */
    @ApiModelProperty("班次ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SHIFT;

    @ApiModelProperty("产品id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> partList;

    @ApiModelProperty("产品版本id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> ptrvList;

    @ApiModelProperty("过程id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> prcsList;

    @ApiModelProperty("测试id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> testList;

    @ApiModelProperty("班次id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> shiftList;

    @ApiModelProperty("工作id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> jobList;

    @ApiModelProperty("批次id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> lotList;

    @ApiModelProperty("用户描述符组id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> dsgpList;

    @ApiModelProperty("用户描述符id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> descList;

    @ApiModelProperty("子组id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> sgrpList;

    @ApiModelProperty("参数集id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parameterId;

    @ApiModelProperty("图表id(对应分析模板里图表配置id)")
    private String chartId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    @ApiModelProperty("菜单id")
    private Long menuId;

    @ApiModelProperty("箱线图分析列表")
    private Object boxPlotsConfigDTO;

    /**
     * 参数对应查询数量
     */
    @Min(value = 1, message = "最大返回子组数必须大于0")
    private Integer totalNum;

    private Integer F_DEL = 0;

    /**
     * 失效标识 0=激活，1=失效 2:都显示
     */
    private Integer F_FLAG = 0;

    /**
     * 测试类型是否只查变量(0:否 1:是)
     */
    private Integer testType = 0;

    /**
     * 子计划是否完成状态(0:未完成 1:已完成)
     */
    private Integer F_FINISH_STATUS;

    /**
     * 检查计划id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_INSP_PLAN;

    /**
     * 采集唯一标识列表
     */
    private List<String> sampleIdList;

    /**
     * 检查计划id列表
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> planIds;

    /**
     * 是否用临时表(0否 1是)
     */
    private Integer isTemp = 0;

    /**
     * 临时表标识
     */
    private String tempIdentify;

    /**
     * 查询唯一标识
     */
    private String identification;

    /**
     * 过程事件解决状态(0:未解决  1:已解决)
     */
    private Integer evntStatus;

    /**
     * 是否范围查询(0:否 1:是)
     */
    private Integer isRange = 0;

    /**
     * 分析模板对应最大返回子组数
     */
    private Integer maxNum;
    /**
     * 能力趋势表 统计周期：0=按天统计，1=按月统计
     */
    private Integer trendType = 0;


    /**
     * 自定义菜单查询条件
     *
     * @param subgroupDataSelectionDTO
     * @param parameterSetInfDto
     * @return
     */
    public static SubgroupDataSelectionDTO getData(SubgroupDataSelectionDTO subgroupDataSelectionDTO, PARAMETER_SET_INF_DTO parameterSetInfDto) {
        SubgroupDataSelectionDTO sgrp_ext = new SubgroupDataSelectionDTO();
        sgrp_ext.setMenuId(subgroupDataSelectionDTO.getMenuId());
        sgrp_ext.setParameterId(subgroupDataSelectionDTO.getParameterId());
        sgrp_ext.setF_PART(subgroupDataSelectionDTO.getF_PART());
        sgrp_ext.setF_REV(subgroupDataSelectionDTO.getF_REV());
        sgrp_ext.setF_PRCS(subgroupDataSelectionDTO.getF_PRCS());
        sgrp_ext.setF_TEST(subgroupDataSelectionDTO.getF_TEST());
        sgrp_ext.setPartList(subgroupDataSelectionDTO.getPartList());
        sgrp_ext.setPrcsList(subgroupDataSelectionDTO.getPrcsList());
        sgrp_ext.setTestList(subgroupDataSelectionDTO.getTestList());
        sgrp_ext.setShiftList(subgroupDataSelectionDTO.getShiftList());
        sgrp_ext.setLotList(subgroupDataSelectionDTO.getLotList());
        sgrp_ext.setDescList(subgroupDataSelectionDTO.getDescList());
        sgrp_ext.setJobList(subgroupDataSelectionDTO.getJobList());
        sgrp_ext.setBoxPlotsConfigDTO(subgroupDataSelectionDTO.getBoxPlotsConfigDTO());
        sgrp_ext.setF_FLAG(subgroupDataSelectionDTO.getF_FLAG());
        sgrp_ext.setIdentification(subgroupDataSelectionDTO.getIdentification());
        sgrp_ext.setEvntStatus(subgroupDataSelectionDTO.getEvntStatus());
        sgrp_ext.setDbType(subgroupDataSelectionDTO.getDbType());
        sgrp_ext.setMaxNum(subgroupDataSelectionDTO.getMaxNum());
        if (subgroupDataSelectionDTO.getTotalNum() != null) {
            sgrp_ext.setTotalNum(subgroupDataSelectionDTO.getTotalNum());
        } else {
            sgrp_ext.setTotalNum(parameterSetInfDto.getF_MAX_ITEM() == null ? 1000 : parameterSetInfDto.getF_MAX_ITEM());
        }
        sgrp_ext.setNext(subgroupDataSelectionDTO.getNext());
        sgrp_ext.setOffset(subgroupDataSelectionDTO.getOffset());
        if (subgroupDataSelectionDTO.getStartTime() != null || subgroupDataSelectionDTO.getEndTime() != null) {
            sgrp_ext.setStartTime(subgroupDataSelectionDTO.getStartTime());
            sgrp_ext.setEndTime(subgroupDataSelectionDTO.getEndTime());
        } else {
            /*判断是否为动态日期*/
            if (parameterSetInfDto.getF_TIME_WINDOW_TYPE() == 1) {
                Date date = DateUtils.getNowDate();
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                switch (TimeEnum.getType(parameterSetInfDto.getF_DATERANGE_TYPE())) {
                    case MINUTE:
                        calendar.add(Calendar.MINUTE, -parameterSetInfDto.getF_RANGE_INTERVAL());
                        break;
                    case HOUR:
                        calendar.add(Calendar.HOUR, -parameterSetInfDto.getF_RANGE_INTERVAL());
                        break;
                    case DAY:
                        calendar.add(Calendar.DAY_OF_MONTH, -parameterSetInfDto.getF_RANGE_INTERVAL());
                        break;
                    case WEEK:
                        calendar.add(Calendar.DAY_OF_WEEK, -parameterSetInfDto.getF_RANGE_INTERVAL());
                        break;
                    case MONTH:
                        calendar.add(Calendar.MONTH, -parameterSetInfDto.getF_RANGE_INTERVAL());
                        break;
                    case YEAR:
                        calendar.add(Calendar.YEAR, -parameterSetInfDto.getF_RANGE_INTERVAL());
                }
                sgrp_ext.setStartDate(calendar.getTime());
                sgrp_ext.setEndDate(date);
            } else {
                if (parameterSetInfDto.getF_START_DATE() != null && parameterSetInfDto.getF_END_DATE() != null) {
                    sgrp_ext.setStartDate(parameterSetInfDto.getF_START_DATE());
                    sgrp_ext.setEndDate(parameterSetInfDto.getF_END_DATE());
                }
            }
        }
        Map<String, List<Long>> map = parameterSetInfDto.getMap();
        if (CollectionUtils.isNotEmpty(map.get(Constants.shiftList))) {
            sgrp_ext.setShiftList(map.get(Constants.shiftList));
        }
        if (CollectionUtils.isNotEmpty(map.get(Constants.jobList))) {
            sgrp_ext.setJobList(map.get(Constants.jobList));
        }
        if (CollectionUtils.isNotEmpty(map.get(Constants.descList))) {
            sgrp_ext.setDescList(map.get(Constants.descList));
        }
        return sgrp_ext;
    }
}
