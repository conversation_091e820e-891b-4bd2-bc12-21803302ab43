package com.yingfei.entity.dto;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.entity.domain.EVNT_INF;
import com.yingfei.entity.domain.SGRP_VAL;
import com.yingfei.entity.util.HistogramUtil;
import com.yingfei.entity.vo.SubgroupDataVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * json对象
 */
@Data
@ApiModel
public class SGRP_VAL_CHILD_DTO {

    /**
     * 测试id
     */
    @ApiModelProperty("测试id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long testId;

    @ApiModelProperty("子计划id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long childId;

    /**
     * 测试名称
     */
    @ApiModelProperty("测试名称")
    private String testName;

    /**
     * 测试实际样本量
     */
    @ApiModelProperty("测试实际样本量")
    private Integer num;

    /**
     * 测试样本量集合
     */
    @ApiModelProperty("测试样本量集合")
    private List<Test> testList;

    /**
     * 均值(testList 测试值的均值)
     */
    @ApiModelProperty("均值")
    private Double average;

    /**
     * 极差或者标准差(根据实际样本量算极差或者标准差)
     * 如果实际样本量大于1小于10  计算极差 最大减最小
     * 大于等于10 计算标准差
     */
    @ApiModelProperty("极差")
    private Double range;

    /**
     * 标准差  大于等于10 计算标准差
     */
    private Double sd;

    /**
     * 件内的极差或者标准差的最大值(取testList所有样本的件内极差或标准差的最大值)
     */
    @ApiModelProperty("件内的极差的最大值")
    private Double withinPieceRangeMax;

    /**
     * 件内的标准差的最大值(取testList所有样本的件内极差或标准差的最大值)
     */
    @ApiModelProperty("件内的标准差的最大值")
    private Double withinPieceSdMax;

    /**
     * 最大值
     */
    private Double max;

    /**
     * 最小值
     */
    private Double min;

    @ApiModelProperty("子组时间")
    private Date F_SGTM;

    /**
     * 子测试样本量
     */
    private Integer F_SBSZ;

    /**
     * 报警详情
     */
    private List<EVNT_INF> evntInfList;

    /**
     * 对应公差限
     */
    private SPEC_INF_DTO specInfDto;

    /**
     * 缺陷代码
     */
    private List<DEF_DAT_DTO> defDatDtoList;

    /**
     * 子测试
     */
    @Data
    public static class SubTest {

        /**
         * 子测试序号
         */
        private Integer subTestNo;

        /**
         * 子测试测试值
         */
        private Double subTestValue;

        /**
         * 子测试缺陷id
         */
        private String subDefectId;

        /**
         * 子测试缺陷代码组id
         */
        private String sudDefectGrpId;

        /**
         * 子测试图片
         */
        private String subImg;

    }

    @Data
    public static class Test {
        /**
         * 过程id 前端传入
         */
        @ApiModelProperty("过程id")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long F_PRCS;

        /**
         * 测试序号
         */
        private Integer testNo;

        /**
         * 测试值(如果为子测试，则为当前样本所有子测试值的均值;否则则是实际值)
         */
        private Double testVal;

        /**
         * 子测试集合
         */
        private List<SubTest> subTestList;

        /**
         * 缺陷代码id
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long defectId;

        /**
         * 缺陷代码组id
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long defectGrpId;

        /**
         * 缺陷代码名称
         */
        private String defectName;

        /**
         * 缺陷代码组名称
         */
        private String defectGrpName;

        /**
         * 序列号id
         */
        private String serialId;

        /**
         * 图片
         */
        private String img;

        /**
         * 当前样本子测试极差
         * (如果有子测试根据子测试的数量来计算极差或标准差)
         * 如果子测试数量小于10  计算极差 最大减最小
         * 如果子测试数量大于等于10  计算标准差  见标准差公式
         */
        private Double withinPieceRange;

        /**
         * 当前样本子测试标准差
         * (如果有子测试根据子测试的数量来计算极差或标准差)
         * 如果子测试数量小于10  计算极差 最大减最小
         * 如果子测试数量大于等于10  计算标准差  见标准差公式(长期标准差)
         */
        private Double withinPieceSd;

    }


    /**
     * 计算测试所需值
     *
     * @param subgroupDataVO
     * @param sgrpValChildDtoList
     * @return
     */
    public static List<SGRP_VAL> computeVal(SubgroupDataVO subgroupDataVO, List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList) {
        List<SGRP_VAL> list = new ArrayList<>();
        for (SGRP_VAL_CHILD_DTO sgrpValChildDTO : sgrpValChildDtoList) {
            int childNum = getChildNum(subgroupDataVO, sgrpValChildDTO);

            /*保存测试对象*/
            SGRP_VAL sgrpVal = new SGRP_VAL();
            if (sgrpValChildDTO.getChildId() == null) {
                sgrpValChildDTO.setChildId(subgroupDataVO.getChildId());
            }
            sgrpVal.setF_SGRP(subgroupDataVO.getF_SGRP())
                    .setF_TEST(sgrpValChildDTO.getTestId())
                    .setF_DATA(JSONObject.toJSONString(sgrpValChildDTO))
                    .setF_SGSZ(sgrpValChildDTO.getNum() == null ? subgroupDataVO.getF_SGSZ() : sgrpValChildDTO.getNum())
                    .setF_SBSZ(childNum)
                    .setF_CHILD(sgrpValChildDTO.getChildId() == null ? subgroupDataVO.getChildId() : sgrpValChildDTO.getChildId())
                    .setF_CRUE(subgroupDataVO.getF_CRUE())
                    .setF_EDUE(subgroupDataVO.getF_EDUE())
                    .setF_SGTM(subgroupDataVO.getF_SGTM() == null ? DateUtils.getNowDate() : subgroupDataVO.getF_SGTM());
            list.add(sgrpVal);
        }
        return list;
    }

    public static int getChildNum(SubgroupDataVO subgroupDataVO, SGRP_VAL_CHILD_DTO sgrpValChildDTO) {
        /*判断是否有子测试*/
        List<Test> testList = sgrpValChildDTO.getTestList();
        int childNum = 0;
        for (Test test : testList) {
            if (CollectionUtils.isEmpty(test.getSubTestList())) continue;
            /*如果子测试不为空,则计算测试值*/
            List<Double> subTestValList = test.getSubTestList().stream().map(SubTest::getSubTestValue)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            Double aDouble = subTestValList.stream().reduce(0d, Double::sum);
            Double v = aDouble / test.getSubTestList().size();
            test.setTestVal(v);
            childNum = test.getSubTestList().size();

            /*子测试样本量大于1 两个都计算*/
            Double longTermStandardDeviation = HistogramUtil.getLongTermStandardDeviation(subTestValList);
            /*样本件内标准差*/
            test.setWithinPieceSd(longTermStandardDeviation);

            Double max = subTestValList.stream().max(Double::compareTo).orElse(0D);
            Double min = subTestValList.stream().min(Double::compareTo).orElse(0D);
            /*样本件内极差*/
            test.setWithinPieceRange(BigDecimal.valueOf(max).subtract(BigDecimal.valueOf(min)).doubleValue());

        }
        subgroupDataVO.setF_SGSZ(childNum);
        /*计算所有样本均值的均值*/
        List<Double> testValList = testList.stream().map(Test::getTestVal).filter(Objects::nonNull).collect(Collectors.toList());
        if (testValList.size() != testList.size()) {
            List<Test> collect = testList.stream().filter(t -> t.getTestVal() != null).collect(Collectors.toList());
            sgrpValChildDTO.setTestList(collect);
        }
        Double average = testValList.stream().reduce(0d, Double::sum) / testValList.size();
        sgrpValChildDTO.setAverage(average);
        /*测试实际样本量*/
        if (sgrpValChildDTO.getNum() == null) {
            sgrpValChildDTO.setNum(testValList.size());
        }
        /*最大值*/
        sgrpValChildDTO.setMax(testValList.stream().max(Double::compareTo).orElse(0D));
        /*最小值*/
        sgrpValChildDTO.setMin(testValList.stream().min(Double::compareTo).orElse(0D));
        /*计算长期标准差*/
        Double longTermStandardDeviation = HistogramUtil.getLongTermStandardDeviation(testValList);
        sgrpValChildDTO.setSd(longTermStandardDeviation);

        /*计算极差*/
        Double max = testValList.stream().max(Double::compareTo).orElse(0D);
        Double min = testValList.stream().min(Double::compareTo).orElse(0D);
        sgrpValChildDTO.setRange(BigDecimal.valueOf(max).subtract(BigDecimal.valueOf(min)).doubleValue());


        /*判断是否有子测试极差或者标准差*/
        List<Double> childRangeList = testList.stream().map(Test::getWithinPieceRange).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(childRangeList)) {
            sgrpValChildDTO.setWithinPieceRangeMax(childRangeList.stream().max(Double::compareTo).orElse(0D));
        }
        List<Double> childSdList = testList.stream().map(Test::getWithinPieceSd).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(childSdList)) {
            sgrpValChildDTO.setWithinPieceSdMax(childSdList.stream().max(Double::compareTo).orElse(0D));
        }
        return childNum;
    }
}
