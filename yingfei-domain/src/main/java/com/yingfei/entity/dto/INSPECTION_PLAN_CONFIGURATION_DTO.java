package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.enums.TimeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 检查计划配置
 */
@Data
@ApiModel
public class INSPECTION_PLAN_CONFIGURATION_DTO {

    /**
     * 检查计划通用属性配置
     *
     * @see com.yingfei.entity.enums.PlanCommonAttributesEnums
     */
    @ApiModelProperty("检查计划通用属性配置")
    List<String> attributesList;

    /**
     * 是否临时保存(0:否 1:是)
     */
    @ApiModelProperty("是否临时保存(0:否 1:是)")
    Integer isTempSave = 0;

    @ApiModelProperty("开始执行时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startExecutionTime;

    /**
     * 时间间隔
     */
    @ApiModelProperty("时间间隔")
    private Integer timeInterval;

    /**
     * 时间类型
     * @see TimeEnum
     */
    @ApiModelProperty("时间类型(1:分钟 2:小时 3:天)")
    private Integer timeType;

    /**
     * 开始前提示时间
     */
    @ApiModelProperty("开始前提示时间")
    private Integer startBeforeTime;

    /**
     * 开始后报警触发时间
     */
    @ApiModelProperty("开始后报警触发时间")
    private Integer startAfterTime;

    /**
     * 提示和报警时间类型
     * @see TimeEnum
     */
    @ApiModelProperty("时间类型(1:分钟 2:小时 3:天)")
    private Integer tipTimeType;

    /**
     * 是否持续输入
     */
    private Boolean isContinuousInput = false;

    /**
     * 展示大图或小图
     */
    private Boolean isShowBigPic = false;

    /**
     * 是否新增产品
     */
    private Boolean isNewPart = false;

    /**
     * 检查计划二次筛选条件
     */
    List<PARAMETER_CHILD_DTO> parameterChildDtoList;
}
