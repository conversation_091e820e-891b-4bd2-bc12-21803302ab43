package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
* 工作流的流程实例的拓展
* @TableName BPM_PROCESS_INSTANCE
*/
@Data
public class BPM_PROCESS_INSTANCE_DTO extends BaseEntity {

    /**
    * 主键
    */
    @ApiModelProperty("主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_BPIE;
    /**
    * 流程实例的名字
    */
    @ApiModelProperty("流程实例的名字")
    private String F_NAME;
    /**
    * 发起流程的用户编号
    */
    @ApiModelProperty("发起流程的用户编号")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_START_USER;
    /**
    * 流程定义的编号
    */
    @ApiModelProperty("流程定义的编号")
    private String F_PROCESS_DEFINITION;
    /**
    * 流程实例的编号
    */
    @ApiModelProperty("流程实例的编号")
    private String F_PROCESS_INSTANCE;
    /**
    * 流程分类
    */
    @NotNull(message="[流程分类]不能为空")
    @ApiModelProperty("流程分类")
    private String F_CATEGORY;
    /**
    * 流程实例的状态
    */
    @ApiModelProperty("流程实例的状态")
    private Integer F_STATUS;
    /**
    * 流程实例的结果
    */
    @ApiModelProperty("流程实例的结果")
    private Integer F_RESULT;
    /**
    * 结束时间
    */
    @ApiModelProperty("结束时间")
    private Date F_END_TIME;
    /**
    * 表单值
    */
    @ApiModelProperty("表单值")
    private String F_FROM_VAL;
    /**
    * 是否删除(0:否 1:是)
    */
    @ApiModelProperty("是否删除(0:否 1:是)")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 创建用户
    */
    @ApiModelProperty("创建用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 修改用户
    */
    @ApiModelProperty("修改用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    @ApiModelProperty("业务的唯一标识-例如说，请假申请的编号")
    private String businessKey;

    /**
     * 报警表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EVNT;
    /**
     * 当前任务
     */
    private List<Task> tasks;

    /**
     * 发起流程的用户
     */
    private User startUser;

    /**
     * 流程定义
     */
    private ProcessDefinition processDefinition;

    @Schema(description = "流程任务")
    @Data
    public static class Task {

        @Schema(description = "流程任务的编号", required = true, example = "1024")
        private String id;

        @Schema(description = "任务名称", required = true, example = "芋道")
        private String name;

    }

    @Schema(description = "用户信息")
    @Data
    public static class User {

        @Schema(description = "用户编号", required = true, example = "1")
        private Long id;
        @Schema(description = "用户昵称", required = true, example = "芋艿")
        private String nickname;

        @Schema(description = "部门编号", required = true, example = "1")
        private Long deptId;
        @Schema(description = "部门名称", required = true, example = "研发部")
        private String deptName;

    }

    @Schema(description = "流程定义信息")
    @Data
    public static class ProcessDefinition {

        @Schema(description = "编号", required = true, example = "1024")
        private String id;

        @Schema(description = "表单类型-参见 bpm_model_form_type 数据字典", example = "1")
        private Integer formType;
        @Schema(description = "表单编号-在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空", example = "1024")
        private Long formId;
        @Schema(description = "表单的配置-JSON 字符串。在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空", required = true)
        private String formConf;
        @Schema(description = "表单项的数组-JSON 字符串的数组。在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空", required = true)
        private List<String> formFields;
        @Schema(description = "自定义表单的提交路径，使用 Vue 的路由地址-在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空",
                example = "/bpm/oa/leave/create")
        private String formCustomCreatePath;
        @Schema(description = "自定义表单的查看路径，使用 Vue 的路由地址-在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空",
                example = "/bpm/oa/leave/view")
        private String formCustomViewPath;

        @Schema(description = "BPMN XML", required = true)
        private String bpmnXml;

    }
}
