package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 解析规则高级配置
 */
@Data
@ApiModel
public class GAUGE_FORMAT_ADVANCED_DTO {

    /**
     * 量具初始化命令
     */
    @ApiModelProperty("量具初始化命令")
    private String gaugeInit;

    /**
     * 测量初始化命令
     */
    @ApiModelProperty("测量初始化命令")
    private String measureInit;

    /**
     * 测量启动命令
     */
    @ApiModelProperty("测量启动命令")
    private String measureStart;

    /**
     * 测量停止命令
     */
    @ApiModelProperty("测量停止命令")
    private String measureStop;

    /**
     * 测量读取命令
     */
    @ApiModelProperty("测量读取命令")
    private String measureRead;

    /**
     * 测量公布命令
     */
    @ApiModelProperty("测量公布命令")
    private String measurePublish;

    /**
     * 测量请求命令
     */
    @ApiModelProperty("测量请求命令")
    private String measureRequest;

    /**
     * 子组采集启动命令
     */
    @ApiModelProperty("子组采集启动命令")
    private String sgrpCollectStart;

    /**
     * 子组采集停止命令
     */
    @ApiModelProperty("子组采集停止命令")
    private String sgrpCollectStop;

    /**
     * 测量命令延迟(ms)
     */
    @ApiModelProperty("测量命令延迟(ms)")
    private Long measureDelay;

    /**
     * 测量读取超时(ms)
     */
    @ApiModelProperty("测量读取超时(ms)")
    private Long measureReadTimeout;

    /**
     * 清除通讯缓冲器，然后读取量具
     */
    @ApiModelProperty("清除通讯缓冲器，然后读取量具")
    private Boolean beforeClean = false;

    /**
     * 在有效读取量具后清除通讯缓冲器
     */
    @ApiModelProperty("在有效读取量具后清除通讯缓冲器")
    private Boolean afterClean = false;
}
