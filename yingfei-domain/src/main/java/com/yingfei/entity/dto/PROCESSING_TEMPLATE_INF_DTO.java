package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;

/**
* 储存数据标准化信息表
* @TableName PROCESSING_TEMPLATE_INF
*/
@Data
public class PROCESSING_TEMPLATE_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PSTP;
    /**
    * 分公司主键
    */
    @ApiModelProperty("分公司主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DIV = 0L;
    /**
    * 数据标准化名称
    */
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("数据标准化名称")
    @Length(max= 100,message="编码长度不能超过100")
    private String F_NAME;
    /**
    * 测试类型，1=变量 2=缺陷 3=不良，默认值为1
    */
    @ApiModelProperty("测试类型，1=变量 2=缺陷 3=不良，默认值为1")
    private Integer F_TEST_TYPE;
    /**
    * 标准化类型 1=无 2=目标值 3=名义值 4=过程均值 5=标准化，默认值为1
    */
    @ApiModelProperty("标准化类型 1=无 2=目标值 3=名义值 4=过程均值 5=标准化，默认值为1")
    private Integer F_STANDARDIZE_TYPE;
    /**
    * 处理类型 1=无 2=经济型控制限 3=CUSUM(休哈特) 4=CUSUM(TABULAR) 5=EWMA 6=%CV，默认值为1
    */
    @ApiModelProperty("处理类型 1=无 2=经济型控制限 3=CUSUM(休哈特) 4=CUSUM(TABULAR) 5=EWMA 6=%CV，默认值为1")
    private Integer F_PROCESSING_TYPE;
    /**
    * 置信区间(数值范围：0.9~0.99999)
    */
    @ApiModelProperty("置信区间(数值范围：0.9~0.99999)")
    private Double F_CONFIDENTIAL_INTERVAL;
    /**
    * 西格玛数量(数值范围：1.623~4.417)
    */
    @ApiModelProperty("西格玛数量(数值范围：1.623~4.417)")
    private Double F_SIGMA_COUNT;
    /**
    * 数据标准化因子，默认为1
    */
    @ApiModelProperty("数据标准化因子，默认为1")
    private Double F_FACTOR = 1D;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;
}
