package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 系统通知:报警属性类
 */
@Data
public class SYSTEM_NOTIFICATION_ALARM_DTO {

    /**
     * 报警时间
     */
    String alarmTime;

    /**
     * 报警类型
     */
    List<AlarmDetails> alarmDetailList;

    /**
     * 报警规则模板名称
     */
    String templateName;

    /**
     * 报警子组id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    Long sgrpId;

    /**
     * 报警子组详情
     */
    List<SubgroupDataDTO> list;

    @Data
    public static class AlarmDetails {

        /**
         * 图表名称
         */
        private String chartName;

        /**
         * 报警类型
         */
        private String alarmType;

        /**
         * 对比值
         */
        private Double compareValue;

        /**
         * 实际值
         */
        private Double actualValue;

        /**
         * 对比值和实际值连接符(如:>,<)
         */
        private String connector;

    }
}


