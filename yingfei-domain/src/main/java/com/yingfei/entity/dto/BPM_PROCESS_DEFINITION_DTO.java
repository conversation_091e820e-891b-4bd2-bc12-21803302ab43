package com.yingfei.entity.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* Bpm 流程定义的拓展表

* @TableName BPM_PROCESS_DEFINITION
*/
@Data
public class BPM_PROCESS_DEFINITION_DTO extends BaseEntity {

    /**
    * 主键
    */
    @ApiModelProperty("主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_BPDE;
    /**
    * 流程定义的编号
    */
    @ApiModelProperty("流程定义的编号")
    private String F_PROCESS_DEFINITION;
    /**
    * 流程模型的编号
    */
    @ApiModelProperty("流程模型的编号")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MODE;
    /**
    * 描述
    */
    @ApiModelProperty("描述")
    private String F_DESCRIPTION;
    /**
    * 表单类型
    */
    @ApiModelProperty("表单类型")
    private Integer F_FROM_TYPE;
    /**
    * 表单表主键
    */
    @ApiModelProperty("表单表主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_FROM;
    /**
    * 表单的配置
    */
    @ApiModelProperty("表单的配置")
    private String F_FROM_CONF;
    /**
    * 表单项的数组
    */
    @ApiModelProperty("表单项的数组")
    private String F_FROM_FIELDS;
    /**
    * 自定义表单的提交路径
    */
    @ApiModelProperty("自定义表单的提交路径")
    private String F_FROM_SUBMIT_PATH;
    /**
    * 自定义表单的查看路径
    */
    @ApiModelProperty("自定义表单的查看路径")
    private String F_FROM_VIEW_PATH;
    /**
    * 是否删除(0:否 1:是)
    */
    @ApiModelProperty("是否删除(0:否 1:是)")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 创建用户
    */
    @ApiModelProperty("创建用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 修改用户
    */
    @ApiModelProperty("修改用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    @ApiModelProperty("版本")
    private Integer version;

    @ApiModelProperty( "部署时间")
    private Date deploymentTime;

    @ApiModelProperty("表单名字")
    private String formName;

    @ApiModelProperty("模型名称")
    private String modeName;
}
