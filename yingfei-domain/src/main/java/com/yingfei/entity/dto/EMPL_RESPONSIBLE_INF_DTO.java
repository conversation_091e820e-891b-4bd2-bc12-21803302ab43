package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 员工责任信息DTO
 * <AUTHOR>
 */
@Data
public class EMPL_RESPONSIBLE_INF_DTO extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RESP;

    /**
     * 业务类型 0=员工与测试映射
     */
    @ApiModelProperty(value = "业务类型 0=员工与测试映射")
    private Short F_TYPE;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPL;

    /**
     * 映射业务JSON
     */
    @ApiModelProperty(value = "映射业务JSON 如果是F_TYPE=0，则为测试ID")
    private String F_DATA;

    /**
     * 是否删除标记，默认值为0
     */
    @ApiModelProperty(value = "是否删除标记，默认值为0")
    private Integer F_DEL;

    /**
     * 记录创建用户ID
     */
    @ApiModelProperty(value = "记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty(value = "记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 记录创建日期
     */
    @ApiModelProperty(value = "记录创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime F_CRTM;

    /**
     * 记录最后编辑日期
     */
    @ApiModelProperty(value = "记录最后编辑日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime F_EDTM;

    // ========== 关联信息字段 ==========

    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "员工姓名")
    private String emplName;

    @ApiModelProperty(value = "测试ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;

    @ApiModelProperty(value = "测试名称")
    private String testName;



}
