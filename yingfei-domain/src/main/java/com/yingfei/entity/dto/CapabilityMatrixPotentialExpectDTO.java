package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2023-12-14 下午 2:13
 * @description 能力矩阵图 计算结果， Potential ，Potential(Centered Process)，Expect DTO
 */
@Data
@ApiModel
public class CapabilityMatrixPotentialExpectDTO {

    /**
     * {
     * "Potential": {
     * "Z_USL": 0,
     * "Z_LSL": 0,
     * "Fraction_GreaterThan_USL": 0,
     * "Fraction_LessThan_LSL": 0,
     * "Weighted_Fraction_LessThan_LSL": 0,
     * "Weighted_Fraction_GreaterThan_USL": 0,
     * "PDPM": 0,
     * "Yield": 0,
     * "Cpk": 0
     * <p>
     * },
     * "Potential_Centered_Process": {
     * "Spec_Z": 0,
     * "Weighted_Fraction_OOS": 0,
     * "Fraction_OOS": 0,
     * "PDPM": 0,
     * "Yield": 0,
     * "Cp": 0
     * <p>
     * },
     * "Expect": {
     * "USL_Z": 0,
     * "LSL_Z": 0,
     * "Fraction_LessThan_LSL": 0,
     * "Fraction_GreaterThan_USL": 0,
     * "Weighted Fraction_LessThan_LSL": 0,
     * "Weighted Fraction_GreaterThan_USL": 0,
     * "DPM": 0,
     * "Yield": 0,
     * "Ppk": 0
     * <p>
     * }
     * }
     */
    @ApiModelProperty("Potential 潜在能力")
    private PotentialBean Potential;
    @ApiModelProperty("Potential(Centered Process)  潜在能力(无偏移)")
    private PotentialCenteredProcessBean Potential_Centered_Process;
    @ApiModelProperty("Expect  预期能力")
    private ExpectBean Expect;
    private GradingBean gradingBean;

    @Data
    @ApiModel
    public static class PotentialBean {
        /**
         * Z_USL : 0
         * Z_LSL : 0
         * Fraction_GreaterThan_USL : 0
         * Fraction_LessThan_LSL : 0
         * Weighted_Fraction_LessThan_LSL : 0
         * Weighted_Fraction_GreaterThan_USL : 0
         * PDPM : 0
         * Yield : 0
         * Cpk : 0
         */
        @ApiModelProperty("Z USL")
        private Double Z_USL;
        @ApiModelProperty("Z LSL")
        private Double Z_LSL;
        @ApiModelProperty("Fraction > USL")
        private Double Fraction_GreaterThan_USL;
        @ApiModelProperty("Fraction < LSL")
        private Double Fraction_LessThan_LSL;
        @ApiModelProperty("Weighted Fraction < LSL")
        private Double Weighted_Fraction_LessThan_LSL;
        @ApiModelProperty("Weighted Fraction > USL")
        private Double Weighted_Fraction_GreaterThan_USL;
        @ApiModelProperty("PDPM")
        private Double PDPM;
        @ApiModelProperty("Yield")
        private Double Yield;
        @ApiModelProperty("Cpk")
        private Double Cpk;


    }

    @Data
    @ApiModel
    public static class PotentialCenteredProcessBean {
        /**
         * Spec_Z : 0
         * Weighted_Fraction_OOS : 0
         * Fraction_OOS : 0
         * PDPM : 0
         * Yield : 0
         * Cp : 0
         */
        @ApiModelProperty("Spec(Z)")
        private Double Spec_Z;
        @ApiModelProperty("Weighted Fraction OOS")
        private Double Weighted_Fraction_OOS;
        @ApiModelProperty("Fraction OOS")
        private Double Fraction_OOS;
        @ApiModelProperty("PDPM")
        private Double PDPM;
        @ApiModelProperty("Yield")
        private Double Yield;
        @ApiModelProperty("Cp")
        private Double Cp;


    }

    @Data
    @ApiModel
    public static class ExpectBean {
        /**
         * USL_Z : 0
         * LSL_Z : 0
         * Fraction_LessThan_LSL : 0
         * Fraction_GreaterThan_USL : 0
         * Weighted Fraction_LessThan_LSL : 0
         * Weighted Fraction_GreaterThan_USL : 0
         * DPM : 0
         * Yield : 0
         * Ppk : 0
         */
        @ApiModelProperty("USL(Z)")
        private Double USL_Z;
        @ApiModelProperty("LSL(Z)")
        private Double LSL_Z;
        @ApiModelProperty("Fraction < LSL")
        private Double Fraction_LessThan_LSL;
        @ApiModelProperty("Fraction > USL")
        private Double Fraction_GreaterThan_USL;
        @ApiModelProperty("Weighted Fraction < LSL")
        private Double WeightedFraction_LessThan_LSL;
        @ApiModelProperty("Weighted Fraction > USL")
        private Double WeightedFraction_GreaterThan_USL;
        @ApiModelProperty("DPM")
        private Double DPM;
        @ApiModelProperty("Yield")
        private Double Yield;
        @ApiModelProperty("Ppk")
        private Double Ppk;


    }

    @Data
    @ApiModel
    public static class GradingBean {
        @ApiModelProperty("能力矩阵对应的1,2,3值  " +
                "1、2、3等级分级标准，需要用到Performance参数，计算公式为：Expect Yield/Potenial Yied" +
                "1级：95%-100%（大于95%，小于等于100%）" +
                "2级：90%-95% （大于90%，小于等于95%）" +
                "3级：<=90%")
        private Double streamPerformance;

        @ApiModelProperty("1,2,3等级")
        private Integer performanceGrade;

        @ApiModelProperty("能力矩阵对应的A,B,C值 " +
                "A、B、C等级分级标准，需要用到参数 Potential Yield" +
                "A级：PY值在(99.99%,100%]内" +
                "B级：PY值在(99.73%,99.99%]内" +
                "C级：PY值在[0%,99.73%]内")
        private Double streamPotential;

        @ApiModelProperty("A,B,C等级")
        private String potentialGrade;

        @ApiModelProperty("1,2,3和A,B,C等级拼接")
        private String totalGrade;

        private Double expectYield;


    }
}
