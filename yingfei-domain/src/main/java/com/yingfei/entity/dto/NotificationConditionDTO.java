package com.yingfei.entity.dto;

import com.yingfei.entity.domain.NOTIFICATION_RULE;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 数据监控通知发送条件判断类
 */

@Data
public class NotificationConditionDTO {

    /**
     * 产品列表
     */
    List<Long> partList;

    /**
     * 产品不选择 是否不限制产品
     */
    Boolean isPartAll = false;

    /**
     * 产品是否动态
     */
    Boolean partIsDynamic = false;

    /**
     * 过程列表  为空时表示不限制过程
     */
    List<Long> prcsList;

    /**
     * 过程不选择 是否不限制过程
     */
    Boolean isPrcsAll = false;
    /**
     * 产品是否动态
     */
    Boolean prcsIsDynamic = false;

    /**
     * 测试列表  为空时表示不限制测试
     */
    List<Long> testList;

    /**
     * 测试不选择 是否不限制测试
     */
    Boolean isTestAll = false;

    /**
     * 是否动态
     */
    Boolean testIsDynamic = false;

    /**
     * 标签组列表  标签列表存在时不用判断
     */
    Set<Long> tagGrpList;

    /**
     * 标签列表
     */
    Set<Long> tagList;

    /**
     * 产品,过程,测试对应的通知规则
     */
    NOTIFICATION_RULE notificationRule;
}
