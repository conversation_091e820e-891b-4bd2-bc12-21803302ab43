package com.yingfei.entity.dto.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class MonthTrendDTO {
    @ApiModelProperty(value = "产品名称")
    private String partName;
    @ApiModelProperty(value = "产品版本名称")
    private String ptrvName;
    @ApiModelProperty(value = "过程名称")
    private String prcsName;
    @ApiModelProperty(value = "测试名称")
    private String testName;
    @ApiModelProperty(value = "USL")
    private Double usl;
    @ApiModelProperty(value = "TAR")
    private Double tar;
    @ApiModelProperty(value = "LSL")
    private Double lsl;

    @ApiModelProperty(value = "cpk")
    private Double cpk;
    @ApiModelProperty(value = "子组数量")
    private Integer sgrpCount;

    @ApiModelProperty(value = "月度Cpk")
    private List<CpkDTO> cpkDTOList;

    @ApiModelProperty(value = "目标cpk")
    private Double targetCpk;
    @ApiModelProperty(value = "月改善幅度")
    private Double mir;
    @ApiModelProperty(value = "负责人")
    private Long emplName;
    @ApiModelProperty(value = "能力分析", allowableValues = "[1,2,3,4]")
    private Integer ca;

    @Data
    public class CpkDTO {
        @ApiModelProperty(value = "月份")
        private Integer month;
        @ApiModelProperty(value = "cpk")
        private Double cpk;
    }
}
