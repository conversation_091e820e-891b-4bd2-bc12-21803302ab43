package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
* 菜单表
* @TableName MENU_INF
*/
@Data
public class MENU_INF_DTO extends BaseEntity {

    /**
    * 菜单id
    */
    @NotNull(message="[菜单id]不能为空")
    @ApiModelProperty("菜单id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MENU;
    /**
    * 菜单名称
    */
    @NotBlank(message="[菜单名称]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("菜单名称")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_NAME;
    /**
    * 菜单顺序
    */
    @NotNull(message="[菜单顺序]不能为空")
    @ApiModelProperty("菜单顺序")
    private Integer F_ORDER;
    /**
    * 菜单路由
    */
    @NotBlank(message="[菜单路由]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("菜单路由")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_PATH;
    /**
    * 菜单类型(0目录 1菜单 2按钮)
    */
    @NotNull(message="[菜单类型(0目录 1菜单 2按钮)]不能为空")
    @ApiModelProperty("菜单类型(0目录 1菜单 2按钮)")
    private Integer F_TYPE;
    /**
    * 自定义菜单几乘几
    */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("自定义菜单几乘几")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_DES;
    /**
    * 父级菜单id
    */
    @NotNull(message="[父级菜单id]不能为空")
    @ApiModelProperty("父级菜单id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PARENT;
    /**
    * 页面唯一标识
    */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("页面唯一标识")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_CODE;
    /**
    * 权限标识
    */
    @NotBlank(message="[权限标识]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("权限标识")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_PERMS;
    /**
    * 记录创建用户ID
    */
    @NotNull(message="[记录创建用户ID]不能为空")
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @NotNull(message="[记录编辑用户ID]不能为空")
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
    * 页面类型(0:系统定义,1:聚合分析,2:单向分析,3:自定义)
    */
    @NotNull(message="[页面类型(0:系统定义,1:聚合分析,2:单向分析,3:自定义)]不能为空")
    @ApiModelProperty("页面类型(0:系统定义,1:聚合分析,2:单向分析,3:自定义)")
    private Integer F_PAGE;

    /**
     * 页面对应分析模板的页面类型(0:聚合分析,1:单项分析)
     */
    private Integer templateType;

    private List<MENU_INF_DTO> children = new ArrayList<>();
}
