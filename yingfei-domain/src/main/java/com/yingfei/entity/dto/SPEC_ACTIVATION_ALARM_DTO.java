package com.yingfei.entity.dto;

import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.entity.vo.excel.SPEC_INF_EXCEL_VO;
import lombok.Data;

/**
 * 公差限是否激活报警
 */
@Data
public class SPEC_ACTIVATION_ALARM_DTO {

    /**
     * 公差上限(0:未激活 1:激活)
     */
    private Integer F_USL_ALARM = 0;

    /**
     * 公差下限(0:未激活 1:激活)
     */
    private Integer F_LSL_ALARM = 0;

    /**
     * 合理上限(0:未激活 1:激活)
     */
    private Integer F_URL_ALARM = 0;

    /**
     * 合理下限(0:未激活 1:激活)
     */
    private Integer F_LRL_ALARM = 0;

    /**
     * 报警上限(0:未激活 1:激活)
     */
    private Integer F_UWL_ALARM = 0;

    /**
     * 报警下限(0:未激活 1:激活)
     */
    private Integer F_LWL_ALARM = 0;

    /**
     * 件内上限(0:未激活 1:激活)
     */
    private Integer F_UWP_ALARM = 0;

    /**
     * 件内下限(0:未激活 1:激活)
     */
    private Integer F_LWP_ALARM = 0;

    /**
     * 子组均值上限(0:未激活 1:激活)
     */
    private Integer F_UAL_ALARM = 0;

    /**
     * 子组均值下限(0:未激活 1:激活)
     */
    private Integer F_LAL_ALARM = 0;

    public SPEC_ACTIVATION_ALARM_DTO() {

    }

    public static SPEC_ACTIVATION_ALARM_DTO getSpecActivationAlarm(SPEC_INF_EXCEL_VO specInfExcelVo) {
        SPEC_ACTIVATION_ALARM_DTO specActivationAlarmDto = new SPEC_ACTIVATION_ALARM_DTO();
        BeanUtils.copyPropertiesIgnoreNull(specInfExcelVo, specActivationAlarmDto);
        if (specActivationAlarmDto.getF_USL_ALARM() == 1 && specInfExcelVo.getF_USL() == null) {
            specActivationAlarmDto.setF_USL_ALARM(0);
        }
        if (specActivationAlarmDto.getF_LSL_ALARM() == 1 && specInfExcelVo.getF_LSL() == null) {
            specActivationAlarmDto.setF_LSL_ALARM(0);
        }
        if (specActivationAlarmDto.getF_URL_ALARM() == 1 && specInfExcelVo.getF_URL() == null) {
            specActivationAlarmDto.setF_URL_ALARM(0);
        }
        if (specActivationAlarmDto.getF_LRL_ALARM() == 1 && specInfExcelVo.getF_LRL() == null) {
            specActivationAlarmDto.setF_LRL_ALARM(0);
        }
        if (specActivationAlarmDto.getF_UWL_ALARM() == 1 && specInfExcelVo.getF_UWL() == null) {
            specActivationAlarmDto.setF_UWL_ALARM(0);
        }
        if (specActivationAlarmDto.getF_LWL_ALARM() == 1 && specInfExcelVo.getF_LWL() == null) {
            specActivationAlarmDto.setF_LWL_ALARM(0);
        }
        if (specActivationAlarmDto.getF_UWP_ALARM() == 1 && specInfExcelVo.getF_UWP() == null) {
            specActivationAlarmDto.setF_UWP_ALARM(0);
        }
        if (specActivationAlarmDto.getF_LWP_ALARM() == 1 && specInfExcelVo.getF_LWP() == null) {
            specActivationAlarmDto.setF_LWP_ALARM(0);
        }
        if (specActivationAlarmDto.getF_UAL_ALARM() == 1 && specInfExcelVo.getF_UAL() == null) {
            specActivationAlarmDto.setF_UAL_ALARM(0);
        }
        if (specActivationAlarmDto.getF_LAL_ALARM() == 1 && specInfExcelVo.getF_LAL() == null) {
            specActivationAlarmDto.setF_LAL_ALARM(0);
        }
        return specActivationAlarmDto;
    }
}
