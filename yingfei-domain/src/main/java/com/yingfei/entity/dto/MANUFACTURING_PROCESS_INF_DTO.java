package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
* 流程图结构表
* @TableName MANUFACTURING_PROCESS_INF
*/
@Data
public class MANUFACTURING_PROCESS_INF_DTO extends BaseEntity {

    /**
    * 主键
    */
    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MFPS;
    /**
    * 流程名称
    */
    @NotBlank(message="[流程名称]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("流程名称")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_NAME;
    /**
    * 流程图json对象(前端生成)
    */
    @NotBlank(message="[流程图json对象(前端生成)]不能为空")
    @ApiModelProperty("流程图json对象(前端生成)")
    private String F_DATA;
    /**
    * 创建用户
    */
    @NotNull(message="[创建用户]不能为空")
    @ApiModelProperty("创建用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 编辑用户
    */
    @NotNull(message="[编辑用户]不能为空")
    @ApiModelProperty("编辑用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();


    /**
     * 工厂id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLNT;
}
