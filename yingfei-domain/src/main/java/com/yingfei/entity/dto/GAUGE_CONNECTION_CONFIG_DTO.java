package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 量具连接参数配置类
 */
@Data
@ApiModel
public class GAUGE_CONNECTION_CONFIG_DTO {

    /**
     * 通讯源
     */
    @ApiModelProperty("通讯源")
    private String com;

    /**
     * 波特率
     */
    @ApiModelProperty("波特率")
    private Integer baud;

    /**
     * 奇偶校验 (0:无 1:奇数 2:偶数 3:标志 4:空格)
     */
    @ApiModelProperty("奇偶校验 (0:无 1:奇数 2:偶数 3:标志 4:空格)")
    private Integer parityCheck;

    /**
     * 数据位
     */
    @ApiModelProperty("数据位")
    private Integer dataBits;

    /**
     * 结束位(0:无  1: 1 ,2: 2 ,3: 1.5)
     */
    @ApiModelProperty("结束位")
    private Integer endBit;

    /**
     * 流控制(0:无 1:XON/XOFF 2:CTS/RTS 3:RTS=OFF/DTR=ON)
     */
    @ApiModelProperty("流控制(0:无 1:XON/XOFF 2:CTS/RTS 3:RTS=OFF/DTR=ON)")
    private Integer flowControl;

    /**
     * 缓冲大小
     */
    @ApiModelProperty("缓冲大小")
    private Integer bufferSize;
}
