package com.yingfei.entity.dto.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.dto.PARAMETER_SET_INF_DTO;
import com.yingfei.entity.enums.TimeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class MonthTrendQueryDTO extends BaseEntity {

    @ApiModelProperty("产品id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> partList;

    @ApiModelProperty("产品版本id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> ptrvList;

    @ApiModelProperty("过程id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> prcsList;

    @ApiModelProperty("测试id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> testList;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty("负责人id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> emplList;

}
