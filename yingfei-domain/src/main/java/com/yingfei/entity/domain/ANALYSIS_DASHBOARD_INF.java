package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 分析页面关联表
 *
 * @TableName ANALYSIS_DASHBOARD_INF
 */
@TableName(value = "ANALYSIS_DASHBOARD_INF")
@Data
public class ANALYSIS_DASHBOARD_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DASH;

    /**
     * 菜单表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MENU = 0L;

    /**
     * 参数集表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRST;

    /**
     * 分析图表模板id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ADTI;

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 标题描述
     */
    private String F_TITLE;

    public static ANALYSIS_DASHBOARD_INF init() {
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = new ANALYSIS_DASHBOARD_INF();
        BeanUtils.setAllFieldsToNull(analysisDashboardInf);
        return analysisDashboardInf;
    }
}