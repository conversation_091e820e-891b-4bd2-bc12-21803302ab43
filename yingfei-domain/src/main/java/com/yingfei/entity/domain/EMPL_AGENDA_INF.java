package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

import java.util.Date;

/**
 * 首页自定义提醒表
 * @TableName EMPL_WARN_INF
 */
@TableName(value ="EMPL_AGENDA_INF")
@Data
public class EMPL_AGENDA_INF extends BaseEntity {
 
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ID;

    /**
     * 提醒日期,到分钟
     */
    private Date F_DATE;

    /**
     * 提醒内容
     */
    private String F_CONTENT;

    /**
     * 提醒通知(1:系统消息通知 2:邮件通知 3:企业微信通知 4:钉钉通知)
     */
    private Integer F_TYPE;

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;
    

    /**
     * 提醒状态(0:未提醒 1:已提醒)
     */
    private Integer F_STATUS = 0;

    /**
     * 所选用户列表,逗号分割
     */
    private String F_EMPL;

    public static EMPL_AGENDA_INF init() {
        EMPL_AGENDA_INF data = new EMPL_AGENDA_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}