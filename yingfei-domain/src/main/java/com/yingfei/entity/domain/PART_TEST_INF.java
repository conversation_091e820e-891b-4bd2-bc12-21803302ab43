package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 
 * @TableName PART_TEST_INF
 */
@TableName(value ="PART_TEST_INF")
@Data
public class PART_TEST_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PATI;

    /**
     * 产品ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;

    /**
     * 测试ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;

    /**
     * 产品测试组合的图片
     */
    private String F_IMAGE;

    /**
     * 1:绑定显示图片,2:工艺菜单 (默认1)
     */
    private Integer F_TYPE = 1;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 描述
     */
    private String F_TEXT;

    /**
     * 产品版本ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PTRV;

    /**
     * 对应检验计划ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLAN;

    /**
     * 对应检验计划绑定内容
     */
    private String F_DATA;

    public static PART_TEST_INF init() {
        PART_TEST_INF data = new PART_TEST_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}