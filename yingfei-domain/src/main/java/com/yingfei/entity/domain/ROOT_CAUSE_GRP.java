package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存异常原因组信息表
 * @TableName ROOT_CAUSE_GRP
 */
@TableName(value ="ROOT_CAUSE_GRP")
@Data
public class ROOT_CAUSE_GRP extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RCGP;

    /**
     * 分公司主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DIV = 0L;

    /**
     * 异常原因组名称
     */
    private String F_NAME;

    /**
     * 异常原因组因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;


    public static ROOT_CAUSE_GRP init() {
        ROOT_CAUSE_GRP data = new ROOT_CAUSE_GRP();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }

}