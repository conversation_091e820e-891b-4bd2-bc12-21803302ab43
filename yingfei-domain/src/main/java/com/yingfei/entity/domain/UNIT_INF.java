package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import lombok.Data;

/**
 *
 * @TableName UNIT_INF
 */
@TableName(value = "UNIT_INF")
@Data
public class UNIT_INF extends BaseEntity {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_UNIT;

    /**
     *
     */
    private String F_NAME;

    /**
     *
     */
    private String F_ABBR = "";

    /**
     * 因子，默认为1
     */
    private Double F_FACTOR = 1d;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

}