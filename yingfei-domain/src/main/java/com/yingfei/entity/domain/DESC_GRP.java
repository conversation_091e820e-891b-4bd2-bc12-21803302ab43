package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;

import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存自定义描述符组信息表
 * @TableName DESC_GRP
 */
@TableName(value ="DESC_GRP")
@Data
public class DESC_GRP extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DSGP;

    /**
     * 分公司主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DIV = 0L;

    /**
     * 自定义描述符组名称
     */
    private String F_NAME;

    /**
     * 自定义描述符组因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 是否允许创建新值(0:不允许  1:允许)
     */
    private Integer F_TYPE = 0;

    /**
     * 输入新值时是否提示(0:不提示  1:提示)
     */
    private Integer F_HINT = 1;

    public static DESC_GRP init() {
        DESC_GRP data = new DESC_GRP();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}