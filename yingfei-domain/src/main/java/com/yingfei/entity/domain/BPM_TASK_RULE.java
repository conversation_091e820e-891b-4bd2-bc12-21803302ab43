package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Bpm 任务规则表
 * @TableName BPM_TASK_RULE
 */
@TableName(value ="BPM_TASK_RULE")
@Accessors(chain = true)
@Data
public class BPM_TASK_RULE extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RULE;

    /**
     * 流程模型的编号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MODE;

    /**
     * 流程定义的编号
     */
    private String F_PROCESS_DEFINITION;

    /**
     * 流程任务定义的 key
     */
    private String F_TASK_KEY;

    /**
     * 规则类型
     */
    private Integer F_TYPE = 0;

    /**
     * 规则值，JSON 数组
     */
    private String F_OPTIONS;

    /**
     * 是否删除(0:否 1:是)
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 创建用户
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 修改用户
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 表单表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_FROM;
}