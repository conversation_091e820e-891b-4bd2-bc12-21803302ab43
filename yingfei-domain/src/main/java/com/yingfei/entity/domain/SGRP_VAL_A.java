package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

import java.sql.Date;

/**
 * 子组测试值表
 * @TableName SGRP_VAL_A
 */
@TableName(value ="SGRP_VAL_A")
@Data
public class SGRP_VAL_A {
    /**
     * 记录主键
     */
    @TableId(type = IdType.NONE)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SGRP;

    /**
     * 测试ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;

    /**
     * 测试值json对象
     */
    private String F_DATA;

    /**
     * 实际样本量
     */
    private Integer F_SGSZ;

    /**
     * 子测试的样本量
     */
    private Integer F_SBSZ = 0;

    /**
     * 子组时间
     */
    private Date F_SGTM;

    /**
     * 子计划id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CHILD;

    /**
     * 创建人
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 修改人
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    public static SGRP_VAL_A init() {
        SGRP_VAL_A data = new SGRP_VAL_A();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}