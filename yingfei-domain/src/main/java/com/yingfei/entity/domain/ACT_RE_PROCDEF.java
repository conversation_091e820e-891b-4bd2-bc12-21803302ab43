package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName ACT_RE_PROCDEF
 */
@TableName(value ="ACT_RE_PROCDEF")
@Data
public class ACT_RE_PROCDEF implements Serializable {
    /**
     * 
     */
    @TableId
    private String ID_;

    /**
     * 
     */
    private Integer REV_;

    /**
     * 
     */
    private String CATEGORY_;

    /**
     * 
     */
    private String NAME_;

    /**
     * 
     */
    private String KEY_;

    /**
     * 
     */
    private Integer VERSION_;

    /**
     * 
     */
    private String DEPLOYMENT_ID_;

    /**
     * 
     */
    private String RESOURCE_NAME_;

    /**
     * 
     */
    private String DGRM_RESOURCE_NAME_;

    /**
     * 
     */
    private Integer HAS_START_FORM_KEY_;

    /**
     * 
     */
    private Integer SUSPENSION_STATE_;

    /**
     * 
     */
    private String TENANT_ID_;

    /**
     * 
     */
    private String VERSION_TAG_;

    /**
     * 
     */
    private Integer HISTORY_TTL_;

    /**
     * 
     */
    private Boolean STARTABLE_;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}