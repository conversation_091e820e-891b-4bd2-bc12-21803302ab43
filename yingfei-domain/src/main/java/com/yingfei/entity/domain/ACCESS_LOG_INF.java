package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户登录日志
 * @TableName ACCESS_LOG_INF
 */
@TableName(value ="ACCESS_LOG_INF")
@Data
public class ACCESS_LOG_INF extends BaseEntity {
    /**
//     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_LLOG;

    /**
     * 登录账号
     */
    private String F_CODE;

    /**
     * 状态 0成功 1失败
     */
    private Integer F_STATUS = 0;

    /**
     * ip地址
     */
    private String F_IPADDR;

    /**
     * 描述
     */
    private String F_MSG;

    /**
     * 访问时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date F_ACTM;
    
    public static ACCESS_LOG_INF init(){
        ACCESS_LOG_INF accessLogInf = new ACCESS_LOG_INF();
        BeanUtils.setAllFieldsToNull(accessLogInf);
        return accessLogInf;
    }

}