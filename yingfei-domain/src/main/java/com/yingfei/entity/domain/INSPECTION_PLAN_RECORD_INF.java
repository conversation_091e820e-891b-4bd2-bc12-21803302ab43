package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 检查计划定时执行记录表
 */
@Data
@ApiModel
@TableName(value ="INSPECTION_PLAN_RECORD_INF")
public class INSPECTION_PLAN_RECORD_INF extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLRE;

    @ApiModelProperty("检查计划表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLAN;

    @ApiModelProperty("定时任务表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SJOB;

    @ApiModelProperty("执行状态(0:待执行 1:已执行 2:执行超时)")
    private Integer F_TYPE = 0;

    @ApiModelProperty("执行人")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPL;

    @ApiModelProperty("报警信息id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EVNT;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    public static INSPECTION_PLAN_RECORD_INF init() {
        INSPECTION_PLAN_RECORD_INF data = new INSPECTION_PLAN_RECORD_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
