package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 报警信息表
 * @TableName EVNT_INF
 */
@TableName(value ="EVNT_INF")
@Accessors(chain = true)
@Data
public class EVNT_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EVNT;

    /**
     * 报警对应的事件类型和事件名称json  事件类型(1:公差限 2:控制限)
     */
    private String F_DATA;

    /**
     * 产品ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;

    /**
     * 过程ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;

    /**
     * 测试ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;

    /**
     * 报警发生时间
     */
    private Date F_EVTM;

    /**
     * 报警对应的子组时间
     */
    private Date F_SGTM;

    /**
     * 事件对应的子组ID。如果事件类型与子组无关，或子组已不存存在（例如，删除子组），则值为0
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SGRP;

    /**
     * 异常原因ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RTCS = 0L;

    /**
     * 异常原因时间
     */
    private Date F_RCTM;

    /**
     * 改善措施ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RSAT = 0L;

    /**
     * 改善措施时间
     */
    private Date F_RSTM;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 解决状态(0:未解决 1:已解决)
     */
    private Integer F_STATUS = 0;

    public static EVNT_INF init() {
        EVNT_INF data = new EVNT_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}