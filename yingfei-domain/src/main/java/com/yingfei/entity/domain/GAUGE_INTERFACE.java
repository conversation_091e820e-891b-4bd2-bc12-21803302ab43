package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 量具接口配置表
 * @TableName GAUGE_INTERFACE
 */
@TableName(value ="GAUGE_INTERFACE")
@Data
public class GAUGE_INTERFACE extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GAIN;

    /**
     * 串值初始化json
     */
    private String F_INIT_DATA;

    /**
     * 延迟多少毫秒
     */
    private Integer F_DELAY;

    /**
     * 初始值串
     */
    private String F_INIT_STR;

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;


    /**
     * 量具接口名称
     */
    private String F_NAME;

}