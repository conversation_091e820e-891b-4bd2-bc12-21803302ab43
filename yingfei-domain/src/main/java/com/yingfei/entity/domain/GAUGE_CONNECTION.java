package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 量具连接参数表
 * @TableName GAUGE_CONNECTION
 */
@TableName(value ="GAUGE_CONNECTION")
@Data
public class GAUGE_CONNECTION extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GICP;

    /**
     * 量具接口表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GAIN;

    /**
     * Agent表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GAAG;

    /**
     * 端口设置json(com口和通讯参数)
     * @see  com.yingfei.entity.dto.GAUGE_CONNECTION_CONFIG_DTO
     */
    private String F_CONFIG;

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

}