package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存报警规则信息表
 * @TableName RULE_INF
 */
@TableName(value ="RULE_INF")
@Data
public class RULE_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ALR;

    /**
     * 规则名称
     */
    private String F_NAME;

    /**
     * 规则优先级
     */
    private Integer F_PRIORITY = 0;

    /**
     * 报警类型 1=超出控制上限 2=超出控制下限
     * @see com.yingfei.entity.enums.StatisticalViolationTypeEnum
     */
    private Integer F_RULE_TYPE = 0;

    /**
     * 用于进行报警判断的子组数
     */
    private Integer F_COUNT = 0;

    /**
     * 满足报警规则的子组数
     */
    private Integer F_HITS = 0;

    /**
     * 报警缩写（控制图、事件报告等图表上显示此字段）
     */
    private String F_ABBR = "";

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 满足报警是否将子组失效(0:不失效 1:失效)
     */
    private Integer F_FLAG = 0;

    public static RULE_INF init() {
        RULE_INF data = new RULE_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}