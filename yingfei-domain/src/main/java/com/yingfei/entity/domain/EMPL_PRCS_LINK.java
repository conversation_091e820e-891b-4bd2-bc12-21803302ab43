package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 账户过程关联表
 * @TableName EMPL_PRCS_LINK
 */
@TableName(value ="EMPL_PRCS_LINK")
@Data
public class EMPL_PRCS_LINK extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPR;

    /**
     * 账户表主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPL;

    /**
     * 过程表主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;

    private Integer F_DEL = YesOrNoEnum.NO.getType();

    public static EMPL_PRCS_LINK init() {
        EMPL_PRCS_LINK data = new EMPL_PRCS_LINK();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}