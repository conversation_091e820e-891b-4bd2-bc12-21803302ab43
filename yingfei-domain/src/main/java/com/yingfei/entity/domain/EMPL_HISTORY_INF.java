package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 账户密码变更历史表
 * @TableName EMPL_HISTORY_INF
 */
@TableName(value ="EMPL_HISTORY_INF")
@Data
public class EMPL_HISTORY_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPH;

    /**
     * 账户信息主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPL;

    /**
     * 修改前密码
     */
    private String F_OPWD;

}