package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 菜单表
 * @TableName MENU_INF
 */
@TableName(value ="MENU_INF")
@Data
public class MENU_INF extends BaseEntity {
    /**
     * 菜单id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MENU;

    /**
     * 菜单名称
     */
    private String F_NAME;

    /**
     * 菜单顺序
     */
    private Integer F_ORDER = 0;

    /**
     * 菜单路由
     */
    private String F_PATH;

    /**
     * 菜单类型(0目录 1菜单 2按钮)
     */
    private Integer F_TYPE = 1;

    /**
     * 自定义菜单几乘几
     */
    private String F_DES;

    /**
     * 父级菜单id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PARENT = 0L;

    /**
     * 页面唯一标识
     */
    private String F_CODE;

    /**
     * 权限标识
     */
    private String F_PERMS = "";

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 页面类型(0:系统定义,1:聚合分析,2:单向分析,3:未定义)
     */
    private Integer F_PAGE = 0;

    public static MENU_INF init() {
        MENU_INF data = new MENU_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}