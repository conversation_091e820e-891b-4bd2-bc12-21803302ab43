package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

@TableName(value = "EMPL_VISIT_INF")
@Data
public class EMPL_VISIT_INF extends BaseEntity{

    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ID;

    /**
     * 菜单id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MENU = 0L;

    /**
     * 菜单名称
     */
    private String F_NAME;

    /**
     * F_PATH
     */
    private String F_PATH;

    /**
     * 登录用户id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPL;

    /**
     * 菜单icon
     */
    private String F_ICON;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    @TableField(exist = false)
    private Integer pageType;

    public static EMPL_VISIT_INF init() {
        EMPL_VISIT_INF data = new EMPL_VISIT_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
