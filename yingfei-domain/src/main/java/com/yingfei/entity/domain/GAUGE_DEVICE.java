package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 量具设备配置表
 * @TableName GAUGE_DEVICE
 */
@TableName(value ="GAUGE_DEVICE")
@Data
public class GAUGE_DEVICE extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GADE;

    /**
     * 描述
     */
    private String F_NAME;

    /**
     * 量具连接参数表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GICP;

    /**
     * 量具解析规则配置表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GAFO;

    /**
     * 配置json
     */
    private String F_CONFIG;

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

}