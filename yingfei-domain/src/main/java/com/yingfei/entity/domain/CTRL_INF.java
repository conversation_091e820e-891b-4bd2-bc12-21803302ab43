package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

import java.util.Date;

/**
 * 储存控制限表
 * @TableName CTRL_INF
 */
@TableName(value ="CTRL_INF")
@Data
public class CTRL_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CTRL;

    /**
     * 产品ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;

    /**
     * 测试ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;

    /**
     * 过程ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;

    /**
     * 生效日期
     */
    private Date F_EFTM;

    /**
     * 过程均值
     */
    private Double F_MEAN;

    /**
     * 过程西格玛
     */
    private Double F_SP = 0d;

    /**
     * 公差下限
     */
    private Double F_SPL = 0d;

    /**
     * 合理上限
     */
    private Double F_SW = 0d;

    /**
     * 合理下限
     */
    private Double F_SWL = 0d;

    /**
     * 因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 处理方式模板对应ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PSTP;

    /**
     * 报警规则模板对应ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ARTP;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录最后编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 1.单值-移动极差,  备注: 单值是图表1  移动极差是图表2
     * 2.单值-移动极差-极差件内,
     * 3.单值-移动极差-标准差件内,
     * 4.均值-极差,
     * 5.均值-极差-极差件内,
     * 6.均值-极差-标准差件内,
     * 7.均值-标准差,
     * 8.均值-标准差-极差件内,
     * 9.均值-标准差-标准差件内,
     * 10.U图,
     * 11.C图
     * 12.P图
     * 13.NP图
     */
    private Integer F_CHART_TYPE = 4;

    /**
     * 产品版本id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PTRV;

    public static CTRL_INF init() {
        CTRL_INF ctrlInf = new CTRL_INF();
        BeanUtils.setAllFieldsToNull(ctrlInf);
        return ctrlInf;
    }
}