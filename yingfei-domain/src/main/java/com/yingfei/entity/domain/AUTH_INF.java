package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName(value ="AUTH_INF")
@Data
public class AUTH_INF {

    @TableId(type = IdType.ASSIGN_ID)
    private String F_AUTH;

    @ApiModelProperty("用户机器唯一编码")
    private String F_CODE;

}
