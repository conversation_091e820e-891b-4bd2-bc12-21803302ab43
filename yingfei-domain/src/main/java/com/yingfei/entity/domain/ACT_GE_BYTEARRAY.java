package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName ACT_GE_BYTEARRAY
 */
@TableName(value ="ACT_GE_BYTEARRAY")
@Data
public class ACT_GE_BYTEARRAY {
    /**
     * 
     */
    @TableId
    private String ID_;

    /**
     * 
     */
    private Integer REV_;

    /**
     * 
     */
    private String NAME_;

    /**
     * 
     */
    private String DEPLOYMENT_ID_;

    /**
     * 
     */
    private byte[] BYTES_;

    /**
     * 
     */
    private Integer GENERATED_;

    /**
     * 
     */
    private String TENANT_ID_;

    /**
     * 
     */
    private Integer TYPE_;

    /**
     * 
     */
    private Date CREATE_TIME_;

    /**
     * 
     */
    private String ROOT_PROC_INST_ID_;

    /**
     * 
     */
    private Date REMOVAL_TIME_;
}