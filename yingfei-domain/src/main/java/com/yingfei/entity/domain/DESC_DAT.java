package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存自定义描述符信息表
 * @TableName DESC_DAT
 */
@TableName(value ="DESC_DAT")
@Data
public class DESC_DAT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DESC;

    /**
     * 自定义描述符所关联的自定义描述符组ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DSGP = 0L;

    /**
     * 自定义描述符名称
     */
    private String F_NAME;

    /**
     * 自定义描述符因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;


    public static DESC_DAT init() {
        DESC_DAT data = new DESC_DAT();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }

}