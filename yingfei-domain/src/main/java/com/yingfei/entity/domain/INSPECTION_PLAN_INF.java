package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 检查计划表
 * @TableName INSPECTION_PLAN_INF
 */
@TableName(value ="INSPECTION_PLAN_INF")
@Data
public class INSPECTION_PLAN_INF extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLAN;

    /**
     * 流程结构主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MFPS;

    /**
     * 节点表主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MFND;

    /**
     * 计划名称  在整个工艺节点中必须唯一
     */
    private String F_NAME;

    /**
     * 最大样本量
     */
    private Integer F_SBNO;

    /**
     * 主体数据
     */
    private String F_DATA;

    /**
     * 子计划数据
     */
    private String F_CHILD;

    /**
     * 创建用户
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 编辑用户
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 子计划数量
     */
    private Integer F_COUNT;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 上传图片
     */
    private String F_IMG;

    /**
     * 用户id
     */
    private String F_EMPL;

    /**
     * 角色id
     */
    private String F_ROLE;

    /**
     * 批次是否开启(0:关闭  1:开启)
     */
    private Integer F_LOT_STATUS = YesOrNoEnum.NO.getType();

    /**
     * 检验计划类型(0:静态,1:动态)
     */
    private Integer F_TYPE = 0;

    public static INSPECTION_PLAN_INF init() {
        INSPECTION_PLAN_INF data = new INSPECTION_PLAN_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}