package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

import java.util.Date;

/**
 * 子组主信息缓存表
 * @TableName SGRP_INF_A
 */
@TableName(value ="SGRP_INF_A")
@Data
public class SGRP_INF_A extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SGRP;

    /**
     * 工艺流程ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MFPS;

    /**
     * 工艺节点ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MFND;

    /**
     * 产品ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;

    /**
     * 过程ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;

    /**
     * 产品版本ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_REV;

    /**
     * 产品批次
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_LOT = 0L;

    /**
     * 工作ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_JOB = 0L;

    /**
     * 班次ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SHIFT = 0L;

    /**
     * 子组样本量
     */
    private Integer F_SGSZ;

    /**
     * 子组时间
     */
    private Date F_SGTM;

    /**
     * 失效标识 0=激活，1=失效
     */
    private Integer F_FLAG = YesOrNoEnum.NO.getType();

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 创建用户
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 编辑用户
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 子计划数量
     */
    private Integer F_NUM = 1;

    /**
     * 检查计划id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_INSP_PLAN;

    /**
     * 子组报警监控状态(0:未完成 1:已完成)
     */
    private Integer F_STATUS = 0;

    /**
     * 抽样唯一标识
     */
    private String F_SAMPLE_ID = "0";

    /**
     * 子计划是否完成状态(0:未完成 1:已完成)
     */
    private Integer F_FINISH_STATUS = 1;

    /**
     * 工厂id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLNT = 0L;

    /**
     * 检验类型id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_INSPECTION_TYPE = 0L;

    public static SGRP_INF_A init() {
        SGRP_INF_A data = new SGRP_INF_A();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}