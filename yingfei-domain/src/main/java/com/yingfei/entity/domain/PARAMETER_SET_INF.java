package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

import java.util.Date;

/**
 * 参数集表
 *
 * @TableName PARAMETER_SET_INF
 */
@TableName(value = "PARAMETER_SET_INF")
@Data
public class PARAMETER_SET_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRST;

    /**
     * 参数集名称
     */
    private String F_NAME;

    /**
     * 筛选条件json   List类型
     * 具体字段如下:
     *
     * @see com.yingfei.entity.dto.PARAMETER_CHILD_DTO
     */
    private String F_DATA_SET;


    /**
     * 时间窗口类型。1=动态 2=静态
     */
    private Integer F_TIME_WINDOW_TYPE = 1;

    /**
     * 日期范围类型。1=分钟，2=小时，3=天，4=周，5=月，6=年
     */
    private Integer F_DATERANGE_TYPE = 3;

    /**
     * 日期范围值
     */
    private Integer F_RANGE_INTERVAL;

    /**
     * 开始时间（仅当选择静态窗口时使用此值）
     */
    private Date F_START_DATE;

    /**
     * 结束时间（仅当选择静态窗口时使用此值）
     */
    private Date F_END_DATE;

    /**
     * 最大返回子组数
     */
    private Integer F_MAX_ITEM = 1000;

    /**
     * 是否包含失效的子组 1=不包含，0=包含，默认值为1
     */
    private Integer F_EXCLUDE_DISABLED_SGS = YesOrNoEnum.YES.getType();

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录最后编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    public static PARAMETER_SET_INF init() {
        PARAMETER_SET_INF data = new PARAMETER_SET_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}