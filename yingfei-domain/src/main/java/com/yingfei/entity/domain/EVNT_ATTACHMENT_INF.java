package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 报警信息附件表
 * @TableName EVNT_ATTACHMENT_INF
 */
@TableName(value ="EVNT_ATTACHMENT_INF")
@Accessors(chain = true)
@Data
public class EVNT_ATTACHMENT_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EVAI;

    /**
     * 关联报警信息主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EVNT;

    /**
     * 附件地址
     */
    private String F_ATT;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 类型(0:整体 1:原因  2:措施)
     */
    private Integer F_TYPE = 0;

    public static EVNT_ATTACHMENT_INF init() {
        EVNT_ATTACHMENT_INF data = new EVNT_ATTACHMENT_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}