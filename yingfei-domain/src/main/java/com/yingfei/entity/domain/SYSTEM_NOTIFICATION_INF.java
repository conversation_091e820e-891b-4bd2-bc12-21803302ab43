package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户系统消息表
 */
@Data
@ApiModel
@TableName(value ="SYSTEM_NOTIFICATION_INF")
public class SYSTEM_NOTIFICATION_INF extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SYNO;

    /**
     * 报警消息
     * @see com.yingfei.entity.dto.SYSTEM_NOTIFICATION_ALARM_DTO
     */
    @ApiModelProperty("系统消息")
    private String F_DATA;

    @ApiModelProperty("消息状态(0:未读 1:已读)")
    private Integer F_STATUS = 0;

    @ApiModelProperty("通知人")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPL;


    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    @ApiModelProperty("消息类型(1:普通消息 2:报警消息)")
    private Integer F_TYPE = 1;

    public static SYSTEM_NOTIFICATION_INF init() {
        SYSTEM_NOTIFICATION_INF data = new SYSTEM_NOTIFICATION_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
