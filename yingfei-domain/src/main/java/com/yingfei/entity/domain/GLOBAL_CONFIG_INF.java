package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("GLOBAL_CONFIG_INF")
public class GLOBAL_CONFIG_INF extends BaseEntity{
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty("记录主键")
    private Long F_GC;

    @ApiModelProperty("配置信息")
    private String F_DATA;

    @ApiModelProperty("模板名称")
    @JsonProperty("F_NAME")
    private String F_NAME;

    @ApiModelProperty("模板描述")
    @JsonProperty("F_DESC")
    private String F_DESC;

    @ApiModelProperty("所属人员ID 0:全局")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPL = 0L;

    @ApiModelProperty("数据类型 0:全局配置 1:控制图配置")
    private Integer F_TYPE = 0;

    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    @ApiModelProperty("删除标识 0:未删除 1:已删除")
    private Integer F_DEL = DelFlagEnum.USE.getType();

    public static GLOBAL_CONFIG_INF init() {
        GLOBAL_CONFIG_INF data = new GLOBAL_CONFIG_INF();
        data.setF_DEL(YesOrNoEnum.NO.getType());
        return data;
    }
}
