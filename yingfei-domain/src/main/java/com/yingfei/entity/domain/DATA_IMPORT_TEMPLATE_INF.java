package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.entity.dto.autoCollect.DataFileAutoCollectConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据文件解析模板表
 *
 * @TableName DATA_IMPORT_TEMPLATE_INF
 */
@TableName(value = "DATA_IMPORT_TEMPLATE_INF")
@Data
public class DATA_IMPORT_TEMPLATE_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ID;

    /**
     * 数据文件字段映射
     * @see DataFileAutoCollectConfigDTO
     */
    private String F_DATA;

    /**
     * 规则名称
     */
    private String F_NAME;

    /**
     * 工厂
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLNT;

    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    @ApiModelProperty("1:数据导入规则（历史数据导入）  2:数据采集解析规则")
    private Integer F_TYPE = 2;

    @ApiModelProperty("删除状态 0：未删除 1：已删除")
    private Integer F_DEL = YesOrNoEnum.NO.getType();


    @ApiModelProperty("0:不允许覆盖  1:允许覆盖")
    @TableField(exist = false)
    private Integer isSameName = 0;

    public static DATA_IMPORT_TEMPLATE_INF init() {
        DATA_IMPORT_TEMPLATE_INF data = new DATA_IMPORT_TEMPLATE_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
