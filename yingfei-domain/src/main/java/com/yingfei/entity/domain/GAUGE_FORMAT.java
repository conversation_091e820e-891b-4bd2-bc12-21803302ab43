package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 量具解析规则配置表
 * @TableName GAUGE_FORMAT
 */
@TableName(value ="GAUGE_FORMAT")
@Data
public class GAUGE_FORMAT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_GAFO;

    /**
     * 解析规则名称
     */
    private String F_NAME;

    /**
     * 记录长度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer F_LENGTH;

    /**
     * 记录启动符
     */
    private String F_START;

    /**
     * 记录终结符
     */
    private String F_END;

    /**
     * 记录分隔符
     */
    private String F_SPLIT;

    /**
     * 读取数据配置json
     */
    private String F_DATA_CONFIG;

    /**
     * 测量值返回类型(0:A值  1:B值  2:差额(A-B)  3:均值(A,B)  4: 求和(A+B))
     */
    private Integer F_RETURN_TYPE = 0;

    /**
     * 高级配置
     */
    private String F_ADVANCED;

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

}
