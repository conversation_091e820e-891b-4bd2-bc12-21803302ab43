package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 用户菜单收藏表
 * @TableName EMPL_FAVORITE_INF
 */
@TableName(value ="EMPL_FAVORITE_INF")
@Data
public class EMPL_FAVORITE_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ID;

    /**
     * 保存用户id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EMPL_ID;

    /**
     * 菜单ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MENU_ID;

    /**
     * 记录创建用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    public static EMPL_FAVORITE_INF init() {
        EMPL_FAVORITE_INF data = new EMPL_FAVORITE_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}