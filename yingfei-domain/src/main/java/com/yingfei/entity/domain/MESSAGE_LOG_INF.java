package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 消息发送日志表
 * @TableName MESSAGE_LOG_INF
 */
@TableName(value ="MESSAGE_LOG_INF")
@Data
public class MESSAGE_LOG_INF extends BaseEntity {
    /**
     * mq消息id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_NEWS;

    /**
     * 消息内容
     */
    private String F_DATA;

    /**
     * 发送状态(0:发送中 1:发送成功 2:发送失败 3:入库成功  4:入库失败)
     */
    private Integer F_STATUS = 0;

    /**
     * 创建人
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;

    /**
     * 类型(0:入库  1:缓存  2:数据监控 3数据采集) 默认0
     */
    private Integer F_TYPE = 0;

    public static MESSAGE_LOG_INF init() {
        MESSAGE_LOG_INF data = new MESSAGE_LOG_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}