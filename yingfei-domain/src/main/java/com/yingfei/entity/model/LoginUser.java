package com.yingfei.entity.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import lombok.Data;

import java.util.Set;

/**
 * 用户信息
 *
 *
 */
@Data
public class LoginUser
{
    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 用户名id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userid;

    /**
     * 用户名
     */
    private String username;

    /**
     * 加密密码
     */
    private String password;

    /**
     * @see com.yingfei.common.core.enums.LoginTypeEnum
     * 登录类型
     */
    private Integer loginType;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 角色列表
     */
    private Set<String> roles;

    /**
     * 用户信息
     */
    private EMPL_INF_DTO sysUser;



}
