package com.yingfei.entity.enums;

import lombok.Getter;

@Getter
public enum HIERARCHY_INFTypeEnum {
    /**
     * 类型(0:集团 1:事业部  2:工厂  3:部门  4:车间)
     */
    GROUP(0, "集团"),
    BUSINESS_DIVISION(1, "事业部"),
    FACTORY(2, "工厂"),
    DEPARTMENT(3, "部门"),
    WORKSHOP(4, "车间"),
    ;


    private final Integer type;
    private final String desc;

    HIERARCHY_INFTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static HIERARCHY_INFTypeEnum getType(Integer type) {
        for (HIERARCHY_INFTypeEnum e : HIERARCHY_INFTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return null;
    }
}
