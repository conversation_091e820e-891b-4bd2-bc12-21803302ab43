package com.yingfei.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * BPM 任务分配规则的类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BpmTaskAssignRuleTypeEnum {

    ROLE(1, "角色"),
    USER(2, "用户"),
    HIERARCHY(3, "层级"),
    ;

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 描述
     */
    private final String desc;

    public static Map<String, String> getMap() {
        HashMap<String, String> map = new HashMap<>();
        for (BpmTaskAssignRuleTypeEnum value : BpmTaskAssignRuleTypeEnum.values()) {
            map.put(value.type.toString(), value.desc);
        }
        return map;
    }

    public static BpmTaskAssignRuleTypeEnum getType(Integer type) {
        for (BpmTaskAssignRuleTypeEnum e : BpmTaskAssignRuleTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return BpmTaskAssignRuleTypeEnum.USER;
    }
}
