package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据库采集dcs配置 评估类型
 */
@Getter
public enum EvaluationTypeEnum {

    /**
     * 新建
     * 小于
     * 小于或等于
     * 等于
     * 不等于
     * 大于或等于
     * 大于
     * 空
     */
    NEW(0, "新建"),
    LESS(1, "小于(<)"),
    LESS_OR_EQUAL(2, "小于或等于(<=)"),
    EQUAL(3, "等于(=)"),
    NOT_EQUAL(4, "不等于(<>)"),
    GREATER_OR_EQUAL(5, "大于或等于(>=)"),
    GREATER(6, "大于(>)"),
    NULL(7, "空"),
    START(8, "以...开始"),
    END(9, "以...结束"),
    CONTAIN(10, "包含"),
    ;

    private Integer type;
    private String desc;

    EvaluationTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static EvaluationTypeEnum getType(Integer type) {
        for (EvaluationTypeEnum e : EvaluationTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return EvaluationTypeEnum.NEW;
    }

    /**
     * 获取评估类型枚举
     * type 1:数据排除评估类型  2:保存条件评估类型
     */
    public static Map<Integer, String> getEvaluationMap(Integer type) {
        HashMap<Integer, String> map = new HashMap<>();
        for (EvaluationTypeEnum e : EvaluationTypeEnum.values()) {
            if (type == 1) {
                if (e.getType() == 0 || e.getType() == 7) continue;
            }
            map.put(e.getType(), e.getDesc());
        }
        return map;
    }

    /**
     * 获取跳过行条件枚举
     *
     * @return
     */
    public static Map<Integer, String> getSkipLineMap() {
        HashMap<Integer, String> map = new HashMap<>();
        for (EvaluationTypeEnum e : EvaluationTypeEnum.values()) {
            if (e.getType() == 8 || e.getType() == 9 || e.getType() == 10)
                map.put(e.getType(), e.getDesc());
        }
        return map;
    }
}
