package com.yingfei.entity.enums;

import com.yingfei.common.core.enums.LanguageEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.entity.domain.DICT_INF;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 箱线图分析角度类型
 */
@Getter
public enum BoxPlotsAnalyseTypeEnum {
    /**
     * 班次
     * 班次组
     * 测试
     * 测试编号
     * 测试组
     * 产品
     * 产品组
     * 工作
     * 工作组
     * 过程
     * 过程组
     * 描述符(选择后选择对应描述符组)
     * 批次
     * 时间(秒,分钟,小时,天数,周,月,季度)
     * 员工
     */
    SHIFT_DAT(1, "班次"),
    SHIFT_GRP(2, "班次组"),
    TEST_DAT(3, "测试"),
    TEST_NO(4, "样本编号"),
    PART_DAT(5, "产品"),
    JOB_DAT(6, "工作"),
    JOB_GRP(7, "工作组"),
    PRCS_DAT(8, "过程"),
    DESC_DAT(9, "描述符"),
    LOT_DAT(10, "批次"),
    TIME(11, "时间"),
    STAFF(12, "员工"),
    INSPECTION_TYPE(13, "检验类型"),
    PTRV_DAT(14, "产品版本"),
    ;

    final Integer code;
    final String describe;

    BoxPlotsAnalyseTypeEnum(int code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static BoxPlotsAnalyseTypeEnum getByCode(int code) {

        for (BoxPlotsAnalyseTypeEnum boxPlotsAnalyseTypeEnum : BoxPlotsAnalyseTypeEnum.values()) {
            if (boxPlotsAnalyseTypeEnum.code == code) {
                return boxPlotsAnalyseTypeEnum;
            }
        }
        return TEST_DAT;
    }

    public static Map<String, String> getMap(Map<String, List<DICT_INF>> dictInfMap) {
        Map<String, String> map = new LinkedHashMap<>();
        for (BoxPlotsAnalyseTypeEnum value : BoxPlotsAnalyseTypeEnum.values()) {
            List<DICT_INF> dictInfList = dictInfMap.get(value.name());
            if (CollectionUtils.isEmpty(dictInfList)) {
                map.put(value.code.toString(), value.describe);
            } else {
                LanguageEnum language = JudgeUtils.getLanguage();
                for (DICT_INF dictInf : dictInfList) {
                    if (language.getIndex() == dictInf.getF_TYPE()){
                        map.put(value.code.toString(), dictInf.getF_CONTENT());
                        break;
                    }
                }
            }
        }
        return map;
    }
}
