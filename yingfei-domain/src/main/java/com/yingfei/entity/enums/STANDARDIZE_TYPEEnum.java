package com.yingfei.entity.enums;

import com.yingfei.entity.dto.CalculatedControlLimit;
import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.util.CorrectionConstantUtil;
import com.yingfei.entity.vo.ControlLimitVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据处理标准化类型枚举
 */
@Getter
@AllArgsConstructor
public enum STANDARDIZE_TYPEEnum {

    /**
     * 标准化类型 1=无 2=目标值 3=名义值 4=过程均值 5=标准化，默认值为1
     */
    NULL(1, "无"),

    TARGET_VAL(2, "目标值"),

    NOMINAL_VAL(3, "名义值"),

    PROCESS_MEAN(4, "过程均值"),

    STANDARDIZATION(5, "标准化"),
    ;

    public final Integer type;
    public final String desc;

    public static STANDARDIZE_TYPEEnum getType(Integer type) {
        for (STANDARDIZE_TYPEEnum e : STANDARDIZE_TYPEEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return STANDARDIZE_TYPEEnum.NULL;
    }

    /**
     * 根据标准化类型对上述值进行二次处理：
     * ---目标值：
     * ----把均值的三个界限（UCL，LCL，CL）都减去目标值-均值：UCL: （UCL-TAR） LCL:(LCL-TAR）CL:(CL-TAR）
     * ----极差不用做处理
     * ----把子组均值减去目标值：（2.8667-TAR）
     * ---名义值：(USL+LSL)/2
     * ----把均值的三个界限（UCL，LCL，CL）都减去名义值-均值：UCL: （UCL-名义值） （LCL:LCL-名义值）（ CL:CL-名义值）
     * ----极差不用做处理
     * ----把子组均值减去名义值：子组均值-名义值）
     * ---过程均值：控制限中定义的均值CL
     * ----把均值的三个界限（UCL，LCL，CL）都减去过程均值-均值：UCL: （UCL-过程均值） （LCL:LCL-过程均值）（ CL:CL-过程均值）
     * ----极差不用做处理
     * ----把子组均值减去过程均值：（子组均值-过程均值）
     * ---标准化：
     * ----把均值的三个界限（UCL，LCL，CL）：UCL: 3 LCL:-3 CL: 0
     * ----极差不用做处理
     * ----把子组均值减去目标值：（2.8667-TAR）
     * <p>
     * 数据监控标准化
     *
     * @param controlLimitDto
     * @param type
     */
    public static void dispose(ControlLimitDTO controlLimitDto, CalculatedControlLimit calculatedControlLimit, STANDARDIZE_TYPEEnum type, ControlChartSingleEnum chartSingleEnum) {
        /*当图表类型是P,U,C,NP 并且 标准化类型 不是 标准化时 直接返回*/
        if ((ControlChartSingleEnum.CHART_GROUP_TYPE_P.equals(chartSingleEnum) ||
                ControlChartSingleEnum.CHART_GROUP_TYPE_U.equals(chartSingleEnum) ||
                ControlChartSingleEnum.CHART_GROUP_TYPE_C.equals(chartSingleEnum) ||
                ControlChartSingleEnum.CHART_GROUP_TYPE_NP.equals(chartSingleEnum)) &&
                !STANDARDIZE_TYPEEnum.STANDARDIZATION.equals(type))
            return;
        switch (type) {
            case TARGET_VAL:
                if (controlLimitDto.getSpecInfDto() == null) break;
                if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_IX || chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_X) {
                    calculatedControlLimit.setCL(calculatedControlLimit.getCL() - controlLimitDto.getSpecInfDto().getF_TAR());
                    calculatedControlLimit.setUCL(calculatedControlLimit.getUCL() - controlLimitDto.getSpecInfDto().getF_TAR());
                    calculatedControlLimit.setLCL(calculatedControlLimit.getLCL() - controlLimitDto.getSpecInfDto().getF_TAR());
                    controlLimitDto.setNowMean(controlLimitDto.getNowMean() - controlLimitDto.getSpecInfDto().getF_TAR());
                }
                break;
            case NOMINAL_VAL:
                if (controlLimitDto.getSpecInfDto() == null) break;
                if (controlLimitDto.getSpecInfDto().getF_USL() == null || controlLimitDto.getSpecInfDto().getF_LSL() == null)
                    break;
                if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_IX || chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_X) {
                    double v = (controlLimitDto.getSpecInfDto().getF_USL() + controlLimitDto.getSpecInfDto().getF_LSL()) / 2;
                    calculatedControlLimit.setCL(calculatedControlLimit.getCL() - v);
                    calculatedControlLimit.setUCL(calculatedControlLimit.getUCL() - v);
                    calculatedControlLimit.setLCL(calculatedControlLimit.getLCL() - v);
                    controlLimitDto.setNowMean(controlLimitDto.getNowMean() - v);
                }
                break;
            case PROCESS_MEAN:
                if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_IX || chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_X) {
                    calculatedControlLimit.setCL(calculatedControlLimit.getCL() - controlLimitDto.getF_MEAN());
                    calculatedControlLimit.setUCL(calculatedControlLimit.getUCL() - controlLimitDto.getF_MEAN());
                    calculatedControlLimit.setLCL(calculatedControlLimit.getLCL() - controlLimitDto.getF_MEAN());
                    controlLimitDto.setNowMean(controlLimitDto.getNowMean() - controlLimitDto.getF_MEAN());
                }
                break;
            case STANDARDIZATION:

                switch (chartSingleEnum) {
                    case CHART_GROUP_TYPE_X:
                        /*当前值(均值)-控制限均值*/
                        double v1 = controlLimitDto.getNowMean() - controlLimitDto.getF_MEAN();
                        /*短期标准差*/
                        double v2 = controlLimitDto.getF_SP() / Math.sqrt(controlLimitDto.getN());
                        if (v2 == 0d) {
                            controlLimitDto.setNowMean(0d);
                        } else {
                            controlLimitDto.setNowMean(v1 / v2);
                        }
                        break;
                    case CHART_GROUP_TYPE_RANGE:
                        /*极差图中心线值*/
                        double RBar = CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_D2) * controlLimitDto.getF_SP();
                        double v3 = (calculatedControlLimit.getUCL() - RBar) / 3;
                        double v4 = (controlLimitDto.getNowR() - RBar) / v3;
                        controlLimitDto.setNowR(v4);
                        break;
                    case CHART_GROUP_TYPE_SD:
                        /*标准差图中心线值*/
                        double SBar = CorrectionConstantUtil.getCorrectionData(controlLimitDto.getN(), CorrectionConstantEnum.CONSTANT_C4) * controlLimitDto.getF_SP();
                        double v5 = (calculatedControlLimit.getUCL() - SBar) / 3;
                        double v6 = (controlLimitDto.getNowSD() - SBar) / v5;
                        controlLimitDto.setNowSD(v6);
                        break;
                    case CHART_GROUP_TYPE_MR:
                        if (Double.isNaN(controlLimitDto.getNowMR())) {
                            break;
                        }
                        if (controlLimitDto.getF_SP() == 0d) {
                            controlLimitDto.setNowMR(0d);
                        } else {
                            controlLimitDto.setNowMR(controlLimitDto.getNowMR() / controlLimitDto.getF_SP());
                        }
                        break;
                    case CHART_GROUP_TYPE_IX:
                        if (controlLimitDto.getF_SP() == 0d) {
                            controlLimitDto.setNowMean(0d);
                        } else {
                            /*当前值(均值)-控制限均值*/
                            double v9 = (controlLimitDto.getNowMean() - controlLimitDto.getF_MEAN());
                            controlLimitDto.setNowMean(v9 / controlLimitDto.getF_SP());
                        }
                        break;
                    case CHART_GROUP_TYPE_RW:
                    case CHART_GROUP_TYPE_SDW:
                    case CHART_GROUP_TYPE_P:
                    case CHART_GROUP_TYPE_U:
                    case CHART_GROUP_TYPE_C:
                    case CHART_GROUP_TYPE_NP:
                        //todo 后续完善
                }
                if (chartSingleEnum.equals(ControlChartSingleEnum.CHART_GROUP_TYPE_MR)) {
                    calculatedControlLimit.setCL(1.128d);
                    calculatedControlLimit.setUCL(3.6855d);
                    calculatedControlLimit.setLCL(0d);
                } else {
                    calculatedControlLimit.setCL(0d);
                    calculatedControlLimit.setUCL(3d);
                    calculatedControlLimit.setLCL(-3d);
                }
        }
    }

    /**
     * 控制图标准化
     */
    public static void dispose(ControlLimitDTO controlLimitDto, ControlLimitVO controlLimitVO , STANDARDIZE_TYPEEnum type, ControlChartSingleEnum chartSingleEnum) {
        /*当图表类型是P,U,C,NP 并且 标准化类型 不是 标准化时 直接返回*/
        if ((ControlChartSingleEnum.CHART_GROUP_TYPE_P.equals(chartSingleEnum) ||
                ControlChartSingleEnum.CHART_GROUP_TYPE_U.equals(chartSingleEnum) ||
                ControlChartSingleEnum.CHART_GROUP_TYPE_C.equals(chartSingleEnum) ||
                ControlChartSingleEnum.CHART_GROUP_TYPE_NP.equals(chartSingleEnum)) &&
                !STANDARDIZE_TYPEEnum.STANDARDIZATION.equals(type))
            return;
        switch (type) {
            case TARGET_VAL:
                if (controlLimitDto.getSpecInfDto() == null) break;
                if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_IX || chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_X) {
                    controlLimitVO.setCl(controlLimitVO.getCl() - controlLimitDto.getSpecInfDto().getF_TAR());
                    controlLimitVO.setUcl(controlLimitVO.getUcl() - controlLimitDto.getSpecInfDto().getF_TAR());
                    controlLimitVO.setLcl(controlLimitVO.getLcl() - controlLimitDto.getSpecInfDto().getF_TAR());
                }
                break;
            case NOMINAL_VAL:
                if (controlLimitDto.getSpecInfDto() == null) break;
                if (controlLimitDto.getSpecInfDto().getF_USL() == null || controlLimitDto.getSpecInfDto().getF_LSL() == null)
                    break;
                if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_IX || chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_X) {
                    double v = (controlLimitDto.getSpecInfDto().getF_USL() + controlLimitDto.getSpecInfDto().getF_LSL()) / 2;
                    controlLimitVO.setCl(controlLimitVO.getCl() - v);
                    controlLimitVO.setUcl(controlLimitVO.getUcl() - v);
                    controlLimitVO.setLcl(controlLimitVO.getLcl() - v);
                }
                break;
            case PROCESS_MEAN:
                if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_IX || chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_X) {
                    controlLimitVO.setCl(controlLimitVO.getCl() - controlLimitDto.getF_MEAN());
                    controlLimitVO.setUcl(controlLimitVO.getUcl() - controlLimitDto.getF_MEAN());
                    controlLimitVO.setLcl(controlLimitVO.getLcl() - controlLimitDto.getF_MEAN());
                }
                break;
            case STANDARDIZATION:
                if (chartSingleEnum.equals(ControlChartSingleEnum.CHART_GROUP_TYPE_MR)) {
                    controlLimitVO.setCl(1.128d);
                    controlLimitVO.setUcl(3.6855d);
                    controlLimitVO.setLcl(0d);
                } else {
                    controlLimitVO.setCl(0d);
                    controlLimitVO.setUcl(3d);
                    controlLimitVO.setLcl(-3d);
                }
        }
    }
}
