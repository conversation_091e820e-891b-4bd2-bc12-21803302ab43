package com.yingfei.entity.enums;

import lombok.Getter;

@Getter
public enum PARAMETER_CHILDTypeEnum {
    /**
     * 类型(0:产品  1:过程  2:测试  3:班次  4:工单  5:自定义描述符)
     */
    PART_DAT(0, "产品"),
    PRCS_DAT(1, "过程"),
    TEST_DAT(2, "测试"),
    SHIFT_DAT(3, "班次"),
    JOB_DAT(4, "工单"),
    DESC_DAT(5, "自定义描述符"),
    WORKSTATION(6, "工作站"),
    STAFF(7, "员工"),
    TEST_NO(8, "测试编号"),
    GAUGE(9, "量具"),
    HIERARCHY(10, "层级"),
    ;


    private final Integer type;
    private final String desc;

    PARAMETER_CHILDTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static PARAMETER_CHILDTypeEnum getType(Integer type) {
        for (PARAMETER_CHILDTypeEnum e : PARAMETER_CHILDTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return PARAMETER_CHILDTypeEnum.PART_DAT;
    }
}
