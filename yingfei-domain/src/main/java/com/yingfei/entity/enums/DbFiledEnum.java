package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 子组采集映射字段枚举
 */
@Getter
public enum DbFiledEnum {

    PART_DAT(1, "产品"),
    PRCS_DAT(2, "过程"),
    SHIFT_DAT(3, "班次"),
    PREV_DAT(4, "版本"),
    JOB_DAT(5, "工单"),
    LOT(6, "批次"),
    TIME(7, "时间"),
    DESC_DAT(8, "描述符"),
    TEST_DAT(9, "测试值"),
    MFPS_DAT(10, "工艺流程"),
    MFND_DAT(11, "工艺节点"),
    PLAN_DAT(12, "检验计划"),
    CHILD_DAT(13, "子计划"),
    /*结构必须包含工艺流程,工艺节点,检验计划,子计划*/
    STRUCTURE(14, "结构"),
    ;

    private final Integer type;
    private final String desc;

    DbFiledEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static DbFiledEnum getType(Integer type) {
        for (DbFiledEnum e : DbFiledEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return DbFiledEnum.PART_DAT;
    }

    public static Map<String, String> getMap() {
        Map<String, String> map = new LinkedHashMap<>();
        for (DbFiledEnum value : DbFiledEnum.values()) {
            map.put(value.desc, String.valueOf(value.type));
        }
        return map;
    }
}
