package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum ALiModelEnum {

    distill_llama_70b(1, "deepseek-r1-distill-llama-70b"),
    distill_qwen_32b(2, "deepseek-r1-distill-qwen-32b"),
    distill_qwen_14b(3, "deepseek-r1-distill-qwen-14b"),
    distill_llama_8b(4, "deepseek-r1-distill-llama-8b"),
    distill_qwen_1_5b(5, "deepseek-r1-distill-qwen-1.5b"),
    distill_qwen_7b(6, "deepseek-r1-distill-qwen-7b"),
    deepseek_r1(7, "deepseek-r1"),
    ;

    final Integer code;
    final String describe;

    ALiModelEnum(int code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static List<String> getMode() {
        List<String> list = new ArrayList<>();
        for (ALiModelEnum e : ALiModelEnum.values()) {
            list.add(e.getDescribe());
        }
        return list;
    }
}
