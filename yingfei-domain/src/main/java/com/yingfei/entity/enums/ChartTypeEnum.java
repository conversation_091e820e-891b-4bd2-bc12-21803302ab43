package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum ChartTypeEnum {

    HISTOGRAM(1, "直方图"),
    CONTROL_CHART(2, "控制图"),
    BOX_PLOTS(3, "多级箱线图"),
    QUADRANTAL_DIAGRAM(4, "象限图"),
    VIOLIN_PLOT(5, "多级小提琴图"),
    PARETO_DIAGRAM(6, "多级帕累托图"),
    SAMPLING_SUMMARY(7, "抽样汇总"),
    INSTRUMENT_BOARD(8, "能力仪表盘"),
    ALARM_RATE(9, "报警率卡片"),
    VIEW_DATA(10,"查看数据"),
    CAPABILITY_TREND(11,"历史能力趋势"),
    EXCEPTION_SUMMARY(12,"异常汇总"),
    LINEAR_REGRESSION(13,"正态概率图"),
    CALENDAR(14,"报警日历图"),
    DATA_REPORT(15,"数据报告"),
    CAPABILITY_MATRIX(16,"能力矩阵"),
    ALARM_TREND(17,"历史报警趋势"),
    REAL_TIME_CAPABILITY_TREND(18,"实时能力趋势"),
    ;

    private Integer type;

    private String name;

    ChartTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static ChartTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (ChartTypeEnum chartTypeEnum : ChartTypeEnum.values()) {
            if (chartTypeEnum.getType().equals(type)) {
                return chartTypeEnum;
            }
        }
        return null;
    }

    public static Map<String, String> getMap() {
        HashMap<String, String> map = new HashMap<>();
        for (ChartTypeEnum value : ChartTypeEnum.values()) {
            map.put(value.type.toString(), value.name);
        }
        return map;
    }
}
