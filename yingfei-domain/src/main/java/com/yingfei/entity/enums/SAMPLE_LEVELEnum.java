package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

@Getter
public enum SAMPLE_LEVELEnum {

    /**
     * 0=General1
     * 1=General2
     * 2=General3
     * 3=Special1
     * 4=Special2
     * 5=Special3
     * 6=Special4
     */
    GENERAL_ONE(0, "General1"),
    GENERAL_TWO(1, "General2"),
    GENERAL_THREE(2, "General3"),
    SPECIAL_ONE(3, "Special1"),
    SPECIAL_TWO(4, "Special2"),
    SPECIAL_THREE(5, "Special3"),
    SPECIAL_FOUR(6, "Special4"),
    ;

    private final Integer type;
    private final String desc;

    SAMPLE_LEVELEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static Map<Integer, String> getMap() {
        Map<Integer, String> map = new LinkedHashMap<>();
        for (SAMPLE_LEVELEnum value : SAMPLE_LEVELEnum.values()) {
            map.put(value.getType(), value.getDesc());
        }
        return map;
    }
}
