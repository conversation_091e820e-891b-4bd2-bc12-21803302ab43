package com.yingfei.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公差限报警枚举
 */
@Getter
@AllArgsConstructor
public enum SpecificationLimitViolation {

    USL(1, ">USL"),
    LSL(2, "<LSL"),
    URL(3, ">URL"),
    LRL(4, "<LRL"),
    UWL(5, ">UWL"),
    LWL(6, "<LWL"),
    UWP(7, ">UWP"),
    LWP(8, "<LWP"),
    UAL(9, ">UAL"),
    LAL(10, "<LAL"),
    ;

    private final int type;
    private final String desc;

    public static List<String> getDescList(List<String> typeList) {
        List<String> arrayList = new ArrayList<>();
        for (String type : typeList) {
            for (SpecificationLimitViolation specificationLimitViolation : SpecificationLimitViolation.values()) {
                if (specificationLimitViolation.getType() == Integer.parseInt(type)) {
                    arrayList.add(specificationLimitViolation.getDesc());
                }
            }
        }
        return arrayList;
    }

    public static Map<Integer, String> getTypeList() {
        Map<Integer, String> map = new HashMap<>();
        for (SpecificationLimitViolation value : SpecificationLimitViolation.values()) {
            map.put(value.getType(),value.getDesc());
        }
        return map;
    }
}
