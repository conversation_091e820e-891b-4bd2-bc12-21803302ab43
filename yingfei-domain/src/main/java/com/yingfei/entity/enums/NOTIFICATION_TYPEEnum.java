package com.yingfei.entity.enums;

import lombok.Getter;

@Getter
public enum NOTIFICATION_TYPEEnum {

    /*1:系统消息通知 2:邮件通知 3:企业微信通知 4:钉钉通知 5:其他通知*/
    SYSTEM_MESSAGE(1, "系统消息通知"),
    EMAIL(2, "邮件通知"),
    QY_WECHAT(3, "企业微信通知"),
    DING_DING(4, "钉钉通知"),
    ;

    private final Integer type;
    private final String desc;

    NOTIFICATION_TYPEEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static NOTIFICATION_TYPEEnum getType(Integer type) {
        for (NOTIFICATION_TYPEEnum e : NOTIFICATION_TYPEEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return NOTIFICATION_TYPEEnum.SYSTEM_MESSAGE;
    }
}
