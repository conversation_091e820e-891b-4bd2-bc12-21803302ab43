package com.yingfei.entity.enums;

import lombok.Getter;

@Getter
public enum TAG_LINKTypeEnum {
    PART_DAT(1, "产品"),
    PRCS_DAT(2, "过程"),
    TEST_DAT(3, "测试"),
    ;

    private final int code;
    private final String description;

    TAG_LINKTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static TAG_LINKTypeEnum getType(Integer type) {
        for (TAG_LINKTypeEnum e : TAG_LINKTypeEnum.values()) {
            if (e.getCode() == type) {
                return e;
            }
        }
        return TAG_LINKTypeEnum.PART_DAT;
    }
}
