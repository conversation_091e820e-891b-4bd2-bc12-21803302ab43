package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum DataValTypeEnum {

    WORKSTATION(1, "工作站"),
    STAFF(2, "员工"),
    PRCS_DAT(3, "过程"),
    PART_DAT(4, "产品"),
    LOT(5, "批次"),
    TEST_NO(6, "测试编号"),
    TEST_DAT(7, "测试"),
    SHIFT_DAT(8, "班次"),
    ;


    private final Integer type;
    private final String desc;

    DataValTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static DataValTypeEnum getType(Integer type) {
        for (DataValTypeEnum e : DataValTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return DataValTypeEnum.PART_DAT;
    }

    public static Map<String, String> getMap() {
        HashMap<String, String> map = new HashMap<>();
        for (DataValTypeEnum value : DataValTypeEnum.values()) {
            map.put(value.type.toString(), value.desc);
        }
        return map;
    }
}
