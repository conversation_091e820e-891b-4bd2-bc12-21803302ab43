package com.yingfei.entity.enums;

import lombok.Getter;

@Getter
public enum LogTitleEnum {

    PART("产品管理","PART_INF"),
    PRCS("过程管理","PRCS_INF"),
    TEST("测试管理","TEST_INF"),
    SHIFT("班次管理","SHIFT_DAT"),
    LOT("产品批次管理","LOT_INF"),
    JOB("工单管理","JOB_DAT"),
    SGRP("子组管理","SGRP_INF"),
    SPEC("公差限管理","SPEC_INF"),
    CTRL("控制限管理","CTRL_INF"),
    EVNT("报警信息","EVNT_INF"),
    PART_REV("产品版本管理","PART_REV"),
    SN("产品序列号管理","SN_INF"),
    PART_TEST("产品测试组合的图片管理","PART_TEST_INF"),
    EMPL_PRCS("账户过程关联信息","EMPL_PRCS_LINK"),
    DESC("自定义描述符管理","DESC_DAT"),
    ;

    private final String title;
    private final String tableName;

    LogTitleEnum(String title,String tableName) {
        this.title = title;
        this.tableName = tableName;
    }

    public static LogTitleEnum getLogTitleEnum(String title) {
        for (LogTitleEnum value : LogTitleEnum.values()) {
            if (value.getTitle().equals(title)) {
                return value;
            }
        }
        return LogTitleEnum.PART;
    }
}
