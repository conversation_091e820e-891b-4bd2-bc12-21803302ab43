package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 抽样汇总分析角度类型
 */
@Getter
public enum SamplingSummaryAnalyseTypeEnum {
    /**
     * 产品
     * 过程
     * 测试
     * 班次
     * 批次
     * 工作
     */
    PART_DAT(1, "产品"),
    PRCS_DAT(2, "过程"),
    TEST_DAT(3, "测试"),
    SHIFT_DAT(4, "班次"),
    LOT_DAT(5, "批次"),
    JOB_DAT(6, "工作"),
    INSPECTION_TYPE(7, "检验类型"),
    PTRV_DAT(8, "产品版本"),
    ;

    final int code;
    final String describe;

    SamplingSummaryAnalyseTypeEnum(int code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static SamplingSummaryAnalyseTypeEnum getByCode(int code) {

        for (SamplingSummaryAnalyseTypeEnum boxPlotsAnalyseTypeEnum : SamplingSummaryAnalyseTypeEnum.values()) {
            if (boxPlotsAnalyseTypeEnum.code == code) {
                return boxPlotsAnalyseTypeEnum;
            }
        }
        return TEST_DAT;
    }

    public static Map<String, String> getMap() {
        Map<String, String> map = new LinkedHashMap<>();
        for (SamplingSummaryAnalyseTypeEnum value : SamplingSummaryAnalyseTypeEnum.values()) {
            map.put(value.describe, String.valueOf(value.code));
        }
        return map;
    }
}
