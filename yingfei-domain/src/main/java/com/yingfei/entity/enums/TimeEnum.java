package com.yingfei.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TimeEnum {
    /**
     * 日期范围类型。1=分钟，2=小时，3=天，4=周，5=月，6=年
     */
    SECOND(0, "秒"),
    MINUTE(1, "分钟"),
    HOUR(2, "小时"),
    DAY(3, "天"),
    WEEK(4, "周"),
    MONTH(5, "月"),
    YEAR(6, "年"),
    QUARTER(7, "季度")
    ;

    private final int type;
    private final String desc;

    public static TimeEnum getType(int type) {
        for (TimeEnum timeEnum : TimeEnum.values()) {
            if (timeEnum.getType() == type) {
                return timeEnum;
            }
        }
        return TimeEnum.DAY;
    }
}
