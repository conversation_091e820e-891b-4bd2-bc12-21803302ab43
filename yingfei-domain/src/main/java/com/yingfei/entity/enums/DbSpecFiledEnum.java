package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 公差限采集映射字段枚举
 */
@Getter
public enum DbSpecFiledEnum {

    PART_DAT(1, "产品"),
    PRCS_DAT(2, "过程"),
    TEST_DAT(3, "测试"),
    PREV_DAT(4, "版本"),
    USL(5, "USL"),
    TAR(6,"目标值"),
    LSL(7, "LSL"),
    URL(8, "URL"),
    LRL(9, "LRL"),
    UWL(10, "UWL"),
    LWL(11, "LWL"),
    UWP(12, "UWP"),
    LWP(13, "LWP"),
    UAL(14, "UAL"),
    LAL(15, "LAL"),
    CP(16,"目标cp"),
    CPK(17,"目标cpk"),
    PP(18,"目标pp"),
    PPK(19,"目标ppk"),
    ;

    private final Integer type;
    private final String desc;

    DbSpecFiledEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static DbSpecFiledEnum getType(Integer type) {
        for (DbSpecFiledEnum e : DbSpecFiledEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return DbSpecFiledEnum.PART_DAT;
    }

    public static Map<String, String> getMap() {
        Map<String, String> map = new LinkedHashMap<>();
        for (DbSpecFiledEnum value : DbSpecFiledEnum.values()) {
            map.put(value.desc, String.valueOf(value.type));
        }
        return map;
    }
}
