package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum ScreenConditionEnum {

    VALUE(1, "取值(评估)"),
    NULL(2, "为空"),
    NOT_NUMBER(3, "不是数字"),
    NOT_DATE(4, "不是日期"),
    LENGTH(5, "长度"),
    ;

    private final Integer type;
    private final String desc;

    ScreenConditionEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ScreenConditionEnum getType(Integer type) {
        for (ScreenConditionEnum e : ScreenConditionEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return ScreenConditionEnum.VALUE;
    }

    public static Map<Integer, String> getScreenConditionMap() {
        HashMap<Integer, String> map = new HashMap<>();
        for (ScreenConditionEnum e : ScreenConditionEnum.values()) {
            map.put(e.getType(), e.getDesc());
        }
        return map;
    }
}
