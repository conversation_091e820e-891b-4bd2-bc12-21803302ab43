package com.yingfei.entity.enums;

import lombok.Getter;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 检查计划通用属性
 */
@Getter
public enum PlanCommonAttributesEnums {
    /**
     * --时间戳：使用采集开始时间，使用子组保存时间，指定时间
     * --样本量：固定样本量，采集开始时指定样本量，采集结束时指定样本量
     * --提示：禁止输入超合理限值，数据超界限时提醒
     * --数据输入：[显示状态，显示测试值]，输入完成后自动保存，存在未处理异常时禁止采集，允许选择多个过程
     */
    COLLECTION_START_TIME("a", "a1", "使用采集开始时间"),
    SUBGROUP_RETENTION_TIME("a", "a2", "使用子组保存时间"),
    SCHEDULE_TIME("a", "a3", "指定时间"),
    FIXED_SAMPLE_SIZE("b", "b1", "固定样本量"),
    START_SPECIFYING_SAMPLE_SIZE("b", "b2", "采集开始时指定样本量"),
    END_SPECIFYING_SAMPLE_SIZE("b", "b3", "采集结束时指定样本量"),
    EXCESSIVE_LIMIT_ALERT("c", "c1", "禁止输入超合理限值"),
    ALERT_WHEN_DATA_IS_OUT_OF_BOUNDS("c", "c2", "数据超界限时提醒"),
    DISPLAY_STATUS("d", "d1", "显示状态"),
    DISPLAY_TEST_VALUE("d", "d2", "显示测试值"),
    COMPLETE_AUTOSAVE("e", "e1", "输入完成后自动保存"),
    NO_COLLECTION("f", "f1", "存在未处理异常时禁止采集"),
    ALLOW_MULTIPLE_PROCESSES("g", "g1", "允许选择多个过程"),
    ;

    private final String group;
    private final String code;
    private final String description;

    PlanCommonAttributesEnums(String group, String code, String description) {
        this.group = group;
        this.code = code;
        this.description = description;
    }

    public static PlanCommonAttributesEnums getType(String code) {
        for (PlanCommonAttributesEnums e : PlanCommonAttributesEnums.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static Map<String, Map<String, String>> getMap() {
        Map<String, Map<String, String>> map = new HashMap<>();
        for (PlanCommonAttributesEnums value : PlanCommonAttributesEnums.values()) {
            Map<String, String> dataMap = map.get(value.group);
            if (MapUtils.isNotEmpty(dataMap)) {
                dataMap.put(value.code, value.description);
            } else {
                Map<String, String> map1 = new HashMap<>();
                map1.put(value.code, value.description);
                map.put(value.group, map1);
            }
        }
        return map;
    }
}
