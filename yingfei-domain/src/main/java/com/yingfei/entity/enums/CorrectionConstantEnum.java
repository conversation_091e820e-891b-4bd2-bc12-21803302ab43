package com.yingfei.entity.enums;

import lombok.Getter;

/**
 * 纠偏常量
 */
@Getter
public enum CorrectionConstantEnum {
    CONSTANT_C4(0, "c4_"),
    CONSTANT_D2(0, "d2_"),
    CONSTANT_d3_L(0, "d3_"),
    CONSTANT_B3(0, "B3_"),
    CONSTANT_B4(0, "B4_"),
    CONSTANT_D3_U(0, "D3_"),
    CONSTANT_D4(0, "D4_"),
    CONSTANT_A2(0, "A2_"),
    CONSTANT_A3(0, "A3_");

    private final Integer code;
    private final String description;

    CorrectionConstantEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

}
