package com.yingfei.entity.enums;

import com.yingfei.entity.util.HistogramUtil;
import lombok.Getter;
import org.apache.commons.math3.stat.descriptive.rank.Median;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 特殊处理类型
 */
@Getter
public enum SpecialHandlingTypeEnum {
    /**
     * 特殊处理类型(0:标准差 1:极差 2:均值 3:频数 4:求和 5:中位数 6:最大值 7:最小值)
     */
    SD(0, "标准差"),
    RANGE(1, "极差"),
    MEAN(2, "均值"),
    FREQUENCY(3, "频数"),
    SUM(4, "求和"),
    MEDIAN(5, "中位数"),
    MAX(6, "最大值"),
    MIN(7, "最小值"),
    ;


    private final Integer type;
    private final String desc;

    SpecialHandlingTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static SpecialHandlingTypeEnum getType(Integer type) {
        for (SpecialHandlingTypeEnum e : SpecialHandlingTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return SpecialHandlingTypeEnum.SD;
    }

    public static Double calculate(SpecialHandlingTypeEnum specialHandlingTypeEnum, List<Double> list) {
        Collections.sort(list);
        double d = 0D;
        switch (specialHandlingTypeEnum) {
            case SD:
                d = HistogramUtil.getLongTermStandardDeviation(list);
                break;
            case RANGE:
                Double max = list.stream().max(Double::compareTo).orElse(0d);
                Double min = list.stream().min(Double::compareTo).orElse(0d);
                d = BigDecimal.valueOf(max).subtract(BigDecimal.valueOf(min)).doubleValue();
                break;
            case MEAN:
                d = list.stream().mapToDouble(Double::doubleValue).average().orElse(0d);
                break;
            case FREQUENCY:
                d = list.size();
                break;
            case SUM:
                d = list.stream().mapToDouble(Double::doubleValue).sum();
                break;
            case MEDIAN:
                double[] array = list.stream().mapToDouble(x -> x).toArray();
                d = new Median().evaluate(array);
                break;
            case MAX:
                d = list.stream().max(Double::compareTo).orElse(0d);
                break;
            case MIN:
                d = list.stream().min(Double::compareTo).orElse(0d);
        }
        return d;
    }

}
