package com.yingfei.entity.enums;

import lombok.Getter;
import org.checkerframework.checker.units.qual.A;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Getter
public enum GAUGE_FORMAT_RETURN_TYPEEnum {

    A_VAL(0, "A值"),
    B_VAL(1, "B值"),
    A_SUBTRACT_B(2, "差额(A-B)"),
    A_MEAN_B(3, "均值(A,B)"),
    A_ADD_B(4, "求和(A+B)"),
    ;

    private final int type;
    private final String description;

    GAUGE_FORMAT_RETURN_TYPEEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    public static GAUGE_FORMAT_RETURN_TYPEEnum getType(Integer type) {
        for (GAUGE_FORMAT_RETURN_TYPEEnum e : GAUGE_FORMAT_RETURN_TYPEEnum.values()) {
            if (e.getType() == type) {
                return e;
            }
        }
        return GAUGE_FORMAT_RETURN_TYPEEnum.A_VAL;
    }

    public static Double getVal(GAUGE_FORMAT_RETURN_TYPEEnum type, Double A, Double B) {
        Double val = 0D;
        switch (type) {
            case A_VAL:
                val = A;
                break;
            case B_VAL:
                val = B;
                break;
            case A_SUBTRACT_B:
                val = BigDecimal.valueOf(A).subtract(BigDecimal.valueOf(B)).doubleValue();
                break;
            case A_MEAN_B:
                val = BigDecimal.valueOf(A).add(BigDecimal.valueOf(B)).divide(BigDecimal.valueOf(2L), 4, RoundingMode.FLOOR).doubleValue();
                break;
            case A_ADD_B:
                val = BigDecimal.valueOf(A).add(BigDecimal.valueOf(B)).doubleValue();
        }
        return val;
    }
}
