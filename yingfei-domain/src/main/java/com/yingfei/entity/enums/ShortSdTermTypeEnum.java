package com.yingfei.entity.enums;

import lombok.Getter;

@Getter
public enum ShortSdTermTypeEnum {
    /**
     * 1:自适应 2:极差法 3:标准差法 4:合并标准差法(标准差法平方+极差法平方的和再开方)
     */
    AUTO(1, "自适应"),
    RANGE(2, "极差法"),
    SD(3, "标准差法"),
    COMBINE(4, "合并标准差法(标准差法平方+极差法平方的和再开方)"),
    ;

    private final int type;
    private final String desc;

    ShortSdTermTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
