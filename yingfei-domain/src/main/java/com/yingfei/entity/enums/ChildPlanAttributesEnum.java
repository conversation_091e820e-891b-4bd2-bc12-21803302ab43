package com.yingfei.entity.enums;

import com.yingfei.common.core.enums.LanguageEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.entity.domain.DICT_INF;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流程图子计划属性枚举
 */
@Getter
public enum ChildPlanAttributesEnum {
    /**
     * 属性如:
     * D.测试管理
     * 1.只用于计算(不保存)
     * 2.允许操作员跳过测试
     * 3.若未定义公差，即跳过测试
     * E.属性编码
     * 1.自动累加缺陷代码数!
     * 2.将属性测试默认为零
     * 3.不要求属性代码
     * 4.自动打开代码表
     * 5.不埴充代码列表
     * 6.允许创建新代码
     * <p>
     * D1. 只用于输入测试  临时值
     * D2. 可以不输入对应列的值(默认全都要输入)
     * D3. 选中一项测试后 在采集任务样本列表需要隐藏该项测试列
     * E1.  选中后 在采集任务样本列表中缺陷代码数量默认为1  不选中 有缺陷代码需要输入缺陷数量
     * E6. 在选中缺陷代码时可以输入自定义代码  并在数据库添加到对应组下面
     */
    FOR_CALCULATION_ONLY(1, "只用于计算(不保存)"),
    ALLOWS_OPERATORS_TO_SKIP_TESTS(2, "允许操作员跳过测试"),
    UNDEFINED_TOLERANCE_SKIP_TESTS(3, "若未定义公差，即跳过测试"),
    AUTOMATIC_ACCUMULATION_OF_DEFECT_CODES(4, "自动累加缺陷代码数!"),
    DEFAULTS_THE_PROPERTY_TEST_TO_ZERO(5, "将属性测试默认为零"),
    NO_PROPERTY_CODE_IS_REQUIRED(6, "不要求属性代码"),
    AUTOMATICALLY_OPENS_THE_CODE_TABLE(7, "自动打开代码表"),
    DO_NOT_FILL_THE_CODE_LIST(8, "不埴充代码列表"),
    ALLOWS_NEW_CODE_TO_BE_CREATED(9, "允许创建新代码"),
    ;

    private final Integer code;
    private final String description;

    ChildPlanAttributesEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static Map<String, String> getMap(Map<String, List<DICT_INF>> dictInfMap) {
        HashMap<String, String> map = new HashMap<>();
        for (ChildPlanAttributesEnum value : ChildPlanAttributesEnum.values()) {
            List<DICT_INF> dictInfList = dictInfMap.get(value.name());
            if (CollectionUtils.isEmpty(dictInfList)) {
                map.put(value.code.toString(), value.description);
            } else {
                LanguageEnum language = JudgeUtils.getLanguage();
                for (DICT_INF dictInf : dictInfList) {
                    if (language.getIndex() == dictInf.getF_TYPE()){
                        map.put(value.code.toString(), dictInf.getF_CONTENT());
                        break;
                    }
                }
            }
        }
        return map;
    }
}
