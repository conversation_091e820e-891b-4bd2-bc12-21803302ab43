package com.yingfei.entity.enums;

import lombok.Getter;
import org.apache.commons.jexl3.JexlContext;
import org.apache.commons.jexl3.JexlEngine;
import org.apache.commons.jexl3.JexlScript;
import org.apache.commons.jexl3.MapContext;
import org.apache.commons.jexl3.internal.Engine;

import java.util.HashMap;
import java.util.Map;

/**
 * 控制图类型枚举
 */
@Getter
public enum ControlChartTypeEnum {

    CONTROL_CHART_IX_MR(1,
            new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_IX,
                    ControlChartSingleEnum.CHART_GROUP_TYPE_MR}, "单值-移动极差",
            "if(x==1 && y<=1){return true;}else{return false;}"),

    CONTROL_CHART_IX_MR_RW(2, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_IX,
            ControlChartSingleEnum.CHART_GROUP_TYPE_MR, ControlChartSingleEnum.CHART_GROUP_TYPE_RW},
            "单值-移动极差-极差件内", "if(x==1 && y>1 && y <=9){return true;}else{return false;}"),

    CONTROL_CHART_IX_MR_SDW(3, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_IX,
            ControlChartSingleEnum.CHART_GROUP_TYPE_MR, ControlChartSingleEnum.CHART_GROUP_TYPE_SDW},
            "单值-移动极差-标准差件内", "if(x==1 && y >9){return true;}else{return false;}"),

    CONTROL_CHART_X_R(4, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_X,
            ControlChartSingleEnum.CHART_GROUP_TYPE_RANGE}, "均值-极差",
            "if(x>1 && x <=9 && y<=1){return true;}else{return false;}"),

    CONTROL_CHART_X_R_RW(5, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_X,
            ControlChartSingleEnum.CHART_GROUP_TYPE_RANGE, ControlChartSingleEnum.CHART_GROUP_TYPE_RW},
            "均值-极差-极差件内", "if(x>1 && x <=9 && y>1 && y <=9){return true;}else{return false;}"),
    CONTROL_CHART_X_R_SDW(6, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_X,
            ControlChartSingleEnum.CHART_GROUP_TYPE_RANGE, ControlChartSingleEnum.CHART_GROUP_TYPE_SDW},
            "均值-极差-标准差件内", "if(x>1 && x <=9 && y >9){return true;}else{return false;}"),

    CONTROL_CHART_X_SD(7, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_X,
            ControlChartSingleEnum.CHART_GROUP_TYPE_SD}, "均值-标准差",
            "if(x>9 && y<=1){return true;}else{return false;}"),

    CONTROL_CHART_X_SD_RW(8, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_X,
            ControlChartSingleEnum.CHART_GROUP_TYPE_SD, ControlChartSingleEnum.CHART_GROUP_TYPE_RW},
            "均值-标准差-极差件内", "if(x>9 && y>1 && y <=9){return true;}else{return false;}"),

    CONTROL_CHART_X_SD_SDW(9, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_X,
            ControlChartSingleEnum.CHART_GROUP_TYPE_SD, ControlChartSingleEnum.CHART_GROUP_TYPE_SDW},
            "均值-标准差-标准差件内", "if(x>9 && y>9){return true;}else{return false;}"),

    CONTROL_CHART_U(10, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_U},
            "U", ""),

    CONTROL_CHART_C(11, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_C},
            "C", ""),

    CONTROL_CHART_P(12, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_P},
            "P", ""),

    CONTROL_CHART_NP(13, new ControlChartSingleEnum[]{ControlChartSingleEnum.CHART_GROUP_TYPE_NP},
            "NP", ""),
    ;

    /**
     * 图表类型
     */
    private Integer type;
    /**
     * 图表分类
     */
    private ControlChartSingleEnum[] groupType;
    /**
     * 图表描述
     */
    private String description;

    /**
     * 控制图判断公式
     */
    private String formula;

    ControlChartTypeEnum(Integer type, ControlChartSingleEnum[] groupType, String description, String formula) {
        this.type = type;
        this.groupType = groupType;
        this.description = description;
        this.formula = formula;
    }

    /**
     * 循环枚举并判断公式
     *
     * @param subgroupSize 子组大小(样本量)
     * @param testNum      子测试数量
     * @param type         测试类型(1--变量；2--缺陷；3--不良)
     */
    public static Integer getType(Double subgroupSize, Integer testNum, Integer type) {
        if (type == 2) {
            return ControlChartTypeEnum.CONTROL_CHART_U.getType();
        } else if (type == 3) {
            return ControlChartTypeEnum.CONTROL_CHART_P.getType();
        } else {
            JexlEngine engine = new Engine();
            JexlContext context = new MapContext();
            for (ControlChartTypeEnum controlChartTypeEnum : ControlChartTypeEnum.values()) {
                String formula = controlChartTypeEnum.getFormula();
                if (formula == null || "".equals(formula)) {
                    continue;
                }
                context.set("x", subgroupSize);
                context.set("y", testNum);
                JexlScript script = engine.createScript(formula);
                Object execute = script.execute(context);
                if (execute != null && (boolean) execute) {
                    return controlChartTypeEnum.getType();
                }
            }
        }
        return null;
    }

    /**
     * 根据类型获取枚举
     *
     * @param type
     */
    public static ControlChartTypeEnum getEnumByType(Integer type) {
        for (ControlChartTypeEnum controlChartTypeEnum : ControlChartTypeEnum.values()) {
            if (controlChartTypeEnum.getType().equals(type)) {
                return controlChartTypeEnum;
            }
        }
        return null;
    }

    public static Map<Integer,String> getMap(){
        HashMap<Integer,String> map = new HashMap<>();
        for (ControlChartTypeEnum controlChartTypeEnum : ControlChartTypeEnum.values()) {
            map.put(controlChartTypeEnum.getType(),controlChartTypeEnum.getDescription());
        }
        return map;
    }
}
