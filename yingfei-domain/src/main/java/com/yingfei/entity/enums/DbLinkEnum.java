package com.yingfei.entity.enums;

import com.yingfei.common.core.utils.StringUtils;
import lombok.Getter;

/**
 * 数据库链接枚举
 */
@Getter
public enum DbLinkEnum {

    SQL_SERVER(1, "com.microsoft.sqlserver.jdbc.SQLServerDriver", "jdbc:sqlserver://${URL};", "databaseName=${DB_NAME};nullCatalogMeansCurrent=true"),

    ORACLE(2, "oracle.jdbc.OracleDriver", "jdbc:oracle:thin:@//${URL}/", "${DB_NAME}"),

    MYSQL(3, "com.mysql.cj.jdbc.Driver", "jdbc:mysql://${URL}/", "${DB_NAME}?useUnicode=true&characterEncoding=UTF-8"),

    PGSQL(3, "org.postgresql.Driver", "jdbc:postgresql://${URL}/", "${DB_NAME}"),
    ;

    /**
     * 数据库类型
     */
    private final int type;
    /**
     * 数据库驱动
     */
    private final String driver;

    /**
     * 数据库链接前缀
     */
    private final String prefix;

    /**
     * 数据库链接后缀
     */
    private final String suffix;

    DbLinkEnum(int type, String driver, String prefix, String suffix) {
        this.type = type;
        this.driver = driver;
        this.prefix = prefix;
        this.suffix = suffix;
    }

    public static DbLinkEnum getType(Integer type) {
        for (DbLinkEnum e : DbLinkEnum.values()) {
            if (e.getType() == type) {
                return e;
            }
        }
        return DbLinkEnum.SQL_SERVER;
    }

    public static DbLinkEnum getType(String driver) {
        if (StringUtils.isEmpty(driver)) {
            return DbLinkEnum.SQL_SERVER;
        }
        for (DbLinkEnum e : DbLinkEnum.values()) {
            if (e.getDriver().equals(driver)) {
                return e;
            }
        }
        return DbLinkEnum.SQL_SERVER;
    }
}
