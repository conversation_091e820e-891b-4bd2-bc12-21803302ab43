package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 帕累托图分析角度类型
 */
@Getter
public enum ParetoAnalyseTypeEnum {
    /**
     * 班次
     * 班次组
     * 测试
     * 测试编号
     * 测试组
     * 产品
     * 产品组
     * 工作
     * 工作组
     * 过程
     * 过程组
     * 描述符(选择后选择对应描述符组)
     * 批次
     * 时间(秒,分钟,小时,天数,周,月,季度)
     * 员工
     * 缺陷代码 (抽样数据有)
     * 缺陷组 (抽样数据有)
     * 事件类型 (过程事件有)
     * 事件名称 (过程事件有)
     * 异常原因组 (过程事件有)
     * 异常原因 (过程事件有)
     * 改善措施组 (过程事件有)
     * 改善措施 (过程事件有)
     */
    SHIFT_DAT(1, "班次", 0),
    SHIFT_GRP(2, "班次组", 0),
    TEST_DAT(3, "测试", 0),
    TEST_NO(4, "样本编号", 0),
    PART_DAT(5, "产品", 0),
    JOB_DAT(6, "工作", 0),
    JOB_GRP(7, "工作组", 0),
    PRCS_DAT(8, "过程", 0),
    DESC_DAT(9, "描述符", 0),
    LOT_DAT(10, "批次", 0),
    TIME(11, "时间", 0),
    STAFF(12, "员工", 0),
    EVENT_TYPE(13, "事件类型", 1),
    EVENT_NAME(14, "事件名称", 1),
    CAUSE_GRP(15, "异常原因组", 1),
    CAUSE_DAT(16, "异常原因", 1),
    RESPONSE_ACTION_GRP(17, "改善措施组", 1),
    RESPONSE_ACTION_DAT(18, "改善措施", 1),
    DEF_GRP(19, "缺陷组", 2),
    DEF_DAT(20, "缺陷代码", 2),
    DESC_GRP(21, "描述符组(删除描述符的逻辑加的判断)", 10),
    PTRV_DAT(22, "产品版本", 0),
    INSPECTION_TYPE(23, "检验类型", 0),
    ;

    final int code;
    final String describe;

    /**
     * 0:默认都有
     * 1:过程事件有
     * 2:抽样数据有
     */
    final int type;

    ParetoAnalyseTypeEnum(int code, String describe, int type) {
        this.code = code;
        this.describe = describe;
        this.type = type;
    }

    public static ParetoAnalyseTypeEnum getByCode(int code) {

        for (ParetoAnalyseTypeEnum boxPlotsAnalyseTypeEnum : ParetoAnalyseTypeEnum.values()) {
            if (boxPlotsAnalyseTypeEnum.code == code) {
                return boxPlotsAnalyseTypeEnum;
            }
        }
        return TEST_DAT;
    }

    public static Map<String, String> getMap(int type) {
        Map<String, String> map = new LinkedHashMap<>();
        for (ParetoAnalyseTypeEnum value : ParetoAnalyseTypeEnum.values()) {
            if (value.getType() == 0 || value.getType() == type) {
                map.put(value.describe, String.valueOf(value.code));
            }
        }
        return map;
    }
}
