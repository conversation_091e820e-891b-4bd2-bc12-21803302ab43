<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yingfei</groupId>
        <artifactId>YINGFEI-ADMIN</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>yingfei-domain</artifactId>

    <description>
        yingfei-domain实体模块
    </description>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- yingfei Common DataScope -->
<!--        <dependency>-->
<!--            <groupId>com.yingfei</groupId>-->
<!--            <artifactId>yingfei-common-datascope</artifactId>-->
<!--        </dependency>-->
        <!-- yingfei Common Core-->
        <dependency>
            <groupId>com.yingfei</groupId>
            <artifactId>yingfei-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.11.2</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>