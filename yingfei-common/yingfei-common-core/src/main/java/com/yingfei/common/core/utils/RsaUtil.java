package com.yingfei.common.core.utils;


import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: RsaUtil
 * @Description: RSA加解密工具类
 */
public class RsaUtil {


    private static final int DEFAULT_RSA_KEY_SIZE = 2048;

    private static final String KEY_ALGORITHM = "RSA";

    public static void main(String[] args) throws Exception {
//        Map<String, String> result = generateRsaKey(DEFAULT_RSA_KEY_SIZE);
//        System.out.println("公钥为：" + result.get("publicKey"));
//        System.out.println("私钥为：" + result.get("privateKey"));
        String decrypt = decrypt("hDU084ofHH2ALEoRADD1GLcXLtUuEOTSSa/G5ms4blTwyHVLp3hsWEqJh+HJ4gr0Fd9D6rQB4neXCCkAnUqDgDuU7m+rOTbVjNK/FerkMViykfGx09vm84rus8jkqK+348bg8L+ULOqWGwrCn/8qco6siqT7+i8nP9ZN6t002PPxj6KYJka5wcSQhiZVZAXzzkN894j219hnvja5xqYs1khDCd1/gCDN6dIVr7MdsCXigX+881X+BAThU/+LMvo95C+MV0meitN5P4hX7D9n7bZBQJdnZNYoKodG9PF7jgBPAVlg0WuWP0CWakOhZc1x15b0Sc2qeAbN+ke0J3bEAg==",
                "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCSKmsYQBNRI6Pc0ipfRcm0ZG3Q4sYTohYYj/teqckjVUciXulHCz8h3xSjGtp8gtKCDPHWfXtspp4QaemlrIO9OlVEi0ttjui5duP4h9BCSRp11OAxcYviwN3lXQ4UfN2F4te5Xw7aq5c+Y4WoAgtvu/dcMLZU0aCs4p5H5e7+NpJLJwl5ljAq3Qm/3I95/ajm1uE/YTvGSWDepkb3zaj37gWe8Meg/kOddjM1PF1xXzFOOL2mnWEVPMbUD4F8ca9VBMVtun1kbDz3bXoOdCpqXh0qhlZ/qMSCobjB1ScpMuDip0EyQtCX7C2ATo9DTaiuvVS18Fr7RhiIvwNO4OjnAgMBAAECggEAUAQCrrrdGE0fdlJjOda1jhhweaq7bqIL4JRpsq/V2Y7kqy80G11PyVYbMRMRSpo/bUxy083L1HX+D9Q7QSQI1n1pkmWC/E4Anw8zy6ll2/OzFZBczxRu1MpqbxabD6IvjwzYtbURJ6pJ2LCYyZmc8yT+TCyqlBJSGHLS3V9Fj0oruNA7FTgxub2xUiK7XTm5MEBgllxEQsKtTkPCtbqQR91eG7vuftjcK+v8oa3naTm87JSFH5sC3a7cFbjAV3/BvLQEt0iSr5ckgQuHs4c9nrbnSvLfce7+J1Ccf2YkBlAWKg7qP5P9PWpMO0x9owHFpEZnoQz4ixM4E/D95e+WgQKBgQD6YDCAjOKDy7uamiQIT5zSZnDv7gRR35fmI5XmzDIqtk7rOZ583RRUWI94oXi+eAOM7aEsW1Qwjg1HdFJL07JPeLG2PI4u1/Vrwt+Dy0qD79yr6N9kZTYH2lZfNbZOtEaZ2UfoM9CEszbojhj3YAB3oSWeB5pERxAkPfQcgf/UwQKBgQCVcvVsJq7Qf8Iag7dcgLPrrDhpiAPD+6LqWC/fNQY3+c/7PdODn1lZGrNrsl1MIyDJmdRtoxvKSv4ULGEGl3jJ2uYrNDB3j/RhODUpn4oHQBkQfGlmOHT6E9eVvgWExo1bl8oFYkUxvv5A7oW0NIH0NVu/Y+MugB+VWnKFw+nfpwKBgG4Xd5w+1qcfIVnEp01kPE+jrctCPHIHwBH42cQHS5POX/s8dbzvtS21Vziyv5X4zY7dOj531hgXji06L6mZ51DcgD/idp6QZSDCKmgYLqa/BKgumHPbsRtY3Ru2UXhf+p9RlDW8+tYuu1kcikvT0cDIgfLv4txc5vMYrssOOoDBAoGADJ1zkfmB8B+YptxPcgN7LH2RFXdhuf1XtyhSgijbqYXwxGhnnkipJhhW5NRdg7vDXMEbaVXHVsAVymYkmoXWsFNF8oepP65rFnnEIwtQf2QtuXKHAxXo6eMa44zysk6ASCb76pBs/tH8bEa2CowUHCUU5Ybs+lPeVqEX4aqz5ykCgYByb0LMJpgmc6+howkMHw6zuopwc5XbgcRU9K4WrFjR6ZdjlhDFeroJDLgE4g6+ALY+XOjh3gWym29LHDW8Z7plrYCXUqbF/80B3g0jsTWCvpU7pyrOaRcyLOfqMfWpqW5WppiXzPgNK3r8dBhO4yzEmDkx1Ys5AOi5gdwjvocjxg==");
        System.err.println(decrypt);
//        String s = AESUtil.decryptStr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decrypt);

        String s = AESUtil.decryptStr("DH8EOi8xF76M0XcI9QrikgBlkdJTQDIPsTOV2VobW8U=", decrypt);
        System.err.println(s);
    }

    /**
     * 生成RSA 公私钥,可选长度为1025,2048位.
     */
    public static Map<String, String> generateRsaKey(int keySize) {
        Map<String, String> result = new HashMap<>(2);
        try {
            KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);

            // 初始化**对生成器，**大小为1024 2048位
            keyPairGen.initialize(keySize, new SecureRandom());
            // 生成一个**对，保存在keyPair中
            KeyPair keyPair = keyPairGen.generateKeyPair();
            // 得到公钥字符串
            result.put("publicKey", Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded()));
            // 得到私钥字符串
            result.put("privateKey", Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded()));
        } catch (GeneralSecurityException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * RSA私钥解密
     *
     * @param str        加密字符串
     * @param privateKey 私钥
     * @return 铭文
     * @throws Exception 解密过程中的异常信息
     */
    public static String decrypt(String str, String privateKey) {
        //64位解码加密后的字符串
        byte[] inputByte;
        String outStr = "";
        try {
            inputByte = Base64.getDecoder().decode(str.getBytes(StandardCharsets.UTF_8));
            //base64编码的私钥
            byte[] decoded = Base64.getDecoder().decode(privateKey);
            RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
            //RSA解密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, priKey);
            outStr = new String(cipher.doFinal(inputByte));
        } catch (NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException | BadPaddingException | InvalidKeySpecException | NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return outStr;
    }

    /**
     * RSA公钥加密
     *
     * @param str 加密字符串
     * @param publicKey 公钥
     * @return 密文
     * @throws Exception 加密过程中的异常信息
     */
    public static String encrypt(String str, String publicKey) throws Exception {
        //base64编码的公钥
        byte[] decoded = Base64.getDecoder().decode(publicKey);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
        //RSA加密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        String outStr = Base64.getEncoder().encodeToString(cipher.doFinal(str.getBytes(StandardCharsets.UTF_8)));

        return outStr;

    }

}
