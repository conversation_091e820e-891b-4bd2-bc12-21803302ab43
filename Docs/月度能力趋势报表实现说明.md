# 月度能力趋势报表实现说明

## 📋 需求概述

实现月度能力趋势报表功能，根据用户需求修改查询条件 `f_type = 1`（按月统计），并补充项目负责人信息查询。

## 🎯 核心功能

### 1. 主表查询
- **表名**: `STREAM_TREND_INF`
- **关键条件**: `F_TYPE = 1` (按月统计)
- **提供字段**:
  - 产品ID (`F_PART`)
  - 过程ID (`F_PRCS`) 
  - 测试ID (`F_TEST`)
  - 产品版本ID (`F_PTRV`)
  - CPK (`F_CPK`)
  - 子组数量 (`F_SGRP_COUNT`)

### 2. 关联表查询

#### 公差限表 (`SPEC_INF`)
- **关联条件**: `F_PART`、`F_PRCS`、`F_TEST`、`F_PTRV`
- **提供字段**:
  - 公差上限 (`F_USL`)
  - 目标值 (`F_TAR`)
  - 公差下限 (`F_LSL`)
  - 目标CPK (`F_CPK`)

#### 产品表 (`PART_INF`)
- **关联条件**: `F_PART`
- **提供字段**:
  - 产品ID (`F_PART`)
  - 产品名称 (`F_NAME`)

#### 过程表 (`PRCS_INF`)
- **关联条件**: `F_PRCS`
- **提供字段**:
  - 过程ID (`F_PRCS`)
  - 过程名称 (`F_NAME`)

#### 产品版本表 (`PART_REV`)
- **关联条件**: `F_PART`、`F_PTRV`
- **提供字段**:
  - 版本ID (`F_PTRV`)
  - 版本名称 (`F_NAME`)

#### 测试表 (`TEST_INF`)
- **关联条件**: `F_TEST`
- **提供字段**:
  - 测试ID (`F_TEST`)
  - 测试名称 (`F_NAME`)

### 3. 项目负责人查询 (新增功能)

#### 员工责任信息表 (`EMPL_RESPONSIBLE_INF`)
- **关联条件**: 
  - `STREAM_TREND_INF.F_TEST = EMPL_RESPONSIBLE_INF.F_DATA`
  - `EMPL_RESPONSIBLE_INF.F_TYPE = 0`
- **提供字段**: 员工ID (`F_EMPL`)

#### 员工信息表 (`EMPL_INF`)
- **关联条件**: `EMPL_RESPONSIBLE_INF.F_EMPL = EMPL_INF.F_EMPL`
- **提供字段**: 员工姓名 (`F_NAME`)
- **实现方式**: 通过 `RemoteUserService` 远程调用

## 🔧 技术实现

### 1. 新增DTO类
- **文件**: `MonthTrendResultDTO.java`
- **功能**: 定义月度趋势报表返回数据结构
- **关键字段**:
  - 基本信息：产品、过程、测试、版本信息
  - 公差限信息：USL、TAR、LSL、目标CPK
  - 月度数据：各月份子组数量和CPK值
  - 分析结果：改善幅度、能力分析
  - 负责人信息：负责人ID和姓名

### 2. 修改Service实现
- **文件**: `MonthTrendServiceImpl.java`
- **主要修改**:
  - 查询条件改为 `F_TYPE = 1`
  - 新增项目负责人查询逻辑
  - 实现复杂的多月份数据处理
  - 添加改善幅度和能力分析计算

### 3. 更新接口定义
- **文件**: `MonthTrendService.java`、`MonthTrendController.java`
- **修改**: 明确返回类型为 `TableDataInfo<MonthTrendResultDTO>`

## 📊 查询条件

支持以下查询条件：
- 产品ID集合 (`partList`)
- 过程ID集合 (`prcsList`)
- 测试ID集合 (`testList`)
- 版本ID集合 (`ptrvList`)
- 开始月份 (`startDate`)
- 结束月份 (`endDate`)

## 📈 返回数据结构

每行数据包含：
- 产品ID、产品名称
- 产品版本ID、产品版本名称
- 过程ID、过程名称
- 测试ID、测试名称
- 公差限信息：USL、TAR、LSL、目标CPK
- 各月份子组数量和CPK值
- 月改善幅度：`(最新月CPK - 上月CPK) / 上月CPK * 100`
- 能力分析：
  - CPK ≥ 1.33 = 满足
  - 1 ≤ CPK < 1.33 = 较差
  - 0.67 ≤ CPK < 1 = 严重不足
  - CPK < 0.67 = 极差
- 项目负责人ID和姓名

## 🔄 API接口

### 请求地址
```
POST /monthTrend/getMonthTrend
```

### 请求参数
```json
{
  "partList": [产品ID列表],
  "prcsList": [过程ID列表], 
  "testList": [测试ID列表],
  "ptrvList": [版本ID列表],
  "startDate": "开始日期",
  "endDate": "结束日期",
  "offset": 页码,
  "next": 每页大小
}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 总记录数,
    "rows": [
      {
        "productId": "产品ID",
        "productName": "产品名称",
        "productVersionId": "版本ID", 
        "productVersionName": "版本名称",
        "processId": "过程ID",
        "processName": "过程名称",
        "testId": "测试ID",
        "testName": "测试名称",
        "usl": 公差上限,
        "tar": 目标值,
        "lsl": 公差下限,
        "targetCpk": 目标CPK,
        "monthlySubgroupCount": {
          "2025-07": 子组数量,
          "2025-06": 子组数量
        },
        "monthlyCpk": {
          "2025-07": CPK值,
          "2025-06": CPK值
        },
        "improvementRate": 改善幅度,
        "capabilityAnalysis": "能力分析结果",
        "responsiblePersonId": "负责人ID",
        "responsiblePersonName": "负责人姓名"
      }
    ]
  }
}
```

## ✅ 实现完成

1. ✅ 修改查询条件 `f_type = 1`
2. ✅ 实现项目负责人查询
3. ✅ 创建完整的返回数据结构
4. ✅ 实现复杂的多月份数据处理
5. ✅ 添加改善幅度和能力分析计算
6. ✅ 更新接口定义和Controller

## 🚀 使用说明

该接口已完成开发，可以直接调用。系统会自动：
- 查询指定条件下的月度趋势数据
- 关联查询相关表信息
- 计算改善幅度和能力分析
- 查询项目负责人信息
- 返回完整的分析结果

如需测试，建议先准备好测试数据，确保各表之间的关联关系正确。
