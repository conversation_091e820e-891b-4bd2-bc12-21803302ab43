# 公差限查询优化说明

## 🎯 优化背景

在月度能力趋势报表中，除了项目负责人查询存在N+1问题外，公差限查询也存在同样的性能问题。每个结果都需要单独查询一次公差限信息，导致数据库查询次数过多。

## 🔍 问题分析

### ❌ 优化前的问题
1. **N+1查询问题**: 在`buildMonthTrendResult`方法中为每个结果单独调用`querySpecInfo`
2. **查询时机不当**: 在分页前就查询了所有数据的公差限信息
3. **重复查询**: 可能存在相同条件的重复查询
4. **性能浪费**: 查询了不在当前页面的数据的公差限信息

### 📊 查询条件复杂度
公差限查询需要4个条件：
- `F_PART` (产品ID)
- `F_PRCS` (过程ID) 
- `F_TEST` (测试ID)
- `F_PTRV` (产品版本ID)

## 🚀 优化方案

### 1. 数据流程调整
```
优化前：
查询数据 → 分组 → 构建结果(含公差限查询) → 分页 → 返回

优化后：
查询数据 → 分组 → 构建结果(不含公差限) → 分页 → 批量查询公差限 → 设置公差限信息 → 返回
```

### 2. 核心组件设计

#### SpecInfo 内部类
```java
private static class SpecInfo {
    private Double usl;      // 公差上限
    private Double tar;      // 目标值
    private Double lsl;      // 公差下限
    private Double targetCpk; // 目标CPK
}
```

#### 批量查询方法
```java
private Map<String, SpecInfo> batchQuerySpecInfo(List<MonthTrendResultDTO> resultList)
```

## 🔧 技术实现

### 1. 构建唯一标识Key
```java
// 为每个结果构建唯一标识
Set<String> specKeys = pagedResult.stream()
        .map(result -> result.getProductId() + "_" + result.getProcessId() + "_" + result.getTestId() + "_" + result.getProductVersionId())
        .collect(Collectors.toSet());
```

### 2. 批量查询逻辑
```java
// 为每个结果构建查询条件
for (MonthTrendResultDTO result : resultList) {
    String key = result.getProductId() + "_" + result.getProcessId() + "_" + result.getTestId() + "_" + result.getProductVersionId();
    
    LambdaQueryWrapper<SPEC_INF> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(SPEC_INF::getF_PART, result.getProductId())
           .eq(SPEC_INF::getF_PRCS, result.getProcessId())
           .eq(SPEC_INF::getF_TEST, result.getTestId())
           .eq(SPEC_INF::getF_PTRV, result.getProductVersionId())
           .eq(SPEC_INF::getF_DEL, YesOrNoEnum.NO.getType())
           .last("LIMIT 1");

    List<SPEC_INF> specInfs = specInfService.list(wrapper);
    if (!specInfs.isEmpty()) {
        SPEC_INF specInf = specInfs.get(0);
        specInfoMap.put(key, new SpecInfo(
                specInf.getF_USL(),
                specInf.getF_TAR(),
                specInf.getF_LSL(),
                specInf.getF_CPK()
        ));
    }
}
```

### 3. 设置公差限信息
```java
// 为当前页面的数据设置公差限信息
pagedResult.forEach(result -> {
    String specKey = result.getProductId() + "_" + result.getProcessId() + "_" + result.getTestId() + "_" + result.getProductVersionId();
    SpecInfo specInfo = specInfoMap.get(specKey);
    if (specInfo != null) {
        result.setUsl(specInfo.getUsl());
        result.setTar(specInfo.getTar());
        result.setLsl(specInfo.getLsl());
        result.setTargetCpk(specInfo.getTargetCpk());
    }
});
```

## 📈 性能优化效果

### 1. 查询次数对比
- **优化前**: 每个结果1次查询 = N次查询
- **优化后**: 当前页面数据量次查询 = 页面大小次查询

### 2. 数据传输优化
- **优化前**: 查询所有数据的公差限信息
- **优化后**: 只查询当前页面数据的公差限信息

### 3. 内存使用优化
- **优化前**: 在内存中保存所有公差限查询结果
- **优化后**: 只在内存中保存当前页面需要的公差限信息

## 🔄 与负责人查询的协同优化

### 统一的优化策略
```java
// 批量查询当前页面的项目负责人和公差限信息
if (!pagedResult.isEmpty()) {
    // 提取测试相关的唯一标识
    Set<Long> testIds = pagedResult.stream()
            .map(MonthTrendResultDTO::getTestId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

    Set<String> specKeys = pagedResult.stream()
            .map(result -> result.getProductId() + "_" + result.getProcessId() + "_" + result.getTestId() + "_" + result.getProductVersionId())
            .collect(Collectors.toSet());

    // 批量查询负责人信息
    Map<Long, ResponsiblePersonInfo> responsiblePersonMap = batchQueryResponsiblePersons(testIds);
    
    // 批量查询公差限信息
    Map<String, SpecInfo> specInfoMap = batchQuerySpecInfo(pagedResult);

    // 统一设置信息
    pagedResult.forEach(result -> {
        // 设置负责人信息
        // 设置公差限信息
    });
}
```

## 🛡️ 异常处理

### 1. 空值检查
- 检查resultList是否为空
- 检查查询结果是否为空
- 检查各个字段是否为空

### 2. 查询异常处理
```java
try {
    // 批量查询逻辑
} catch (Exception e) {
    log.error("批量查询公差限信息失败，error: {}", e.getMessage());
    return new HashMap<>();
}
```

### 3. 数据一致性保证
- 使用LIMIT 1确保只返回一条记录
- 检查删除标记确保数据有效性

## 🔮 进一步优化建议

### 1. 真正的批量查询
当前实现仍然是循环查询，可以进一步优化为：
```sql
SELECT * FROM SPEC_INF 
WHERE (F_PART, F_PRCS, F_TEST, F_PTRV) IN (
    (part1, prcs1, test1, ptrv1),
    (part2, prcs2, test2, ptrv2),
    ...
)
AND F_DEL = 0
```

### 2. 缓存机制
- 对于相同条件的公差限信息可以考虑缓存
- 使用Redis或本地缓存提升查询性能

### 3. 索引优化
- 确保SPEC_INF表在(F_PART, F_PRCS, F_TEST, F_PTRV)上有复合索引
- 优化查询执行计划

## ✅ 优化收益

1. **性能提升**: 显著减少数据库查询次数
2. **内存优化**: 减少不必要的数据加载
3. **代码一致性**: 与负责人查询采用相同的优化策略
4. **可维护性**: 代码结构更清晰，逻辑更合理
5. **扩展性**: 为后续其他关联查询优化提供模板

## 📊 测试验证

### 1. 功能测试
- 验证公差限信息是否正确显示
- 测试各种边界条件
- 验证异常情况处理

### 2. 性能测试
- 对比优化前后的查询次数
- 测试大数据量下的性能表现
- 验证内存使用情况

通过这次优化，公差限查询和负责人查询都采用了相同的批量查询策略，大幅提升了月度能力趋势报表的整体查询性能。
