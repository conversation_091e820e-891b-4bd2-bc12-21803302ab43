# 批量查询负责人优化说明

## 🎯 优化目标

将项目负责人查询从循环中的单个查询优化为批量查询，并将查询时机调整到分页之后，进一步提升性能。

## 🔄 优化前后对比

### ❌ 优化前的问题
1. **N+1查询问题**: 在循环中为每个结果单独查询负责人信息
2. **查询时机不当**: 在分页前查询所有数据的负责人信息
3. **性能浪费**: 查询了不在当前页面的数据的负责人信息

### ✅ 优化后的改进
1. **批量查询**: 一次性查询所有需要的负责人信息
2. **分页后查询**: 只查询当前页面数据的负责人信息
3. **Map缓存**: 使用Map存储查询结果，避免重复查询

## 🏗️ 实现架构

### 1. 数据流程调整
```
原流程：
查询数据 → 分组 → 构建结果(含负责人查询) → 分页 → 返回

新流程：
查询数据 → 分组 → 构建结果(不含负责人) → 分页 → 批量查询负责人 → 设置负责人信息 → 返回
```

### 2. 核心组件

#### ResponsiblePersonInfo 内部类
```java
private static class ResponsiblePersonInfo {
    private Long personId;
    private String personName;
    
    public ResponsiblePersonInfo(Long personId, String personName) {
        this.personId = personId;
        this.personName = personName;
    }
}
```

#### 批量查询方法
```java
private Map<Long, ResponsiblePersonInfo> batchQueryResponsiblePersons(Set<Long> testIds)
```

## 🔧 技术实现细节

### 1. 分页后提取测试ID
```java
// 批量查询当前页面的项目负责人信息
Set<Long> testIds = pagedResult.stream()
        .map(MonthTrendResultDTO::getTestId)
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
```

### 2. 批量查询负责人映射关系
```java
// 批量查询EMPL_RESPONSIBLE_INF表
List<String> testIdStrings = testIds.stream()
        .map(String::valueOf)
        .collect(Collectors.toList());

LambdaQueryWrapper<EMPL_RESPONSIBLE_INF> responsibleWrapper = new LambdaQueryWrapper<>();
responsibleWrapper.in(EMPL_RESPONSIBLE_INF::getF_DATA, testIdStrings)
                 .eq(EMPL_RESPONSIBLE_INF::getF_TYPE, 0)
                 .eq(EMPL_RESPONSIBLE_INF::getF_DEL, DelFlagEnum.USE.getType());
```

### 3. 批量查询员工信息
```java
// 提取员工ID列表
Set<Long> emplIds = responsibleList.stream()
        .map(EMPL_RESPONSIBLE_INF::getF_EMPL)
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());

// 批量查询员工信息
Map<Long, AdminUserRespDTO> userMap = new HashMap<>();
for (Long emplId : emplIds) {
    AdminUserRespDTO adminUser = remoteUserService.getUser(emplId).getData();
    if (adminUser != null) {
        userMap.put(emplId, adminUser);
    }
}
```

### 4. 构建结果映射
```java
// 构建结果Map：testId -> ResponsiblePersonInfo
return responsibleList.stream()
        .filter(resp -> resp.getF_EMPL() != null && userMap.containsKey(resp.getF_EMPL()))
        .collect(Collectors.toMap(
                resp -> Long.valueOf(resp.getF_DATA()),
                resp -> {
                    AdminUserRespDTO user = userMap.get(resp.getF_EMPL());
                    return new ResponsiblePersonInfo(user.getId(), user.getNickname());
                },
                (existing, replacement) -> existing // 如果有重复，保留第一个
        ));
```

### 5. 设置负责人信息
```java
// 为当前页面的数据设置负责人信息
pagedResult.forEach(result -> {
    ResponsiblePersonInfo responsiblePerson = responsiblePersonMap.get(result.getTestId());
    if (responsiblePerson != null) {
        result.setResponsiblePersonId(responsiblePerson.getPersonId());
        result.setResponsiblePersonName(responsiblePerson.getPersonName());
    }
});
```

## 📊 性能优化效果

### 1. 查询次数减少
- **优化前**: 1 + N 次查询（1次主查询 + N次负责人查询）
- **优化后**: 3 次查询（1次主查询 + 1次负责人映射查询 + 1次员工信息查询）

### 2. 数据传输优化
- **优化前**: 查询所有数据的负责人信息
- **优化后**: 只查询当前页面数据的负责人信息

### 3. 内存使用优化
- **优化前**: 在内存中保存所有负责人查询结果
- **优化后**: 只在内存中保存当前页面需要的负责人信息

## 🛡️ 异常处理

### 1. 空值检查
- 检查testIds是否为空
- 检查查询结果是否为空
- 检查员工ID是否为空

### 2. 远程调用异常处理
```java
try {
    AdminUserRespDTO adminUser = remoteUserService.getUser(emplId).getData();
    if (adminUser != null) {
        userMap.put(emplId, adminUser);
    }
} catch (Exception e) {
    log.warn("远程查询员工信息失败，emplId: {}, error: {}", emplId, e.getMessage());
}
```

### 3. 重复数据处理
```java
.collect(Collectors.toMap(
    resp -> Long.valueOf(resp.getF_DATA()),
    resp -> new ResponsiblePersonInfo(...),
    (existing, replacement) -> existing // 保留第一个
));
```

## ✅ 优化收益

1. **性能提升**: 显著减少数据库查询次数
2. **内存优化**: 减少不必要的数据加载
3. **网络优化**: 减少远程服务调用次数
4. **可维护性**: 代码结构更清晰，逻辑更合理
5. **扩展性**: 易于后续优化和功能扩展

## 🔍 测试建议

### 1. 功能测试
- 验证负责人信息是否正确显示
- 测试分页功能是否正常
- 验证异常情况处理

### 2. 性能测试
- 对比优化前后的查询次数
- 测试大数据量下的性能表现
- 验证内存使用情况

### 3. 边界测试
- 测试空数据情况
- 测试负责人不存在的情况
- 测试远程服务异常情况

这次优化将显著提升月度能力趋势报表的查询性能，特别是在数据量较大的情况下效果更加明显。
