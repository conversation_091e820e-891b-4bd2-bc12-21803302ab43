<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                   xmlns:camunda="http://camunda.org/schema/1.0/bpmn" id="diagram_Process_1721285690465"
                   targetNamespace="http://bpmn.io/schema/bpmn">
    <bpmn2:process id="Alarm_Require_RC_RA" name="报警流程" isExecutable="true">
        <bpmn2:startEvent id="Event_1ln5pt2" name="发生报警">
            <bpmn2:outgoing>Flow_1mf22sq</bpmn2:outgoing>
        </bpmn2:startEvent>
        <bpmn2:userTask id="Activity_126el9w" name="异常原因审核" camunda:assignee="${assignee}">
            <bpmn2:extensionElements>
                <camunda:properties>
                    <camunda:property name="test" value="1"/>
                </camunda:properties>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_1i7l6w3</bpmn2:incoming>
            <bpmn2:outgoing>Flow_0ycdegt</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_1mf22sq" sourceRef="Event_1ln5pt2" targetRef="Activity_0y6viz2"/>
        <bpmn2:userTask id="Activity_0uje3eu" name="改善措施审核" camunda:assignee="${assignee}">
            <bpmn2:incoming>Flow_198j7g5</bpmn2:incoming>
            <bpmn2:outgoing>Flow_16iw2lb</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_0ycdegt" sourceRef="Activity_126el9w" targetRef="Gateway_0yy3bcw"/>
        <bpmn2:endEvent id="Event_1it51sq" name="结束">
            <bpmn2:incoming>Flow_1wjqs3x</bpmn2:incoming>
        </bpmn2:endEvent>
        <bpmn2:sequenceFlow id="Flow_16iw2lb" sourceRef="Activity_0uje3eu" targetRef="Gateway_04nq6vh"/>
        <bpmn2:userTask id="Activity_1tvfk5e" name="指定改善措施" camunda:assignee="${assignee}">
            <bpmn2:incoming>Flow_00ejswa</bpmn2:incoming>
            <bpmn2:incoming>Flow_1scniah</bpmn2:incoming>
            <bpmn2:outgoing>Flow_198j7g5</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_198j7g5" sourceRef="Activity_1tvfk5e" targetRef="Activity_0uje3eu"/>
        <bpmn2:userTask id="Activity_0y6viz2" name="指定异常原因" camunda:assignee="${assignee}">
            <bpmn2:incoming>Flow_1mf22sq</bpmn2:incoming>
            <bpmn2:incoming>Flow_0vgudhv</bpmn2:incoming>
            <bpmn2:outgoing>Flow_1i7l6w3</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_1i7l6w3" sourceRef="Activity_0y6viz2" targetRef="Activity_126el9w"/>
        <bpmn2:exclusiveGateway id="Gateway_04nq6vh">
            <bpmn2:incoming>Flow_16iw2lb</bpmn2:incoming>
            <bpmn2:outgoing>Flow_1wjqs3x</bpmn2:outgoing>
            <bpmn2:outgoing>Flow_00ejswa</bpmn2:outgoing>
        </bpmn2:exclusiveGateway>
        <bpmn2:sequenceFlow id="Flow_1wjqs3x" sourceRef="Gateway_04nq6vh" targetRef="Event_1it51sq">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${input == 'Y'}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="Flow_00ejswa" sourceRef="Gateway_04nq6vh" targetRef="Activity_1tvfk5e">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${input == 'H'}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:exclusiveGateway id="Gateway_0yy3bcw">
            <bpmn2:incoming>Flow_0ycdegt</bpmn2:incoming>
            <bpmn2:outgoing>Flow_1scniah</bpmn2:outgoing>
            <bpmn2:outgoing>Flow_0vgudhv</bpmn2:outgoing>
        </bpmn2:exclusiveGateway>
        <bpmn2:sequenceFlow id="Flow_1scniah" sourceRef="Gateway_0yy3bcw" targetRef="Activity_1tvfk5e">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${input == 'Y'}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="Flow_0vgudhv" sourceRef="Gateway_0yy3bcw" targetRef="Activity_0y6viz2">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${input == 'H'}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="Alarm_Require_RC_RA_di" bpmnElement="Alarm_Require_RC_RA">
            <bpmndi:BPMNEdge id="Flow_00ejswa_di" bpmnElement="Flow_00ejswa">
                <di:waypoint x="870" y="235"/>
                <di:waypoint x="870" y="120"/>
                <di:waypoint x="600" y="120"/>
                <di:waypoint x="600" y="220"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1wjqs3x_di" bpmnElement="Flow_1wjqs3x">
                <di:waypoint x="895" y="260"/>
                <di:waypoint x="922" y="260"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1i7l6w3_di" bpmnElement="Flow_1i7l6w3">
                <di:waypoint x="290" y="260"/>
                <di:waypoint x="340" y="260"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_198j7g5_di" bpmnElement="Flow_198j7g5">
                <di:waypoint x="650" y="260"/>
                <di:waypoint x="720" y="260"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_16iw2lb_di" bpmnElement="Flow_16iw2lb">
                <di:waypoint x="820" y="260"/>
                <di:waypoint x="845" y="260"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ycdegt_di" bpmnElement="Flow_0ycdegt">
                <di:waypoint x="440" y="260"/>
                <di:waypoint x="465" y="260"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1mf22sq_di" bpmnElement="Flow_1mf22sq">
                <di:waypoint x="148" y="260"/>
                <di:waypoint x="190" y="260"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1scniah_di" bpmnElement="Flow_1scniah">
                <di:waypoint x="515" y="260"/>
                <di:waypoint x="550" y="260"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0vgudhv_di" bpmnElement="Flow_0vgudhv">
                <di:waypoint x="490" y="235"/>
                <di:waypoint x="490" y="100"/>
                <di:waypoint x="240" y="100"/>
                <di:waypoint x="240" y="220"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Activity_0uje3eu_di" bpmnElement="Activity_0uje3eu">
                <dc:Bounds x="720" y="220" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1it51sq_di" bpmnElement="Event_1it51sq">
                <dc:Bounds x="922" y="242" width="36" height="36"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="929" y="285" width="22" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1tvfk5e_di" bpmnElement="Activity_1tvfk5e">
                <dc:Bounds x="550" y="220" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_04nq6vh_di" bpmnElement="Gateway_04nq6vh" isMarkerVisible="true">
                <dc:Bounds x="845" y="235" width="50" height="50"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1ln5pt2_di" bpmnElement="Event_1ln5pt2">
                <dc:Bounds x="112" y="242" width="36" height="36"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="109" y="285" width="44" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0y6viz2_di" bpmnElement="Activity_0y6viz2">
                <dc:Bounds x="190" y="220" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_126el9w_di" bpmnElement="Activity_126el9w">
                <dc:Bounds x="340" y="220" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0yy3bcw_di" bpmnElement="Gateway_0yy3bcw" isMarkerVisible="true">
                <dc:Bounds x="465" y="235" width="50" height="50"/>
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>
