package com.yingfei.system.utils.dingding;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.taobao.api.ApiException;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.entity.dto.dingDing.DingDingSendMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;

import javax.annotation.Resource;

/**
 * 钉钉发送消息工具类
 */
@Slf4j
@Configuration
public class DingDingSend {
    @Resource
    private RedisService redisService;

    public static final String POST_SEND_MESSAGE = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2";

    @Async
    public void sendMessage(DingDingSendMessageDTO dingDingSendMessageDTO) {
        try {
            Object access_token = redisService.get(RedisConstant.DING_DING_ACCESS_TOKEN);
            if (access_token == null) {
                log.info("-----------------获取access_token失败-----------------");
                throw new BusinessException(CommonExceptionEnum.ACCESS_TOKEN_FAILED);
            }
            DingTalkClient client = new DefaultDingTalkClient(POST_SEND_MESSAGE + "?access_token=" + access_token);
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(dingDingSendMessageDTO.getAgent_id());
            req.setUseridList(dingDingSendMessageDTO.getUserid_list());
            req.setDeptIdList(dingDingSendMessageDTO.getDept_id_list());
            req.setToAllUser(dingDingSendMessageDTO.getTo_all_user());
            req.setMsg(dingDingSendMessageDTO.getMsg().toJSONString());
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, "");
            rsp.getBody();
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }
}
