package com.yingfei.dataCollection.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.config.ThreadPoolConfig;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataCollectionExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataCollection.config.InitConfig;
import com.yingfei.dataCollection.mapper.INSPECTION_PLAN_INFMapper;
import com.yingfei.dataCollection.mapper.MANUFACTURING_NODE_INFMapper;
import com.yingfei.dataCollection.mapper.MANUFACTURING_PROCESS_INFMapper;
import com.yingfei.dataCollection.mapper.SPEC_INFMapper;
import com.yingfei.dataCollection.service.*;
import com.yingfei.dataCollection.util.DbCollectionUtil;
import com.yingfei.dataManagement.api.RemoteSpecService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.autoCollect.DataFileAutoCollectConfigDTO;
import com.yingfei.entity.dto.dataImport.DataColumnMappingDTO;
import com.yingfei.entity.dto.dataImport.FileNameDataDTO;
import com.yingfei.entity.dto.globalConfig.SysyemGlobalConfig;
import com.yingfei.entity.enums.DbFiledEnum;
import com.yingfei.entity.enums.DbSpecFiledEnum;
import com.yingfei.entity.enums.TimeEnum;
import com.yingfei.entity.util.ParseDataFileUtil;
import com.yingfei.entity.vo.SPEC_INF_VO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.entity.vo.dataCollection.DataCollectionVO;
import com.yingfei.mq.api.RemoteMqService;
import com.yingfei.system.api.RemoteGlobalConfigInfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataImportServiceImpl implements DataImportService {

    @Resource
    private RedisService redisService;
    @Resource
    private MANUFACTURING_PROCESS_INFMapper manufacturingProcessInfMapper;
    @Resource
    private MANUFACTURING_NODE_INFMapper manufacturingNodeInfMapper;
    @Resource
    private INSPECTION_PLAN_INFMapper inspectionPlanInfMapper;
    @Resource
    private RemoteMqService remoteMqService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private PART_INFService partInfService;
    @Resource
    private PRCS_INFService prcsInfService;
    @Resource
    private PART_REVService partRevService;
    @Resource
    private LOT_INFService lotInfService;
    @Resource
    private JOB_DATService jobDatService;
    @Resource
    private JOB_GRPService jobGrpService;
    @Resource
    private SHIFT_DATService shiftDatService;
    @Resource
    private SHIFT_GRPService shiftGrpService;
    @Resource
    private DESC_DATService descDatService;
    @Resource
    private DESC_GRPService descGrpService;
    @Resource
    private DEF_DATService defDatService;
    @Resource
    private DEF_GRPService defGrpService;
    @Resource
    private TEST_INFService testInfService;
    @Resource
    private DB_CONFIG_INFService dbConfigInfService;
    @Resource
    private SPEC_INFMapper specInfMapper;
    @Resource
    private RemoteSpecService remoteSpecService;
    @Resource
    private RemoteGlobalConfigInfService remoteGlobalConfigInfService;

    @Override
    public Map<String, Object> dataSelect(DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, MultipartFile file) {
        List<List<String>> list = ParseDataFileUtil.getDataColumn(dataFileAutoCollectConfigDTO, file);
        Set<Integer> set = new HashSet<>();
        int max = 0;
        for (List<String> line : list) {
            set.add(line.size());
            if (line.size() > max) max = line.size();
        }
        if (set.size() > 1) {
            throw new BusinessException(DataCollectionExceptionEnum.NUMBER_OF_COLUMNS_DOES_NOT_MATCH_EXCEPTION);
        }
        String key = RedisConstant.DATA_IMPORT + SecurityUtils.getLoginUser().getToken();
        if (CollectionUtils.isNotEmpty(redisService.getCacheList(key))) {
            redisService.deleteObject(key);
        }
        redisService.setCacheList(key, list, Constants.REDIS_EXPIRE_TIME);

        List<List<String>> subList = list.subList(0, Math.min(50, list.size()));

        Map<String, Object> map = new HashMap<>();
        map.put("maxColumn", max);
        map.put("total", list.size());
        map.put("list", subList);
        return map;
    }

    @Override
    public void analyzeData(DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, MultipartFile multipartFile) {
        String dataKey = RedisConstant.DATA_IMPORT + SecurityUtils.getLoginUser().getToken();
        List<List<String>> list = redisService.getCacheList(dataKey);
        if (CollectionUtils.isEmpty(list)) {
            list = ParseDataFileUtil.getDataColumn(dataFileAutoCollectConfigDTO, multipartFile);
        }
        List<SubgroupDataVO> subgroupDataVOList = getSubgroupDataVOList(dataFileAutoCollectConfigDTO, list);
        redisService.deleteObject(dataKey);

        String saveKey = RedisConstant.DATA_IMPORT_SAVE + SecurityUtils.getLoginUser().getToken();
        redisService.deleteObject(saveKey);
        redisService.setCacheList(saveKey, subgroupDataVOList, Constants.REDIS_EXPIRE_TIME);
    }

    @Override
    public List<SubgroupDataVO> getSubgroupDataVOList(DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, List<List<String>> list) {
        /*工艺流程缓存*/
        Map<String, MANUFACTURING_PROCESS_INF> mfpsMap = new HashMap<>();
        /*工艺节点缓存*/
        Map<String, MANUFACTURING_NODE_INF> mfndMap = new HashMap<>();
        /*检查计划缓存*/
        Map<String, INSPECTION_PLAN_INF> planMap = new HashMap<>();

        List<DataColumnMappingDTO> filedList = dataFileAutoCollectConfigDTO.getDataColumnMappingDTOList();

        List<SubgroupDataVO> subgroupDataVOList = new ArrayList<>();
        /*保存条件map*/
        Map<String, String> saveMapCache = new HashMap<>();
        SubgroupDataVO subgroupDataVO = new SubgroupDataVO();
        /*文件总行数*/
        int size = list.size();

        Date startDate = null;
        if (StringUtils.isNotEmpty(dataFileAutoCollectConfigDTO.getEndSgrpTim())) {
            /*获取时间间隔*/
            switch (TimeEnum.getType(dataFileAutoCollectConfigDTO.getTimeType())) {
                case SECOND:
                    startDate = DateUtils.addSeconds(DateUtils.parseDate(dataFileAutoCollectConfigDTO.getEndSgrpTim()), -size * dataFileAutoCollectConfigDTO.getTimeNum());
                    break;
                case MINUTE:
                    startDate = DateUtils.addMinutes(DateUtils.parseDate(dataFileAutoCollectConfigDTO.getEndSgrpTim()), -size * dataFileAutoCollectConfigDTO.getTimeNum());
                    break;
                case HOUR:
                    startDate = DateUtils.addHours(DateUtils.parseDate(dataFileAutoCollectConfigDTO.getEndSgrpTim()), -size * dataFileAutoCollectConfigDTO.getTimeNum());
                    break;
                case DAY:
                    startDate = DateUtils.addDays(DateUtils.parseDate(dataFileAutoCollectConfigDTO.getEndSgrpTim()), -size * dataFileAutoCollectConfigDTO.getTimeNum());
                    break;
            }
        }

        /*固定行数判断*/
        Integer num = 0;
        int total = 0;
        for (List<String> dataLine : list) {
            total++;
            /*固定行保存判断*/
            boolean isSaveSubgroup = false;
            /*动态保存条件*/
            boolean dySaveSubgroup = false;
            if (dataFileAutoCollectConfigDTO.getFixedLine() != null) {
                num++;
                Integer fixedLine = dataFileAutoCollectConfigDTO.getFixedLine();
                if (Objects.equals(fixedLine, num)) {
                    isSaveSubgroup = true;
                    num = 0;
                }
            }
            if (!isSaveSubgroup)
                dySaveSubgroup = ParseDataFileUtil.getSaveCondition(1, dataFileAutoCollectConfigDTO, saveMapCache, dataLine, null);
            if (dySaveSubgroup) {
                if (dataFileAutoCollectConfigDTO.getSaveType() == 1) {
                    if (StringUtils.isNotEmpty(subgroupDataVO.getMfpsName())) {
                        subgroupDataVOList.add(subgroupDataVO);
                        subgroupDataVO = new SubgroupDataVO();
                        num = 0;
                    }
                }
            }
            subgroupDataVO.setF_PLNT(dataFileAutoCollectConfigDTO.getF_PLNT());
            subgroupDataVO.setHistoricalData(dataFileAutoCollectConfigDTO.getHistoricalData());
            if (startDate != null) {
                Date date = null;
                if (total != 1) {
                    switch (TimeEnum.getType(dataFileAutoCollectConfigDTO.getTimeType())) {
                        case SECOND:
                            date = DateUtils.addSeconds(startDate, (total - 1) * dataFileAutoCollectConfigDTO.getTimeNum());
                            break;
                        case MINUTE:
                            date = DateUtils.addMinutes(startDate, (total - 1) * dataFileAutoCollectConfigDTO.getTimeNum());
                            break;
                        case HOUR:
                            date = DateUtils.addHours(startDate, (total - 1) * dataFileAutoCollectConfigDTO.getTimeNum());
                            break;
                        case DAY:
                            date = DateUtils.addDays(startDate, (total - 1) * dataFileAutoCollectConfigDTO.getTimeNum());
                            break;
                    }
                } else {
                    date = startDate;
                }
                if (date == null) date = DateUtils.getNowDate();
                subgroupDataVO.setF_SGTM(DateUtils.toUtc(date));
            }
            List<String> testNameList = new ArrayList<>();
            for (DataColumnMappingDTO dataColumnMappingDTO : filedList) {
                switch (DbFiledEnum.getType(dataColumnMappingDTO.getFiled())) {
                    case STRUCTURE:
                        List<DataColumnMappingDTO> dataColumnMappingDTOList = dataColumnMappingDTO.getDataColumnMappingDTOList();
                        if (CollectionUtils.isEmpty(dataColumnMappingDTOList) && dataColumnMappingDTOList.size() != 4) {
                            throw new BusinessException(DataManagementExceptionEnum.STRUCTURE_UNDER_CONFIGURATION);
                        }
                        for (DataColumnMappingDTO columnMappingDTO : dataColumnMappingDTOList) {
                            switch (DbFiledEnum.getType(columnMappingDTO.getFiled())) {
                                case MFPS_DAT:
                                    if (StringUtils.isNotEmpty(subgroupDataVO.getMfpsName())) continue;
                                    String mfpsName = ParseDataFileUtil.analyzeField(columnMappingDTO.getType(),
                                            columnMappingDTO.getAnalyticRuleList(), columnMappingDTO.getColumn(),
                                            columnMappingDTO.getColumnName(), dataLine);
                                    if (StringUtils.isEmpty(mfpsName))
                                        throw new BusinessException(DataManagementExceptionEnum.MFPS_NOT_EXISTS);
                                    if (ParseDataFileUtil.judgeName(total, mfpsName, DbFiledEnum.MFPS_DAT.getDesc()))
                                        continue;
                                    subgroupDataVO.setMfpsName(mfpsName);
                                    MANUFACTURING_PROCESS_INF manufacturingProcessInf;
                                    if (mfpsMap.get(subgroupDataVO.getMfpsName()) != null) {
                                        manufacturingProcessInf = mfpsMap.get(subgroupDataVO.getMfpsName());
                                        subgroupDataVO.setF_MFPS(manufacturingProcessInf.getF_MFPS());
                                    } else {
                                        LambdaQueryWrapper<MANUFACTURING_PROCESS_INF> mfpsQueryWrapper = new LambdaQueryWrapper<>();
                                        mfpsQueryWrapper.eq(MANUFACTURING_PROCESS_INF::getF_NAME, subgroupDataVO.getMfpsName())
                                                .eq(MANUFACTURING_PROCESS_INF::getF_DEL, DelFlagEnum.USE.getType());
                                        manufacturingProcessInf = manufacturingProcessInfMapper.selectOne(mfpsQueryWrapper);
                                        if (manufacturingProcessInf != null) {
                                            /*判断工艺流程的工厂与所选工厂是否一致*/
                                            if (!manufacturingProcessInf.getF_PLNT().equals(dataFileAutoCollectConfigDTO.getF_PLNT())) {
                                                throw new BusinessException(DataManagementExceptionEnum.MFPS_PLNT_INCONFORMITY);
                                            }
                                            mfpsMap.put(subgroupDataVO.getMfpsName(), manufacturingProcessInf);
                                            subgroupDataVO.setF_MFPS(manufacturingProcessInf.getF_MFPS());
                                        } else {
                                            throw new BusinessException(DataManagementExceptionEnum.MFPS_NOT_EXISTS);
                                        }
                                    }
                                    break;
                                case MFND_DAT:
                                    if (StringUtils.isNotEmpty(subgroupDataVO.getMfndName())) continue;
                                    String mfndName = ParseDataFileUtil.analyzeField(columnMappingDTO.getType(),
                                            columnMappingDTO.getAnalyticRuleList(), columnMappingDTO.getColumn(),
                                            columnMappingDTO.getColumnName(), dataLine);
                                    if (StringUtils.isEmpty(mfndName)) {
                                        throw new BusinessException(DataManagementExceptionEnum.MFND_NOT_EXISTS);
                                    }
                                    if (ParseDataFileUtil.judgeName(total, mfndName, DbFiledEnum.MFND_DAT.getDesc()))
                                        continue;
                                    subgroupDataVO.setMfndName(mfndName);
                                    MANUFACTURING_NODE_INF manufacturingNodeInf;
                                    String key = subgroupDataVO.getMfndName() + subgroupDataVO.getMfpsName();
                                    if (mfndMap.get(key) != null) {
                                        manufacturingNodeInf = mfndMap.get(key);
                                        subgroupDataVO.setF_MFND(manufacturingNodeInf.getF_MFND());
                                    } else {
                                        LambdaQueryWrapper<MANUFACTURING_NODE_INF> mfndQueryWrapper = new LambdaQueryWrapper<>();
                                        mfndQueryWrapper.eq(MANUFACTURING_NODE_INF::getF_NAME, subgroupDataVO.getMfndName())
                                                .eq(MANUFACTURING_NODE_INF::getF_MFPS, subgroupDataVO.getF_MFPS())
                                                .eq(MANUFACTURING_NODE_INF::getF_DEL, DelFlagEnum.USE.getType());
                                        manufacturingNodeInf = manufacturingNodeInfMapper.selectOne(mfndQueryWrapper);
                                        if (manufacturingNodeInf != null) {
                                            mfndMap.put(key, manufacturingNodeInf);
                                            subgroupDataVO.setF_MFND(manufacturingNodeInf.getF_MFND());
                                        } else {
                                            throw new BusinessException(DataManagementExceptionEnum.MFND_NOT_EXISTS);
                                        }
                                    }
                                    subgroupDataVO.setF_MFND(manufacturingNodeInf.getF_MFND());
                                    break;
                                case PLAN_DAT:
                                    if (StringUtils.isNotEmpty(subgroupDataVO.getPlanName())) continue;
                                    String planName = ParseDataFileUtil.analyzeField(columnMappingDTO.getType(),
                                            columnMappingDTO.getAnalyticRuleList(), columnMappingDTO.getColumn(),
                                            columnMappingDTO.getColumnName(), dataLine);
                                    if (ParseDataFileUtil.judgeName(total, planName, DbFiledEnum.PLAN_DAT.getDesc()))
                                        continue;
                                    subgroupDataVO.setPlanName(planName);
                                    INSPECTION_PLAN_INF inspectionPlanInf;
                                    String planKey = subgroupDataVO.getPlanName() + subgroupDataVO.getMfndName() + subgroupDataVO.getMfpsName();
                                    if (planMap.get(planKey) != null) {
                                        inspectionPlanInf = planMap.get(planKey);
                                        subgroupDataVO.setF_INSP_PLAN(inspectionPlanInf.getF_PLAN());
                                    } else {
                                        LambdaQueryWrapper<INSPECTION_PLAN_INF> queryWrapper = new LambdaQueryWrapper<>();
                                        queryWrapper.eq(INSPECTION_PLAN_INF::getF_NAME, subgroupDataVO.getPlanName())
                                                .eq(INSPECTION_PLAN_INF::getF_MFPS, subgroupDataVO.getF_MFPS())
                                                .eq(INSPECTION_PLAN_INF::getF_MFND, subgroupDataVO.getF_MFND())
                                                .eq(INSPECTION_PLAN_INF::getF_DEL, DelFlagEnum.USE.getType());
                                        inspectionPlanInf = inspectionPlanInfMapper.selectOne(queryWrapper);
                                        if (inspectionPlanInf != null) {
                                            planMap.put(planKey, inspectionPlanInf);
                                            subgroupDataVO.setF_INSP_PLAN(inspectionPlanInf.getF_PLAN());
                                        } else {
                                            throw new BusinessException(DataManagementExceptionEnum.PLAN_NOT_EXISTS);
                                        }
                                    }
                                    subgroupDataVO.setF_INSP_PLAN(inspectionPlanInf.getF_PLAN());
                                    break;
                                case CHILD_DAT:
                                    String childName = ParseDataFileUtil.analyzeField(columnMappingDTO.getType(),
                                            columnMappingDTO.getAnalyticRuleList(), columnMappingDTO.getColumn(),
                                            columnMappingDTO.getColumnName(), dataLine);
                                    if (ParseDataFileUtil.judgeName(total, childName, DbFiledEnum.CHILD_DAT.getDesc()))
                                        continue;
                                    String childKey = subgroupDataVO.getPlanName() + subgroupDataVO.getMfndName() + subgroupDataVO.getMfpsName();
                                    if (planMap.get(childKey) != null) {
                                        INSPECTION_PLAN_INF planInf = planMap.get(childKey);
                                        Map<String, List<INSPECTION_PLAN_CHILD_DTO>> map =
                                                JSONObject.parseObject(planInf.getF_CHILD())
                                                        .entrySet().stream()
                                                        .collect(Collectors.toMap(Map.Entry::getKey, entry ->
                                                                JSONArray.parseArray(String.valueOf(entry.getValue()), INSPECTION_PLAN_CHILD_DTO.class)));
                                        List<INSPECTION_PLAN_CHILD_DTO> inspectionPlanChildDtoList = map.get(childName);
                                        if (inspectionPlanChildDtoList == null) {
                                            throw new BusinessException(DataManagementExceptionEnum.CHILD_NOT_EXISTS);
                                        }
                                        subgroupDataVO.setChildId(inspectionPlanChildDtoList.get(0).getChildId());
                                    } else {
                                        throw new BusinessException(DataManagementExceptionEnum.CHILD_NOT_EXISTS);
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                        break;
                    case PART_DAT:
                        if (StringUtils.isNotEmpty(subgroupDataVO.getPartName())) continue;
                        String partName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                dataColumnMappingDTO.getColumnName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, partName, DbFiledEnum.PART_DAT.getDesc())) continue;
                        subgroupDataVO.setPartName(partName);

                        if (StringUtils.isNotEmpty(subgroupDataVO.getPtrvName())) continue;

                        if(dataColumnMappingDTO.getGroupType() == 1 && ObjectUtils.isEmpty(dataColumnMappingDTO.getAppointColumn())){
                            continue;
                        }

                        String ptrvName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                dataColumnMappingDTO.getGroupName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, ptrvName, DbFiledEnum.PREV_DAT.getDesc())) continue;
                        subgroupDataVO.setPtrvName(ptrvName);
                        break;
                    case PRCS_DAT:
                        if (StringUtils.isNotEmpty(subgroupDataVO.getPrcsName())) continue;
                        String prcsName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                dataColumnMappingDTO.getColumnName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, prcsName, DbFiledEnum.PRCS_DAT.getDesc())) continue;
                        subgroupDataVO.setPrcsName(prcsName);
                        break;
                    case SHIFT_DAT:
                        if (StringUtils.isNotEmpty(subgroupDataVO.getShiftName())) continue;
                        String shiftName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                dataColumnMappingDTO.getColumnName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, shiftName, DbFiledEnum.SHIFT_DAT.getDesc())) continue;
                        subgroupDataVO.setShiftName(shiftName);

                        if (StringUtils.isNotEmpty(subgroupDataVO.getShiftGrpName())) continue;
                        String shiftGrpName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                dataColumnMappingDTO.getGroupName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, shiftGrpName, DbFiledEnum.SHIFT_DAT.getDesc())) continue;
                        subgroupDataVO.setShiftGrpName(shiftGrpName);
                        break;
                    case JOB_DAT:
                        if (StringUtils.isNotEmpty(subgroupDataVO.getJobName())) continue;
                        String jobName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                dataColumnMappingDTO.getColumnName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, jobName, DbFiledEnum.JOB_DAT.getDesc())) continue;
                        subgroupDataVO.setJobName(jobName);

                        if (StringUtils.isNotEmpty(subgroupDataVO.getJobGrpName())) continue;
                        String jobGrpName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                dataColumnMappingDTO.getGroupName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, jobGrpName, DbFiledEnum.JOB_DAT.getDesc())) continue;
                        subgroupDataVO.setJobGrpName(jobGrpName);
                        break;
                    case LOT:
                        if (StringUtils.isNotEmpty(subgroupDataVO.getLotName())) continue;
                        String lotName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                dataColumnMappingDTO.getColumnName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, lotName, DbFiledEnum.LOT.getDesc())) continue;
                        subgroupDataVO.setLotName(lotName);
                        break;
                    case DESC_DAT:
                        List<SGRP_DSC> sgrpDscList = subgroupDataVO.getSgrpDscList();
                        if (CollectionUtils.isEmpty(sgrpDscList))
                            sgrpDscList = new ArrayList<>();
                        String descName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                dataColumnMappingDTO.getColumnName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, descName, DbFiledEnum.DESC_DAT.getDesc())) continue;

                        String dsgpName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                dataColumnMappingDTO.getGroupName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, dsgpName, DbFiledEnum.DESC_DAT.getDesc())) continue;

                        AtomicBoolean atomicBoolean = new AtomicBoolean(true);
                        sgrpDscList.forEach(sgrpDsc -> {
                            if (sgrpDsc.getDescGrpName().equals(dsgpName) && sgrpDsc.getDescName().equals(descName)) {
                                atomicBoolean.set(false);
                            }
                        });
                        if (atomicBoolean.get()) {
                            SGRP_DSC sgrpDsc = new SGRP_DSC();
                            sgrpDsc.setDescName(descName);
                            sgrpDsc.setDescGrpName(dsgpName);
                            sgrpDscList.add(sgrpDsc);
                        }
                        subgroupDataVO.setSgrpDscList(sgrpDscList);
                        break;
                    case TEST_DAT:
                        List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList = subgroupDataVO.getSgrpValChildDtoList();
                        if (CollectionUtils.isEmpty(sgrpValChildDtoList))
                            sgrpValChildDtoList = new ArrayList<>();
                        String testVal = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                dataColumnMappingDTO.getColumnName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, testVal, DbFiledEnum.TEST_DAT.getDesc())) continue;
                        if (!StringUtils.isNumber(testVal)) {
                            throw new BusinessException(DataCollectionExceptionEnum.START_LINE_EXCEPTION);
                        }
                        Double testValue = new BigDecimal(testVal).doubleValue();
                        String testName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                dataColumnMappingDTO.getGroupName(), dataLine);
                        if (ParseDataFileUtil.judgeName(total, testName, DbFiledEnum.TEST_DAT.getDesc())) continue;

                        AtomicReference<SGRP_VAL_CHILD_DTO> dto = new AtomicReference<>();
                        sgrpValChildDtoList.forEach(sgrpValChildDto -> {
                            if (sgrpValChildDto.getTestName().equals(testName)) {
                                dto.set(sgrpValChildDto);
                                if (testNameList.contains(testName) && CollectionUtils.isNotEmpty(testNameList)) {
                                    /*测试名称一致添加子测试*/
                                    List<SGRP_VAL_CHILD_DTO.Test> testList = sgrpValChildDto.getTestList();
                                    if (CollectionUtils.isNotEmpty(testList)) {
                                        List<SGRP_VAL_CHILD_DTO.SubTest> subTestList = testList.get(0).getSubTestList();
                                        if (CollectionUtils.isEmpty(subTestList)) {
                                            subTestList = new ArrayList<>();
                                            SGRP_VAL_CHILD_DTO.SubTest subTest = new SGRP_VAL_CHILD_DTO.SubTest();
                                            /*记录第一个子测试*/
                                            subTest.setSubTestValue(testList.get(0).getTestVal());
                                            subTest.setSubTestNo(YesOrNoEnum.YES.getType());
                                            subTestList.add(subTest);
                                        }
                                        SGRP_VAL_CHILD_DTO.SubTest subTest = new SGRP_VAL_CHILD_DTO.SubTest();
                                        subTest.setSubTestValue(testValue);
                                        subTest.setSubTestNo(subTestList.size() + 1);
                                        subTestList.add(subTest);
                                        testList.get(0).setSubTestList(subTestList);
                                    }
                                } else {
                                    SGRP_VAL_CHILD_DTO.Test test = new SGRP_VAL_CHILD_DTO.Test();
                                    test.setTestNo(sgrpValChildDto.getTestList().size());
                                    test.setTestVal(testValue);
                                    /*获取缺陷代码*/
                                    String defectName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getDefectType(),
                                            dataColumnMappingDTO.getAnalyticRuleDefectList(), dataColumnMappingDTO.getDefectColumn(),
                                            dataColumnMappingDTO.getDefectName(), dataLine);
                                    test.setDefectName(defectName);
                                    sgrpValChildDto.getTestList().add(test);
                                }
                            }
                        });
                        testNameList.add(testName);
                        if (dto.get() == null) {
                            SGRP_VAL_CHILD_DTO sgrpValChildDto = new SGRP_VAL_CHILD_DTO();
                            sgrpValChildDto.setTestName(testName);
                            sgrpValChildDto.setChildId(subgroupDataVO.getChildId());
                            dto.set(sgrpValChildDto);
                            sgrpValChildDtoList.add(sgrpValChildDto);
                        }
                        SGRP_VAL_CHILD_DTO sgrpValChildDto = dto.get();
                        List<SGRP_VAL_CHILD_DTO.Test> testList = sgrpValChildDto.getTestList();
                        if (CollectionUtils.isEmpty(testList)) testList = new ArrayList<>();
                        if (CollectionUtils.isEmpty(testList)) {
                            SGRP_VAL_CHILD_DTO.Test test = new SGRP_VAL_CHILD_DTO.Test();
                            test.setTestNo(testList.size());
                            test.setTestVal(testValue);
                            /*获取缺陷代码*/
                            String defectName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getDefectType(),
                                    dataColumnMappingDTO.getAnalyticRuleDefectList(), dataColumnMappingDTO.getDefectColumn(),
                                    dataColumnMappingDTO.getDefectName(), dataLine);
                            test.setDefectName(defectName);
                            testList.add(test);
                        }
                        sgrpValChildDto.setTestList(testList);
                        subgroupDataVO.setF_SGSZ(testList.size());
                        subgroupDataVO.setSgrpValChildDtoList(sgrpValChildDtoList);
                        break;
                    case TIME:
                        if (subgroupDataVO.getF_SGTM() != null) continue;
                        /*判断时间是否包含汉字*/

                        String sgtm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                dataColumnMappingDTO.getColumnName(), dataLine);
                        if (Validator.hasChinese(sgtm)) {
                            throw new BusinessException(DataCollectionExceptionEnum.START_LINE_EXCEPTION);
                        }
                        if (sgtm.length() < 4) {
                            throw new BusinessException(DataCollectionExceptionEnum.START_LINE_EXCEPTION);
                        }
                        if (ParseDataFileUtil.judgeName(total, sgtm, DbFiledEnum.TIME.getDesc())) continue;
                        /*文件读取时间转UTC时间*/
                        subgroupDataVO.setF_SGTM(DateUtils.toUtc(sgtm));
                        break;
                    default:
                        break;
                }

            }
            /*判断保存条件*/
            if (isSaveSubgroup) {
                subgroupDataVOList.add(subgroupDataVO);
                subgroupDataVO = new SubgroupDataVO();
            } else {
                dySaveSubgroup = ParseDataFileUtil.getSaveCondition(1, dataFileAutoCollectConfigDTO, saveMapCache, dataLine, null);
                if (dySaveSubgroup) {
                    if (dataFileAutoCollectConfigDTO.getSaveType() == 2) {
                        subgroupDataVOList.add(subgroupDataVO);
                        subgroupDataVO = new SubgroupDataVO();
                        num = 0;
                    }
                }
            }
            /*判断是否是最后一条并判断是否触发EOF*/
            if (total == size) {
                if (dataFileAutoCollectConfigDTO.getSaveMap().get(Constants.EOF) != null) {
                    subgroupDataVOList.add(subgroupDataVO);
                }
            }
        }
        return subgroupDataVOList;
    }

    @Override
    @Transactional
    public void batchAdd(List<SubgroupDataVO> subgroupDataVOList) {
        String saveKey = null;
        if (CollectionUtils.isEmpty(subgroupDataVOList)) {
            saveKey = RedisConstant.DATA_IMPORT_SAVE + SecurityUtils.getLoginUser().getToken();
            subgroupDataVOList = redisService.getCacheList(saveKey);
        }

        if (extracted(subgroupDataVOList)) return;
        if (StringUtils.isNotEmpty(saveKey))
            redisService.deleteObject(saveKey);
        remoteMqService.send(subgroupDataVOList);
    }

    private final ReentrantLock lock = new ReentrantLock();

    private boolean extracted(List<SubgroupDataVO> subgroupDataVOList) {
        lock.lock();
        try {
            long start = System.currentTimeMillis();
            /*生成抽样唯一标识*/
            UUID uuid = UUID.randomUUID();
            EMPL_INF_DTO emplInfDto;
            if (SecurityUtils.getLoginUser() == null) {
                emplInfDto = redisService.getCacheObject(RedisConstant.ADMIN_USER_ID);
            } else {
                emplInfDto = SecurityUtils.getLoginUser().getSysUser();
            }

            /*产品缓存*/
            ConcurrentHashMap<String, PART_INF> partMap = new ConcurrentHashMap<>();
            /*版本缓存*/
            ConcurrentHashMap<String, PART_REV> ptrvMap = new ConcurrentHashMap<>();
            /*过程缓存*/
            ConcurrentHashMap<String, PRCS_INF> prcsMap = new ConcurrentHashMap<>();
            /*批次缓存*/
            ConcurrentHashMap<String, LOT_INF> lotMap = new ConcurrentHashMap<>();
            /*工作组缓存*/
            ConcurrentHashMap<String, JOB_GRP> jobGrpMap = new ConcurrentHashMap<>();
            /*工作缓存*/
            ConcurrentHashMap<String, JOB_DAT> jobDatMap = new ConcurrentHashMap<>();
            /*班次组缓存*/
            ConcurrentHashMap<String, SHIFT_GRP> shiftGrpMap = new ConcurrentHashMap<>();
            /*班次缓存*/
            ConcurrentHashMap<String, SHIFT_DAT> shiftDatMap = new ConcurrentHashMap<>();
            /*描述符组缓存*/
            ConcurrentHashMap<String, DESC_GRP> descGrpMap = new ConcurrentHashMap<>();
            /*描述符缓存*/
            ConcurrentHashMap<String, DESC_DAT> descDatMap = new ConcurrentHashMap<>();
            /*缺陷代码组缓存*/
            ConcurrentHashMap<String, DEF_GRP> defGrpMap = new ConcurrentHashMap<>();
            /*缺陷代码缓存*/
            ConcurrentHashMap<String, DEF_DAT> defDatMap = new ConcurrentHashMap<>();
            /*测试缓存*/
            ConcurrentHashMap<String, TEST_INF> testMap = new ConcurrentHashMap<>();

            /*新增缺陷代码组*/
            LambdaQueryWrapper<DEF_GRP> defGrpQueryWrapper = new LambdaQueryWrapper<>();
            String name = "缺陷代码组";
            defGrpQueryWrapper.eq(DEF_GRP::getF_NAME, name).eq(DEF_GRP::getF_DEL, DelFlagEnum.USE.getType());
            List<DEF_GRP> def_grpList = defGrpService.list(defGrpQueryWrapper);
            Long defGrpId;
            if (CollectionUtils.isEmpty(def_grpList)) {
                DEF_GRP defGrp = new DEF_GRP();
                defGrp.setF_NAME(name);
                defGrp.setF_EDUE(emplInfDto.getF_EMPL());
                defGrp.setF_CRUE(emplInfDto.getF_EMPL());
                defGrpService.save(defGrp);
                defGrpMap.put(defGrp.getF_NAME(), defGrp);
                defGrpId = defGrp.getF_DFGP();
            } else {
                defGrpId = def_grpList.get(0).getF_DFGP();
            }

//            CountDownLatch countDownLatch = new CountDownLatch(subgroupDataVOList.size());
            subgroupDataVOList.forEach(subgroupDataVO -> {
//                threadPoolConfig.threadPoolTaskExecutor().execute(() -> {
//                    try {
                        long l = System.currentTimeMillis();
                        subgroupDataVO.setF_FINISH_STATUS(YesOrNoEnum.YES.getType());
                        subgroupDataVO.setF_SAMPLE_ID(uuid.toString());
                        subgroupDataVO.setF_CRUE(InitConfig.empl);
                        subgroupDataVO.setF_EDUE(InitConfig.empl);


                        /*判断产品名称是否存在*/
                        if (StringUtils.isNotEmpty(subgroupDataVO.getPartName())) {
                            String partKey = subgroupDataVO.getPartName() + subgroupDataVO.getF_PLNT();
                            PART_INF partInf = partMap.computeIfAbsent(partKey, k -> {
                                PART_INF newPart = partInfService.findByName(subgroupDataVO.getPartName(), subgroupDataVO.getF_PLNT());
                                if (newPart == null) {
                                    newPart = new PART_INF();
                                    newPart.setF_NAME(subgroupDataVO.getPartName());
                                    newPart.setF_PLNT(subgroupDataVO.getF_PLNT());
                                    newPart.setF_EDUE(emplInfDto.getF_EMPL());
                                    newPart.setF_CRUE(emplInfDto.getF_EMPL());
                                    newPart.setF_PART(JudgeUtils.defaultIdentifierGenerator.nextId(null));
                                    newPart.setIsAdd(YesOrNoEnum.YES.getType());
                                }
                                return newPart;
                            });
                            subgroupDataVO.setF_PART(partInf.getF_PART());
                        }

                        //补充产品版本
                        buildRev(subgroupDataVO, emplInfDto, ptrvMap);

                        /*判断过程是否输入的名称*/
                        if (StringUtils.isNotEmpty(subgroupDataVO.getPrcsName())) {
                            String prcsKey = subgroupDataVO.getPrcsName();
                            PRCS_INF prcsInf = prcsMap.computeIfAbsent(prcsKey, k -> {
                                /*先查询*/
                                LambdaQueryWrapper<PRCS_INF> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(PRCS_INF::getF_NAME, subgroupDataVO.getPrcsName())
                                        .eq(PRCS_INF::getF_PLNT, subgroupDataVO.getF_PLNT())
                                        .eq(PRCS_INF::getF_DEL, DelFlagEnum.USE.getType());
                                List<PRCS_INF> list = prcsInfService.list(queryWrapper);
                                PRCS_INF newPrcs = new PRCS_INF();
                                if (CollectionUtils.isEmpty(list)) {
                                    newPrcs.setF_NAME(subgroupDataVO.getPrcsName());
                                    newPrcs.setF_PLNT(subgroupDataVO.getF_PLNT());
                                    newPrcs.setF_EDUE(emplInfDto.getF_EMPL());
                                    newPrcs.setF_CRUE(emplInfDto.getF_EMPL());
                                    newPrcs.setF_PRCS(JudgeUtils.defaultIdentifierGenerator.nextId(DateUtils.toUtc(DateUtils.getNowDate())));
                                    newPrcs.setIsAdd(YesOrNoEnum.YES.getType());
                                } else {
                                    newPrcs = list.get(0);
                                }
                                return newPrcs;
                            });
                            subgroupDataVO.setF_PRCS(prcsInf.getF_PRCS());
                        }

                        /*判断批次是否输入的名称*/
                        if (StringUtils.isNotEmpty(subgroupDataVO.getLotName())) {
                            String lotKey = subgroupDataVO.getLotName();
                            LOT_INF lotInf = lotMap.computeIfAbsent(lotKey, k -> {
                                /*先查询*/
                                LambdaQueryWrapper<LOT_INF> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(LOT_INF::getF_NAME, subgroupDataVO.getLotName())
                                        .eq(LOT_INF::getF_DEL, DelFlagEnum.USE.getType());
                                List<LOT_INF> list = lotInfService.list(queryWrapper);
                                LOT_INF newLot = new LOT_INF();
                                if (CollectionUtils.isEmpty(list)) {
                                    newLot.setF_NAME(subgroupDataVO.getLotName());
                                    newLot.setF_EDUE(emplInfDto.getF_EMPL());
                                    newLot.setF_CRUE(emplInfDto.getF_EMPL());
                                    newLot.setF_PART(subgroupDataVO.getF_PART());
                                    newLot.setF_LOT(JudgeUtils.defaultIdentifierGenerator.nextId(DateUtils.toUtc(DateUtils.getNowDate())));
                                    newLot.setIsAdd(YesOrNoEnum.YES.getType());
                                } else {
                                    newLot = list.get(0);
                                }
                                return newLot;
                            });
                            subgroupDataVO.setF_LOT(lotInf.getF_LOT());
                        }

                        /*判断工作组是否输入的名称*/
                        if (StringUtils.isNotEmpty(subgroupDataVO.getJobGrpName())) {
                            String jobGrpKey = subgroupDataVO.getJobGrpName();
                            JOB_GRP jobGrp = jobGrpMap.computeIfAbsent(jobGrpKey, k -> {
                                /*先查询*/
                                LambdaQueryWrapper<JOB_GRP> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(JOB_GRP::getF_NAME, subgroupDataVO.getJobGrpName())
                                        .eq(JOB_GRP::getF_DEL, DelFlagEnum.USE.getType());
                                List<JOB_GRP> list = jobGrpService.list(queryWrapper);
                                JOB_GRP newJobGrp = new JOB_GRP();
                                if (CollectionUtils.isEmpty(list)) {
                                    newJobGrp.setF_NAME(subgroupDataVO.getJobGrpName());
                                    newJobGrp.setF_EDUE(emplInfDto.getF_EMPL());
                                    newJobGrp.setF_CRUE(emplInfDto.getF_EMPL());
                                    newJobGrp.setF_JBGP(JudgeUtils.defaultIdentifierGenerator.nextId(DateUtils.toUtc(DateUtils.getNowDate())));
                                    newJobGrp.setIsAdd(YesOrNoEnum.YES.getType());
                                } else {
                                    newJobGrp = list.get(0);
                                }
                                return newJobGrp;
                            });
                            subgroupDataVO.setF_JBGP(jobGrp.getF_JBGP());
                        }

                        /*判断工作是否输入的名称*/
                        if (StringUtils.isNotEmpty(subgroupDataVO.getJobName())) {
                            if (StringUtils.isEmpty(subgroupDataVO.getF_JBGP())) {
                                log.info("工作组未指定!");
                                return;
                            }
                            String jobDatKey = subgroupDataVO.getJobName() + subgroupDataVO.getJobGrpName();
                            JOB_DAT jobDat = jobDatMap.computeIfAbsent(jobDatKey, k -> {
                                /*先查询*/
                                LambdaQueryWrapper<JOB_DAT> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(JOB_DAT::getF_NAME, subgroupDataVO.getJobName())
                                        .eq(JOB_DAT::getF_JBGP, subgroupDataVO.getF_JBGP())
                                        .eq(JOB_DAT::getF_DEL, DelFlagEnum.USE.getType());
                                List<JOB_DAT> list = jobDatService.list(queryWrapper);
                                JOB_DAT newJobDat = new JOB_DAT();
                                if (CollectionUtils.isEmpty(list)) {
                                    newJobDat.setF_NAME(subgroupDataVO.getJobName());
                                    newJobDat.setF_JBGP(jobGrpMap.get(subgroupDataVO.getJobGrpName()).getF_JBGP());
                                    newJobDat.setF_EDUE(emplInfDto.getF_EMPL());
                                    newJobDat.setF_CRUE(emplInfDto.getF_EMPL());
                                    newJobDat.setF_JOB(JudgeUtils.defaultIdentifierGenerator.nextId(DateUtils.toUtc(DateUtils.getNowDate())));
                                    newJobDat.setIsAdd(YesOrNoEnum.YES.getType());
                                } else {
                                    newJobDat = list.get(0);
                                }
                                return newJobDat;
                            });
                            subgroupDataVO.setF_JOB(jobDat.getF_JOB());
                        }

                        /*判断班次组是否输入的名称*/
                        if (StringUtils.isNotEmpty(subgroupDataVO.getShiftGrpName())) {
                            String shiftGrpKey = subgroupDataVO.getShiftGrpName();
                            SHIFT_GRP shiftGrp = shiftGrpMap.computeIfAbsent(shiftGrpKey, k -> {
                                /*先查询*/
                                LambdaQueryWrapper<SHIFT_GRP> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(SHIFT_GRP::getF_NAME, subgroupDataVO.getShiftGrpName())
                                        .eq(SHIFT_GRP::getF_DEL, DelFlagEnum.USE.getType());
                                List<SHIFT_GRP> list = shiftGrpService.list(queryWrapper);
                                SHIFT_GRP newShiftGrp = new SHIFT_GRP();
                                if (CollectionUtils.isEmpty(list)) {
                                    newShiftGrp.setF_NAME(subgroupDataVO.getShiftGrpName());
                                    newShiftGrp.setF_EDUE(emplInfDto.getF_EMPL());
                                    newShiftGrp.setF_CRUE(emplInfDto.getF_EMPL());
                                    newShiftGrp.setF_SHGP(JudgeUtils.defaultIdentifierGenerator.nextId(DateUtils.toUtc(DateUtils.getNowDate())));
                                    newShiftGrp.setIsAdd(YesOrNoEnum.YES.getType());
                                } else {
                                    newShiftGrp = list.get(0);
                                }
                                return newShiftGrp;
                            });

                            subgroupDataVO.setF_SHGP(shiftGrp.getF_SHGP());
                        }

                        /*判断班次是否输入的名称*/
                        if (StringUtils.isNotEmpty(subgroupDataVO.getShiftName())) {
                            if (StringUtils.isEmpty(subgroupDataVO.getF_SHGP())) {
                                log.info("班次组未指定!");
                                return;
                            }
                            String shiftDatKey = subgroupDataVO.getShiftName() + subgroupDataVO.getShiftGrpName();
                            SHIFT_DAT shiftDat = shiftDatMap.computeIfAbsent(shiftDatKey, k -> {
                                /*先查询*/
                                LambdaQueryWrapper<SHIFT_DAT> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(SHIFT_DAT::getF_NAME, subgroupDataVO.getShiftName())
                                        .eq(SHIFT_DAT::getF_SHGP, subgroupDataVO.getF_SHGP())
                                        .eq(SHIFT_DAT::getF_DEL, DelFlagEnum.USE.getType());
                                List<SHIFT_DAT> list = shiftDatService.list(queryWrapper);
                                SHIFT_DAT newShiftDat = new SHIFT_DAT();
                                if (CollectionUtils.isEmpty(list)) {
                                    newShiftDat.setF_NAME(subgroupDataVO.getShiftName());
                                    newShiftDat.setF_SHGP(shiftGrpMap.get(subgroupDataVO.getShiftGrpName()).getF_SHGP());
                                    newShiftDat.setF_EDUE(emplInfDto.getF_EMPL());
                                    newShiftDat.setF_CRUE(emplInfDto.getF_EMPL());
                                    newShiftDat.setF_SHIFT(JudgeUtils.defaultIdentifierGenerator.nextId(DateUtils.toUtc(DateUtils.getNowDate())));
                                    newShiftDat.setIsAdd(YesOrNoEnum.YES.getType());
                                } else {
                                    newShiftDat = list.get(0);
                                }
                                return newShiftDat;
                            });
                            subgroupDataVO.setF_SHIFT(shiftDat.getF_SHIFT());
                        }

                        if (CollectionUtils.isNotEmpty(subgroupDataVO.getSgrpDscList())) {
                            subgroupDataVO.getSgrpDscList().forEach(sgrpDsc -> {

                                /*判断描述符组是否输入的名称*/
                                if (StringUtils.isNotEmpty(sgrpDsc.getDescGrpName())) {
                                    String descGrpKey = sgrpDsc.getDescGrpName();
                                    DESC_GRP descGrp = descGrpMap.computeIfAbsent(descGrpKey, k -> {
                                        /*先查询*/
                                        LambdaQueryWrapper<DESC_GRP> queryWrapper = new LambdaQueryWrapper<>();
                                        queryWrapper.eq(DESC_GRP::getF_NAME, sgrpDsc.getDescGrpName())
                                                .eq(DESC_GRP::getF_DEL, DelFlagEnum.USE.getType());
                                        List<DESC_GRP> list = descGrpService.list(queryWrapper);
                                        DESC_GRP newDescGrp = new DESC_GRP();
                                        if (CollectionUtils.isEmpty(list)) {
                                            newDescGrp.setF_NAME(sgrpDsc.getDescGrpName());
                                            newDescGrp.setF_EDUE(emplInfDto.getF_EMPL());
                                            newDescGrp.setF_CRUE(emplInfDto.getF_EMPL());
                                            newDescGrp.setF_DSGP(JudgeUtils.defaultIdentifierGenerator.nextId(DateUtils.toUtc(DateUtils.getNowDate())));
                                            newDescGrp.setIsAdd(YesOrNoEnum.YES.getType());
                                        } else {
                                            newDescGrp = list.get(0);
                                        }
                                        return newDescGrp;
                                    });
                                    sgrpDsc.setF_DSGP(descGrp.getF_DSGP());
                                }

                                /*判断描述符是否输入的名称*/
                                if (StringUtils.isNotEmpty(sgrpDsc.getDescName())) {
                                    if (StringUtils.isEmpty(sgrpDsc.getF_DSGP())) {
                                        log.info("描述符组名称未指定");
                                        return;
                                    }
                                    String descDatKey = sgrpDsc.getDescName() + sgrpDsc.getDescGrpName();
                                    DESC_DAT descDat = descDatMap.computeIfAbsent(descDatKey, k -> {
                                        /*先查询*/
                                        LambdaQueryWrapper<DESC_DAT> queryWrapper = new LambdaQueryWrapper<>();
                                        queryWrapper.eq(DESC_DAT::getF_NAME, sgrpDsc.getDescName())
                                                .eq(DESC_DAT::getF_DSGP, sgrpDsc.getF_DSGP())
                                                .eq(DESC_DAT::getF_DEL, DelFlagEnum.USE.getType());
                                        List<DESC_DAT> list = descDatService.list(queryWrapper);
                                        DESC_DAT newDescDat = new DESC_DAT();
                                        if (CollectionUtils.isEmpty(list)) {
                                            newDescDat.setF_NAME(sgrpDsc.getDescName());
                                            newDescDat.setF_DSGP(descGrpMap.get(sgrpDsc.getDescGrpName()).getF_DSGP());
                                            newDescDat.setF_EDUE(emplInfDto.getF_EMPL());
                                            newDescDat.setF_CRUE(emplInfDto.getF_EMPL());
                                            newDescDat.setF_DESC(JudgeUtils.defaultIdentifierGenerator.nextId(DateUtils.toUtc(DateUtils.getNowDate())));
                                            newDescDat.setIsAdd(YesOrNoEnum.YES.getType());
                                        } else {
                                            newDescDat = list.get(0);
                                        }
                                        return newDescDat;
                                    });
                                    sgrpDsc.setF_DESC(descDat.getF_DESC());
                                }
                            });
                        }

                        /*判断是缺陷代码和测试是否输入的名称*/
                        long l1 = System.currentTimeMillis();
                        subgroupDataVO.getSgrpValChildDtoList().forEach(sgrpValChildDto -> {
                            if (CollectionUtils.isEmpty(sgrpValChildDto.getTestList())) {
                                throw new BusinessException(DataManagementExceptionEnum.TEST_VAL_IS_NULL);
                            }
                            /*判断测试是否输入的名称*/
                            if (StringUtils.isNotEmpty(sgrpValChildDto.getTestName()) && sgrpValChildDto.getTestId() == null) {
                                String testKey = sgrpValChildDto.getTestName();
                                TEST_INF testInf = testMap.computeIfAbsent(testKey, k -> {
                                    /*先查询*/
                                    LambdaQueryWrapper<TEST_INF> queryWrapper = new LambdaQueryWrapper<>();
                                    queryWrapper.eq(TEST_INF::getF_NAME, sgrpValChildDto.getTestName())
                                            .eq(TEST_INF::getF_PLNT, subgroupDataVO.getF_PLNT())
                                            .eq(TEST_INF::getF_DEL, DelFlagEnum.USE.getType());
                                    List<TEST_INF> list = testInfService.list(queryWrapper);
                                    TEST_INF newTestInf = new TEST_INF();
                                    if (CollectionUtils.isEmpty(list)) {
                                        newTestInf.setF_NAME(sgrpValChildDto.getTestName());
                                        newTestInf.setF_EDUE(emplInfDto.getF_EMPL());
                                        newTestInf.setF_CRUE(emplInfDto.getF_EMPL());
                                        newTestInf.setF_PLNT(subgroupDataVO.getF_PLNT());
                                        newTestInf.setF_TEST(JudgeUtils.defaultIdentifierGenerator.nextId(DateUtils.toUtc(DateUtils.getNowDate())));
                                        newTestInf.setIsAdd(YesOrNoEnum.YES.getType());
                                    } else {
                                        newTestInf = list.get(0);
                                    }
                                    return newTestInf;
                                });
                                sgrpValChildDto.setTestId(testInf.getF_TEST());
                            }
                            sgrpValChildDto.getTestList().forEach(test -> {
                                /*判断缺陷代码组是否输入的名称*/
                                TEST_INF testInf = testMap.get(sgrpValChildDto.getTestName());
                                if (StringUtils.isEmpty(testInf.getF_DFGP()) ||
                                        testInf.getF_DFGP().equals(DelFlagEnum.USE.getType())) {
                                    /*新增缺陷代码组*/
                                    testInf.setF_DFGP(defGrpId);
                                }

                                if (StringUtils.isNotEmpty(test.getDefectName())) {
                                    String defDatKey = test.getDefectName() + sgrpValChildDto.getTestName();
                                    DEF_DAT defDat = defDatMap.computeIfAbsent(defDatKey, k -> {
                                        /*先查询*/
                                        LambdaQueryWrapper<DEF_DAT> queryWrapper = new LambdaQueryWrapper<>();
                                        queryWrapper.eq(DEF_DAT::getF_NAME, test.getDefectName())
                                                .eq(DEF_DAT::getF_DFGP, testInf.getF_DFGP())
                                                .eq(DEF_DAT::getF_DEL, DelFlagEnum.USE.getType());
                                        List<DEF_DAT> list = defDatService.list(queryWrapper);
                                        DEF_DAT newDefDat = new DEF_DAT();
                                        if (CollectionUtils.isEmpty(list)) {
                                            newDefDat.setF_DFGP(testInf.getF_DFGP());
                                            newDefDat.setF_NAME(test.getDefectName());
                                            newDefDat.setF_EDUE(emplInfDto.getF_EMPL());
                                            newDefDat.setF_CRUE(emplInfDto.getF_EMPL());
                                            newDefDat.setF_DEF(JudgeUtils.defaultIdentifierGenerator.nextId(DateUtils.toUtc(DateUtils.getNowDate())));
                                            newDefDat.setIsAdd(YesOrNoEnum.YES.getType());
                                        } else {
                                            newDefDat = list.get(0);
                                        }
                                        return newDefDat;
                                    });
                                    test.setDefectId(defDat.getF_DEF());
                                } else {
                                    testInf.setF_DFGP(0L);
                                }
                            });
                        });
                        long l2 = System.currentTimeMillis();
                        log.info("测试描述符记录耗时-------->{}", l2 - l1);
                        log.info("一条记录耗时-------->{}", l1 - l);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    } finally {
//                        log.info("剩余数量 : {}", countDownLatch.getCount());
//                        countDownLatch.countDown();
//                    }
//                });
            });
//            try {
//                countDownLatch.await();
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
            if (MapUtils.isNotEmpty(partMap)) {
                Set<PART_INF> partInfList = partMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(partInfList)) {
                    partInfService.saveBatch(partInfList);
                }
            }
            if (MapUtils.isNotEmpty(ptrvMap)) {
                Set<PART_REV> partRevList = ptrvMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(partRevList)) {
                    partRevService.saveBatch(partRevList);
                }
            }
            if (MapUtils.isNotEmpty(prcsMap)) {
                Set<PRCS_INF> prcsInfList = prcsMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(prcsInfList)) {
                    prcsInfService.saveBatch(prcsInfList);
                }
            }
            if (MapUtils.isNotEmpty(lotMap)) {
                Set<LOT_INF> lotInfList = lotMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(lotInfList)) {
                    lotInfService.saveBatch(lotInfList);
                }
            }
            if (MapUtils.isNotEmpty(jobGrpMap)) {
                Set<JOB_GRP> jobGrpList = jobGrpMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(jobGrpList)) {
                    jobGrpService.saveBatch(jobGrpList);
                }
            }
            if (MapUtils.isNotEmpty(jobDatMap)) {
                Set<JOB_DAT> jobDatList = jobDatMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(jobDatList)) {
                    jobDatService.saveBatch(jobDatList);
                }
            }
            if (MapUtils.isNotEmpty(shiftGrpMap)) {
                Set<SHIFT_GRP> shiftGrpList = shiftGrpMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(shiftGrpList)) {
                    shiftGrpService.saveBatch(shiftGrpList);
                }
            }
            if (MapUtils.isNotEmpty(shiftDatMap)) {
                Set<SHIFT_DAT> shiftDatList = shiftDatMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(shiftDatList)) {
                    shiftDatService.saveBatch(shiftDatList);
                }
            }
            if (MapUtils.isNotEmpty(descGrpMap)) {
                Set<DESC_GRP> descGrpList = descGrpMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(descGrpList)) {
                    descGrpService.saveBatch(descGrpList);
                }
            }
            if (MapUtils.isNotEmpty(descDatMap)) {
                Set<DESC_DAT> descDatList = descDatMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(descDatList)) {
                    descDatService.saveBatch(descDatList);
                }
            }
            if (MapUtils.isNotEmpty(defGrpMap)) {
                Set<DEF_GRP> defGrpList = defGrpMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(defGrpList)) {
                    defGrpService.saveBatch(defGrpList);
                }
            }
            if (MapUtils.isNotEmpty(defDatMap)) {
                Set<DEF_DAT> defDatList = defDatMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(defDatList)) {
                    defDatService.saveBatch(defDatList);
                }
            }
            if (MapUtils.isNotEmpty(testMap)) {
                Set<TEST_INF> testInfList = testMap.values().stream().filter(s -> s.getIsAdd() == YesOrNoEnum.YES.getType()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(testInfList)) {
                    testInfService.saveBatch(testInfList);
                }
            }

            long end = System.currentTimeMillis();
            log.info("批量导入耗时---------->{}", end - start);
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        } finally {
            lock.unlock();
        }
        return false;
    }

//    private void buildRev(SubgroupDataVO subgroupDataVO, EMPL_INF_DTO emplInfDto, ConcurrentHashMap<String, PART_REV> ptrvMap) {
//        if (StringUtils.isEmpty(subgroupDataVO.getPtrvName()) && StringUtils.isEmpty(subgroupDataVO.getF_REV())) {
//            PART_REV partRev = new PART_REV();
//            partRev.setF_PART(subgroupDataVO.getF_PART());
//            partRev.setF_STTM(DateUtils.toUtc(DateUtils.getNowDate()));
//            partRev.setF_FNTM(DateUtils.parseDate("2099-01-01"));
//            partRev.setF_CRUE(emplInfDto.getF_EMPL());
//            partRev.setF_EDUE(emplInfDto.getF_EMPL());
//            String fprtvName = subgroupDataVO.getPtrvName();
//            if(StringUtils.isBlank(fprtvName)){
//                SysyemGlobalConfig systemConfig = null;
//                try {
//                    systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
//                } catch (Exception e) {
//                    log.error("获取系统配置信息失败 ex:{}",e.getMessage(), e);
//                    throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
//                }
//                fprtvName = systemConfig.getBasicConfig().getPartRevName();
//            }
//            partRev.setF_NAME(fprtvName);
//            partRev.setF_PTRV(JudgeUtils.defaultIdentifierGenerator.nextId(null));
//            String ptrvKey = partRev.getF_NAME() + subgroupDataVO.getPartName();
//            final PART_REV partRevExist = ptrvMap.get(ptrvKey);
//            if (ObjectUtils.isEmpty(partRevExist)) {
//                partRev.setIsAdd(YesOrNoEnum.YES.getType());
//                ptrvMap.put(ptrvKey, partRev);
//                subgroupDataVO.setF_REV(partRev.getF_PTRV());
//                subgroupDataVO.setPtrvName(partRev.getF_NAME());
//            }else{
//                subgroupDataVO.setF_REV(partRevExist.getF_PTRV());
//                subgroupDataVO.setPtrvName(partRevExist.getF_NAME());
//            }
//        }
//        if (StringUtils.isEmpty(subgroupDataVO.getPtrvName()) && StringUtils.isNotEmpty(subgroupDataVO.getF_REV())) {
//            LambdaQueryWrapper<PART_REV> revQueryWrapper = new LambdaQueryWrapper<>();
//            revQueryWrapper.eq(PART_REV::getF_PART, subgroupDataVO.getF_PART())
//                    .eq(PART_REV::getF_PTRV, subgroupDataVO.getF_REV())
//                    .eq(PART_REV::getF_DEL, DelFlagEnum.USE.getType())
//                    .le(PART_REV::getF_STTM, DateUtils.toUtc(DateUtils.getNowDate()))
//                    .orderByDesc(PART_REV::getF_STTM);
//            List<PART_REV> revList = partRevService.list(revQueryWrapper);
//            if (CollectionUtils.isNotEmpty(revList)) {
//                subgroupDataVO.setF_REV(revList.get(0).getF_PTRV());
//                subgroupDataVO.setPtrvName(revList.get(0).getF_NAME());
//            }else{
//                PART_REV partRev = new PART_REV();
//                partRev.setF_PART(subgroupDataVO.getF_PART());
//                partRev.setF_STTM(DateUtils.toUtc(DateUtils.getNowDate()));
//                partRev.setF_FNTM(DateUtils.parseDate("2099-01-01"));
//                partRev.setF_CRUE(emplInfDto.getF_EMPL());
//                partRev.setF_EDUE(emplInfDto.getF_EMPL());
//                String fprtvName = subgroupDataVO.getPtrvName();
//                if(StringUtils.isBlank(fprtvName)){
//                    SysyemGlobalConfig systemConfig = null;
//                    try {
//                        systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
//                    } catch (Exception e) {
//                        log.error("获取系统配置信息失败 ex:{}",e.getMessage(), e);
//                        throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
//                    }
//                    fprtvName = systemConfig.getBasicConfig().getPartRevName();
//                }
//                partRev.setF_NAME(fprtvName);
//                partRev.setF_PTRV(JudgeUtils.defaultIdentifierGenerator.nextId(null));
//                String ptrvKey = partRev.getF_NAME() + subgroupDataVO.getPartName();
//                final PART_REV partRevExist = ptrvMap.get(ptrvKey);
//                if (ObjectUtils.isEmpty(partRevExist)) {
//                    partRev.setIsAdd(YesOrNoEnum.YES.getType());
//                    ptrvMap.put(ptrvKey, partRev);
//                    subgroupDataVO.setF_REV(partRev.getF_PTRV());
//                    subgroupDataVO.setPtrvName(partRev.getF_NAME());
//                }else{
//                    subgroupDataVO.setF_REV(partRevExist.getF_PTRV());
//                    subgroupDataVO.setPtrvName(partRevExist.getF_NAME());
//                }
//            }
//        }
//        if (StringUtils.isNotEmpty(subgroupDataVO.getPtrvName()) && StringUtils.isEmpty(subgroupDataVO.getF_REV())) {
//            LambdaQueryWrapper<PART_REV> revQueryWrapper = new LambdaQueryWrapper<>();
//            revQueryWrapper.eq(PART_REV::getF_PART, subgroupDataVO.getF_PART())
//                    .eq(PART_REV::getF_NAME, subgroupDataVO.getPtrvName())
//                    .eq(PART_REV::getF_DEL, DelFlagEnum.USE.getType())
//                    .le(PART_REV::getF_STTM, DateUtils.toUtc(DateUtils.getNowDate()))
//                    .orderByDesc(PART_REV::getF_STTM);
//            List<PART_REV> revList = partRevService.list(revQueryWrapper);
//            if (CollectionUtils.isNotEmpty(revList)) {
//                subgroupDataVO.setF_REV(revList.get(0).getF_PTRV());
//                subgroupDataVO.setPtrvName(revList.get(0).getF_NAME());
//            }else{
//                PART_REV partRev = new PART_REV();
//                partRev.setF_PART(subgroupDataVO.getF_PART());
//                partRev.setF_STTM(DateUtils.toUtc(DateUtils.getNowDate()));
//                partRev.setF_FNTM(DateUtils.parseDate("2099-01-01"));
//                partRev.setF_CRUE(emplInfDto.getF_EMPL());
//                partRev.setF_EDUE(emplInfDto.getF_EMPL());
//                partRev.setF_NAME(subgroupDataVO.getPtrvName());
//                partRev.setF_PTRV(JudgeUtils.defaultIdentifierGenerator.nextId(null));
//                String ptrvKey = partRev.getF_NAME() + subgroupDataVO.getPartName();
//                final PART_REV partRevExist = ptrvMap.get(ptrvKey);
//                if (ObjectUtils.isEmpty(partRevExist)) {
//                    partRev.setIsAdd(YesOrNoEnum.YES.getType());
//                    ptrvMap.put(ptrvKey, partRev);
//                    subgroupDataVO.setF_REV(partRev.getF_PTRV());
//                    subgroupDataVO.setPtrvName(partRev.getF_NAME());
//                }else{
//                    subgroupDataVO.setF_REV(partRevExist.getF_PTRV());
//                    subgroupDataVO.setPtrvName(partRevExist.getF_NAME());
//                }
//            }
//        }
//    }
private void buildRev(SubgroupDataVO subgroupDataVO, EMPL_INF_DTO emplInfDto, ConcurrentHashMap<String, PART_REV> ptrvMap) {
    String ptrvName = subgroupDataVO.getPtrvName();
    Long fRev = subgroupDataVO.getF_REV();

    // 尝试获取已存在的版本记录
    PART_REV partRev = getExistingPartRev(subgroupDataVO, ptrvName, fRev);

    if (partRev == null) {
        // 创建新的版本记录
        partRev = createNewPartRev(subgroupDataVO, emplInfDto, ptrvName);
        String ptrvKey = partRev.getF_NAME() + subgroupDataVO.getPartName();
        final PART_REV partRevExist = ptrvMap.get(ptrvKey);
        if (ObjectUtils.isEmpty(partRevExist)) {
            partRev.setIsAdd(YesOrNoEnum.YES.getType());
            ptrvMap.put(ptrvKey, partRev);
        } else {
            partRev = partRevExist;
        }
    }

    // 更新 SubgroupDataVO 的版本信息
    subgroupDataVO.setF_REV(partRev.getF_PTRV());
    subgroupDataVO.setPtrvName(partRev.getF_NAME());
}

    /**
     * 尝试获取已存在的版本记录
     * @param subgroupDataVO 子组数据对象
     * @param ptrvName 版本名称
     * @param fRev 版本 ID
     * @return 已存在的版本记录，不存在则返回 null
     */
    private PART_REV getExistingPartRev(SubgroupDataVO subgroupDataVO, String ptrvName, Long fRev) {
        LambdaQueryWrapper<PART_REV> revQueryWrapper = new LambdaQueryWrapper<>();
       //查询产品版本
        revQueryWrapper.eq(PART_REV::getF_PART, subgroupDataVO.getF_PART())
                .eq(PART_REV::getF_DEL, DelFlagEnum.USE.getType())
                .le(PART_REV::getF_STTM, DateUtils.toUtc(DateUtils.getNowDate()))
                .orderByDesc(PART_REV::getF_STTM);

        //根据版本ID查询
        if (fRev != null) {
            revQueryWrapper.eq(PART_REV::getF_PTRV, fRev);
        }
        //根据版本名称查询
        else if (StringUtils.isNotEmpty(ptrvName)) {
            revQueryWrapper.eq(PART_REV::getF_NAME, ptrvName);
        }

        List<PART_REV> revList = partRevService.list(revQueryWrapper);
        return CollectionUtils.isNotEmpty(revList) ? revList.get(0) : null;
    }

    /**
     * 创建新的版本记录
     * @param subgroupDataVO 子组数据对象
     * @param emplInfDto 员工信息对象
     * @param ptrvName 版本名称
     * @return 新创建的版本记录
     */
    private PART_REV createNewPartRev(SubgroupDataVO subgroupDataVO, EMPL_INF_DTO emplInfDto, String ptrvName) {
        PART_REV partRev = new PART_REV();
        partRev.setF_PART(subgroupDataVO.getF_PART());
        partRev.setF_STTM(DateUtils.toUtc(DateUtils.getNowDate()));
        partRev.setF_FNTM(DateUtils.parseDate("2099-01-01"));
        partRev.setF_CRUE(emplInfDto.getF_EMPL());
        partRev.setF_EDUE(emplInfDto.getF_EMPL());

        if (StringUtils.isBlank(ptrvName)) {
            SysyemGlobalConfig systemConfig = null;
            try {
                systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
            } catch (Exception e) {
                log.error("获取系统配置信息失败 ex:{}", e.getMessage(), e);
                throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
            }
            ptrvName = systemConfig.getBasicConfig().getPartRevName();
        }
        partRev.setF_NAME(ptrvName);
        partRev.setF_PTRV(JudgeUtils.defaultIdentifierGenerator.nextId(null));
        return partRev;
    }
    @Override
    @Transactional
    public void batchAdd(FileNameDataDTO fileNameDataDTO, List<SubgroupDataVO> subgroupDataVOList) {
        /*判断是否要清除MQ消息*/
        Integer cacheObject = redisService.getCacheObject(RedisConstant.CLEAR_MQ + fileNameDataDTO.getJobId());
        if (cacheObject != null) {
            log.info("需要清空MQ消息,不执行后续逻辑");
            return;
        }
        /*构造子组内容*/
        if (extracted(subgroupDataVOList)) {
            log.info("构造子组内容出错");
            return;
        }
        /*记录正在处理的文件*/
        redisService.setCacheMapValue(String.format(RedisConstant.BE_DEALING_WITH, fileNameDataDTO.getJobId()), fileNameDataDTO.getName(), subgroupDataVOList, Constants.REDIS_EXPIRE_TIME);
        remoteMqService.batchSend(fileNameDataDTO);
    }


    /**
     * 缓存
     */
    public static Map<String, String> cacheMap = new HashMap<>();
    public static final String startVal = "dbCollect_startVal:";
    public static final String startKey = "dbCollect_startKey:";

    public static String saveMapKey = "item_";
    public static String testMapKey = "testItem_";

    /**
     * 数据采集:数据库类型
     *
     * @return
     */
    @Override
    public List<SubgroupDataVO> autoCollectDb(DataCollectionVO dataCollectionVO, SCHEDULE_JOB_INF scheduleJob) {
        List<SubgroupDataVO> list = new ArrayList<>();
        /*工艺流程缓存*/
        Map<String, MANUFACTURING_PROCESS_INF> mfpsMap = new HashMap<>();
        /*工艺节点缓存*/
        Map<String, MANUFACTURING_NODE_INF> mfndMap = new HashMap<>();
        /*检查计划缓存*/
        Map<String, INSPECTION_PLAN_INF> planMap = new HashMap<>();
        String handleSql = dataCollectionVO.getHandleSql();

        /*启动条件缓存*/
        String startValKey = startVal + scheduleJob.getF_SJOB();
        if (dataCollectionVO.getStartType() == 1) {
            /*改成从缓存取*/
            String startSql = cacheMap.get(startValKey);
            /*处理sql占位符  动态匹配*/
            String placeholder = cacheMap.get(startKey + scheduleJob.getF_SJOB());
            if (startSql == null) {
                /*判断redis里是否有值*/
                String cache = redisService.getCacheObject(startValKey);
                if (StringUtils.isNotEmpty(cache)) {
                    String[] split = cache.split("@-@");
                    startSql = split[1];
                    placeholder = split[0];
                    if (cacheMap.get(startValKey) == null) {
                        cacheMap.put(startValKey, startSql);
                        cacheMap.put(startKey + scheduleJob.getF_SJOB(), split[0]);
                    }
                } else {
                    /*启动sql数据*/
                    List<Map<String, Object>> mapList = dbConfigInfService.testSql(scheduleJob.getF_BUID(), dataCollectionVO.getStartSql());
                    /*只取一个*/
                    String key = mapList.get(0).keySet().toArray()[0].toString();
                    cacheMap.put(startValKey, mapList.get(0).get(key).toString());
                    redisService.setCacheObject(startValKey, key + "@-@" + mapList.get(0).get(key).toString());
                    cacheMap.put(startKey + scheduleJob.getF_SJOB(), key);
                    startSql = cacheMap.get(startValKey);
                    placeholder = key;
                }
            }
            /*处理sql数据*/
            handleSql = handleSql.replace("{" + placeholder + "}", startSql).replace("&gt;", ">").replace("&lt;", "<");
        }
        if (handleSql.contains("{")) return list;

        /*获取数据*/
        List<Map<String, Object>> mapList = dbConfigInfService.testSql(scheduleJob.getF_BUID(), handleSql);
        DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO = dataCollectionVO.getDataFileAutoCollectConfigDTO();
        List<DataColumnMappingDTO> dataColumnMappingDTOList = dataFileAutoCollectConfigDTO.getDataColumnMappingDTOList();
        /*保存条件map*/
        Map<String, String> saveMapCache = new HashMap<>();
        Map<String, String> redisCache = redisService.getCacheMap(RedisConstant.IDLE_TIME_CACHE + scheduleJob.getF_SJOB());
        if (redisCache != null) saveMapCache = redisCache;

        /*判断是否有闲置子组*/
        SubgroupDataVO subgroupDataVO = redisService.getCacheObject(RedisConstant.IDLE_TIME_DATA + scheduleJob.getF_SJOB());
        if (subgroupDataVO == null) subgroupDataVO = new SubgroupDataVO();
        int size = mapList.size();
        /*固定行数判断*/
        Integer num = 0;
        int total = 0;
        for (Map<String, Object> map : mapList) {
            //是否满足保存条件
            total++;
            /*固定行保存判断*/
            boolean isSaveSubgroup = false;
            /*动态保存条件*/
            boolean dySaveSubgroup = false;
            if (dataFileAutoCollectConfigDTO.getFixedLine() != null) {
                num++;
                Integer fixedLine = dataFileAutoCollectConfigDTO.getFixedLine();
                if (Objects.equals(fixedLine, num)) {
                    isSaveSubgroup = true;
                    num = 0;
                }
            }
            if (!isSaveSubgroup)
                dySaveSubgroup = ParseDataFileUtil.getSaveCondition(dataCollectionVO.getStatus(), dataFileAutoCollectConfigDTO, saveMapCache, null, map);
            if (dySaveSubgroup) {
                if (dataFileAutoCollectConfigDTO.getSaveType() == 1) {
                    /*上一条满足条件子组保存(不包含当前子组)*/
                    if (StringUtils.isNotEmpty(subgroupDataVO.getMfpsName())) {
                        list.add(subgroupDataVO);
                        subgroupDataVO = new SubgroupDataVO();
                        num = 0;
                        /*为当前子组重新定义动态保存条件*/
                        dySaveSubgroup = false;
                    }
                }
            }
            subgroupDataVO.setF_PLNT(dataCollectionVO.getF_PLNT());
            List<String> testNameList = new ArrayList<>();
            for (DataColumnMappingDTO dataColumnMappingDTO : dataColumnMappingDTOList) {
                switch (DbFiledEnum.getType(dataColumnMappingDTO.getFiled())) {
                    case STRUCTURE:
                        List<DataColumnMappingDTO> dataColumnMappingDTOS = dataColumnMappingDTO.getDataColumnMappingDTOList();
                        if (CollectionUtils.isEmpty(dataColumnMappingDTOList) && dataColumnMappingDTOList.size() != 4) {
                            throw new BusinessException(DataManagementExceptionEnum.STRUCTURE_UNDER_CONFIGURATION);
                        }
                        /*排序成有序数组*/
                        dataColumnMappingDTOS = dataColumnMappingDTOS.stream()
                                .sorted(Comparator.comparingInt(DataColumnMappingDTO::getFiled)).collect(Collectors.toList());
                        for (DataColumnMappingDTO columnMappingDTO : dataColumnMappingDTOS) {
                            log.info("执行结构顺序------------->{}", columnMappingDTO.getFiled());
                            switch (DbFiledEnum.getType(columnMappingDTO.getFiled())) {
                                case MFPS_DAT:
                                    if (StringUtils.isNotEmpty(subgroupDataVO.getMfpsName())) continue;
                                    String mfpsName = DbCollectionUtil.getItemName(columnMappingDTO.getType(),
                                            columnMappingDTO.getAnalyticRuleList(),
                                            columnMappingDTO.getColumnName(), map);
                                    if (StringUtils.isEmpty(mfpsName))
                                        throw new BusinessException(DataManagementExceptionEnum.MFPS_NOT_EXISTS);
                                    subgroupDataVO.setMfpsName(mfpsName);
                                    MANUFACTURING_PROCESS_INF manufacturingProcessInf;
                                    if (mfpsMap.get(subgroupDataVO.getMfpsName()) != null) {
                                        manufacturingProcessInf = mfpsMap.get(subgroupDataVO.getMfpsName());
                                        subgroupDataVO.setF_MFPS(manufacturingProcessInf.getF_MFPS());
                                    } else {
                                        LambdaQueryWrapper<MANUFACTURING_PROCESS_INF> mfpsQueryWrapper = new LambdaQueryWrapper<>();
                                        mfpsQueryWrapper.eq(MANUFACTURING_PROCESS_INF::getF_NAME, subgroupDataVO.getMfpsName())
                                                .eq(MANUFACTURING_PROCESS_INF::getF_DEL, DelFlagEnum.USE.getType());
                                        manufacturingProcessInf = manufacturingProcessInfMapper.selectOne(mfpsQueryWrapper);
                                        if (manufacturingProcessInf != null) {
                                            /*判断工艺流程的工厂与所选工厂是否一致*/
                                            if (!manufacturingProcessInf.getF_PLNT().equals(dataCollectionVO.getF_PLNT())) {
                                                throw new BusinessException(DataManagementExceptionEnum.MFPS_PLNT_INCONFORMITY);
                                            }
                                            mfpsMap.put(subgroupDataVO.getMfpsName(), manufacturingProcessInf);
                                            subgroupDataVO.setF_MFPS(manufacturingProcessInf.getF_MFPS());
                                        } else {
                                            throw new BusinessException(DataManagementExceptionEnum.MFPS_NOT_EXISTS);
                                        }
                                    }
                                    break;
                                case MFND_DAT:
                                    if (StringUtils.isNotEmpty(subgroupDataVO.getMfndName())) continue;
                                    String mfndName = DbCollectionUtil.getItemName(columnMappingDTO.getType(),
                                            columnMappingDTO.getAnalyticRuleList(),
                                            columnMappingDTO.getColumnName(), map);
                                    if (StringUtils.isEmpty(mfndName)) {
                                        throw new BusinessException(DataManagementExceptionEnum.MFND_NOT_EXISTS);
                                    }
                                    subgroupDataVO.setMfndName(mfndName);
                                    MANUFACTURING_NODE_INF manufacturingNodeInf;
                                    String key = subgroupDataVO.getMfndName() + subgroupDataVO.getMfpsName();
                                    if (mfndMap.get(key) != null) {
                                        manufacturingNodeInf = mfndMap.get(key);
                                        subgroupDataVO.setF_MFND(manufacturingNodeInf.getF_MFND());
                                    } else {
                                        LambdaQueryWrapper<MANUFACTURING_NODE_INF> mfndQueryWrapper = new LambdaQueryWrapper<>();
                                        mfndQueryWrapper.eq(MANUFACTURING_NODE_INF::getF_NAME, subgroupDataVO.getMfndName())
                                                .eq(MANUFACTURING_NODE_INF::getF_MFPS, subgroupDataVO.getF_MFPS())
                                                .eq(MANUFACTURING_NODE_INF::getF_DEL, DelFlagEnum.USE.getType());
                                        manufacturingNodeInf = manufacturingNodeInfMapper.selectOne(mfndQueryWrapper);
                                        if (manufacturingNodeInf != null) {
                                            mfndMap.put(key, manufacturingNodeInf);
                                            subgroupDataVO.setF_MFND(manufacturingNodeInf.getF_MFND());
                                        } else {
                                            throw new BusinessException(DataManagementExceptionEnum.MFND_NOT_EXISTS);
                                        }
                                    }
                                    subgroupDataVO.setF_MFND(manufacturingNodeInf.getF_MFND());
                                    break;
                                case PLAN_DAT:
                                    if (StringUtils.isNotEmpty(subgroupDataVO.getPlanName())) continue;
                                    String planName = DbCollectionUtil.getItemName(columnMappingDTO.getType(),
                                            columnMappingDTO.getAnalyticRuleList(),
                                            columnMappingDTO.getColumnName(), map);
                                    subgroupDataVO.setPlanName(planName);
                                    INSPECTION_PLAN_INF inspectionPlanInf;
                                    String planKey = subgroupDataVO.getPlanName() + subgroupDataVO.getMfndName() + subgroupDataVO.getMfpsName();
                                    if (planMap.get(planKey) != null) {
                                        inspectionPlanInf = planMap.get(planKey);
                                        subgroupDataVO.setF_INSP_PLAN(inspectionPlanInf.getF_PLAN());
                                    } else {
                                        LambdaQueryWrapper<INSPECTION_PLAN_INF> queryWrapper = new LambdaQueryWrapper<>();
                                        queryWrapper.eq(INSPECTION_PLAN_INF::getF_NAME, subgroupDataVO.getPlanName())
                                                .eq(INSPECTION_PLAN_INF::getF_MFPS, subgroupDataVO.getF_MFPS())
                                                .eq(INSPECTION_PLAN_INF::getF_MFND, subgroupDataVO.getF_MFND())
                                                .eq(INSPECTION_PLAN_INF::getF_DEL, DelFlagEnum.USE.getType());
                                        inspectionPlanInf = inspectionPlanInfMapper.selectOne(queryWrapper);
                                        if (inspectionPlanInf != null) {
                                            planMap.put(planKey, inspectionPlanInf);
                                            subgroupDataVO.setF_INSP_PLAN(inspectionPlanInf.getF_PLAN());
                                        } else {
                                            throw new BusinessException(DataManagementExceptionEnum.PLAN_NOT_EXISTS);
                                        }
                                    }
                                    subgroupDataVO.setF_INSP_PLAN(inspectionPlanInf.getF_PLAN());
                                    break;
                                case CHILD_DAT:
                                    if (StringUtils.isNotEmpty(subgroupDataVO.getChildId())) continue;
                                    String childName = DbCollectionUtil.getItemName(columnMappingDTO.getType(),
                                            columnMappingDTO.getAnalyticRuleList(),
                                            columnMappingDTO.getColumnName(), map);
                                    String childKey = subgroupDataVO.getPlanName() + subgroupDataVO.getMfndName() + subgroupDataVO.getMfpsName();
                                    if (planMap.get(childKey) != null) {
                                        INSPECTION_PLAN_INF planInf = planMap.get(childKey);
                                        Map<String, List<INSPECTION_PLAN_CHILD_DTO>> childMap =
                                                JSONObject.parseObject(planInf.getF_CHILD())
                                                        .entrySet().stream()
                                                        .collect(Collectors.toMap(Map.Entry::getKey, entry ->
                                                                JSONArray.parseArray(String.valueOf(entry.getValue()), INSPECTION_PLAN_CHILD_DTO.class)));
                                        List<INSPECTION_PLAN_CHILD_DTO> inspectionPlanChildDtoList = childMap.get(childName);
                                        if (inspectionPlanChildDtoList == null) {
                                            throw new BusinessException(DataManagementExceptionEnum.CHILD_NOT_EXISTS);
                                        }
                                        subgroupDataVO.setChildId(inspectionPlanChildDtoList.get(0).getChildId());
                                    } else {
                                        throw new BusinessException(DataManagementExceptionEnum.CHILD_NOT_EXISTS);
                                    }
                                    break;
                            }
                        }
                        break;
                    case PART_DAT:
                        if (StringUtils.isEmpty(subgroupDataVO.getPartName())) {
                            subgroupDataVO.setPartName(DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map));
                        }
                        if (StringUtils.isEmpty(subgroupDataVO.getPtrvName())) {
                            subgroupDataVO.setPtrvName(DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map));
                        }
                        break;
                    case PRCS_DAT:
                        if (StringUtils.isEmpty(subgroupDataVO.getPrcsName())) {
                            subgroupDataVO.setPrcsName(DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map));
                        }
                        break;
                    case SHIFT_DAT:
                        if (StringUtils.isEmpty(subgroupDataVO.getShiftGrpName())) {
                            subgroupDataVO.setShiftName(DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map));
                            subgroupDataVO.setShiftGrpName(DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map));
                        }
                        break;
                    case JOB_DAT:
                        if (StringUtils.isEmpty(subgroupDataVO.getJobGrpName())) {
                            subgroupDataVO.setJobName(DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map));
                            subgroupDataVO.setJobGrpName(DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map));
                        }
                        break;
                    case LOT:
                        if (StringUtils.isEmpty(subgroupDataVO.getLotName())) {
                            subgroupDataVO.setLotName(DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map));
                        }
                        break;
                    case TIME:
                        Date date = DateUtils.parseDate(DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map));
                        subgroupDataVO.setF_SGTM(DateUtils.toUtc(date));
                        break;
                    case DESC_DAT:
                        List<SGRP_DSC> sgrpDscList = subgroupDataVO.getSgrpDscList();
                        if (CollectionUtils.isEmpty(sgrpDscList))
                            sgrpDscList = new ArrayList<>();
                        String descName = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                        String dsgpName = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                        AtomicBoolean atomicBoolean = new AtomicBoolean(true);
                        sgrpDscList.forEach(sgrpDsc -> {
                            if (sgrpDsc.getDescGrpName().equals(dsgpName) && sgrpDsc.getDescName().equals(descName)) {
                                atomicBoolean.set(false);
                            }
                        });
                        if (atomicBoolean.get()) {
                            SGRP_DSC sgrpDsc = new SGRP_DSC();
                            sgrpDsc.setDescName(descName);
                            sgrpDsc.setDescGrpName(dsgpName);
                            sgrpDscList.add(sgrpDsc);
                        }
                        subgroupDataVO.setSgrpDscList(sgrpDscList);
                        break;
                    case TEST_DAT:
                        List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList = subgroupDataVO.getSgrpValChildDtoList();
                        if (CollectionUtils.isEmpty(sgrpValChildDtoList))
                            sgrpValChildDtoList = new ArrayList<>();
                        String testVal = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                        String testName = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                        Double testValue = new BigDecimal(testVal).doubleValue();
                        if (Double.isNaN(testValue)) {
//                            return true;
                        }
                        AtomicReference<SGRP_VAL_CHILD_DTO> dto = new AtomicReference<>();
                        sgrpValChildDtoList.forEach(sgrpValChildDto -> {
                            if (sgrpValChildDto.getTestName().equals(testName)) {
                                dto.set(sgrpValChildDto);
                                if (testNameList.contains(testName) && CollectionUtils.isNotEmpty(testNameList)) {
                                    /*测试名称一致添加子测试*/
                                    List<SGRP_VAL_CHILD_DTO.Test> testList = sgrpValChildDto.getTestList();
                                    if (CollectionUtils.isNotEmpty(testList)) {
                                        List<SGRP_VAL_CHILD_DTO.SubTest> subTestList = testList.get(0).getSubTestList();
                                        if (CollectionUtils.isEmpty(subTestList)) {
                                            subTestList = new ArrayList<>();
                                            SGRP_VAL_CHILD_DTO.SubTest subTest = new SGRP_VAL_CHILD_DTO.SubTest();
                                            /*记录第一个子测试*/
                                            subTest.setSubTestValue(testList.get(0).getTestVal());
                                            subTest.setSubTestNo(YesOrNoEnum.YES.getType());
                                            subTestList.add(subTest);
                                        }
                                        SGRP_VAL_CHILD_DTO.SubTest subTest = new SGRP_VAL_CHILD_DTO.SubTest();
                                        subTest.setSubTestValue(testValue);
                                        subTest.setSubTestNo(subTestList.size() + 1);
                                        subTestList.add(subTest);
                                        testList.get(0).setSubTestList(subTestList);
                                    }
                                } else {
                                    SGRP_VAL_CHILD_DTO.Test test = new SGRP_VAL_CHILD_DTO.Test();
                                    test.setTestNo(sgrpValChildDto.getTestList().size());
                                    test.setTestVal(testValue);
                                    /*获取缺陷代码*/
                                    String defectName = DbCollectionUtil.getItemName(dataColumnMappingDTO.getDefectType(),
                                            dataColumnMappingDTO.getAnalyticRuleDefectList(),
                                            dataColumnMappingDTO.getDefectName(), map);
                                    test.setDefectName(defectName);
                                    sgrpValChildDto.getTestList().add(test);
                                }
                            }
                        });
                        testNameList.add(testName);
                        if (dto.get() == null) {
                            SGRP_VAL_CHILD_DTO sgrpValChildDto = new SGRP_VAL_CHILD_DTO();
                            sgrpValChildDto.setTestName(testName);
                            sgrpValChildDto.setChildId(subgroupDataVO.getChildId());
                            dto.set(sgrpValChildDto);
                            sgrpValChildDtoList.add(sgrpValChildDto);
                        }
                        SGRP_VAL_CHILD_DTO sgrpValChildDto = dto.get();
                        List<SGRP_VAL_CHILD_DTO.Test> testList = sgrpValChildDto.getTestList();
                        if (CollectionUtils.isEmpty(testList)) testList = new ArrayList<>();
                        if (CollectionUtils.isEmpty(testList)) {
                            SGRP_VAL_CHILD_DTO.Test test = new SGRP_VAL_CHILD_DTO.Test();
                            test.setTestNo(testList.size());
                            test.setTestVal(testValue);
                            /*获取缺陷代码*/
                            String defectName = DbCollectionUtil.getItemName(dataColumnMappingDTO.getDefectType(),
                                    dataColumnMappingDTO.getAnalyticRuleDefectList(),
                                    dataColumnMappingDTO.getDefectName(), map);
                            test.setDefectName(defectName);
                            testList.add(test);
                        }
                        sgrpValChildDto.setTestList(testList);
                        subgroupDataVO.setF_SGSZ(testList.size());
                        subgroupDataVO.setSgrpValChildDtoList(sgrpValChildDtoList);
                        break;

                }
            }

            /*判断保存条件*/
            if (isSaveSubgroup) {
                list.add(subgroupDataVO);
                subgroupDataVO = new SubgroupDataVO();
            } else {
                if (!dySaveSubgroup) {
                    dySaveSubgroup = ParseDataFileUtil.getSaveCondition(dataCollectionVO.getStatus(), dataFileAutoCollectConfigDTO, saveMapCache, null, map);
                }
                if (dySaveSubgroup) {
                    if (dataFileAutoCollectConfigDTO.getSaveType() == 2) {
                        list.add(subgroupDataVO);
                        subgroupDataVO = new SubgroupDataVO();
                        num = 0;
                    }
                } else {
                    if (size == total) {
                        /*最后一条记录未满足保存条件保存*/
                        if (dataFileAutoCollectConfigDTO.getSaveIdleTime() != null) {
                            if (dataFileAutoCollectConfigDTO.getSaveIdleTimeUnit() == 0) {
                                redisService.setCacheObject(RedisConstant.IDLE_TIME + scheduleJob.getF_SJOB(), scheduleJob.getF_SJOB(), (long) dataFileAutoCollectConfigDTO.getSaveIdleTime());
                                redisService.setCacheObject(RedisConstant.IDLE_TIME_DATA + scheduleJob.getF_SJOB(), subgroupDataVO, (long) dataFileAutoCollectConfigDTO.getSaveIdleTime() * 2);
                                redisService.setCacheMap(RedisConstant.IDLE_TIME_CACHE + scheduleJob.getF_SJOB(), saveMapCache, (long) dataFileAutoCollectConfigDTO.getSaveIdleTime() * 2);
                            } else {
                                redisService.setCacheObject(RedisConstant.IDLE_TIME + scheduleJob.getF_SJOB(), scheduleJob.getF_SJOB(), dataFileAutoCollectConfigDTO.getSaveIdleTime() * 60L);
                                redisService.setCacheObject(RedisConstant.IDLE_TIME_DATA + scheduleJob.getF_SJOB(), subgroupDataVO, dataFileAutoCollectConfigDTO.getSaveIdleTime() * 60L * 2);
                                redisService.setCacheMap(RedisConstant.IDLE_TIME_CACHE + scheduleJob.getF_SJOB(), saveMapCache, (long) dataFileAutoCollectConfigDTO.getSaveIdleTime() * 60 * 2);
                            }
                        }
                    }

                }
            }
            /*更新启动sql的值*/
            if (map.get(cacheMap.get(startKey + scheduleJob.getF_SJOB())) != null) {
                cacheMap.put(startValKey, map.get(cacheMap.get(startKey + scheduleJob.getF_SJOB())).toString());
                redisService.setCacheObject(startValKey, cacheMap.get(startKey + scheduleJob.getF_SJOB()) + "@-@" + map.get(cacheMap.get(startKey + scheduleJob.getF_SJOB())).toString());
            }
        }

        return list;
    }

    /**
     * 闲置到期保存子组
     */
    @Override
    public void saveSubgroup(String idleTimeKey) {
        String id = idleTimeKey.substring(idleTimeKey.lastIndexOf(":") + 1);
        SubgroupDataVO subgroupDataVO = redisService.getCacheObject(RedisConstant.IDLE_TIME_DATA + id);
        if (subgroupDataVO == null) return;
        List<SubgroupDataVO> arrayList = new ArrayList<>();
        arrayList.add(subgroupDataVO);
        redisService.deleteObject(RedisConstant.IDLE_TIME_DATA + id);
        batchAdd(arrayList);
    }

    /**
     * 保存公差限
     *
     * @param dataFileAutoCollectConfigDTO
     * @param dataColumnList
     * @param specSkip 公差限一样覆盖或跳过(0:否 1:是)
     */
    @Override
    public void saveSpec(DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO, List<List<String>> dataColumnList, Integer specSkip) {
        /*产品缓存*/
        ConcurrentHashMap<String, PART_INF_DTO> partMap = new ConcurrentHashMap<>();
        /*过程缓存*/
        ConcurrentHashMap<String, PRCS_INF> prcsMap = new ConcurrentHashMap<>();
        /*测试缓存*/
        ConcurrentHashMap<String, TEST_INF> testMap = new ConcurrentHashMap<>();
        List<DataColumnMappingDTO> filedList = dataFileAutoCollectConfigDTO.getDataColumnMappingDTOList();
        int total = 0;

        outerLoop:
        for (List<String> dataLine : dataColumnList) {
            try {
                total++;
                SPEC_INF_VO specInf = new SPEC_INF_VO();
                SPEC_ACTIVATION_ALARM_DTO specActivationAlarmDto = new SPEC_ACTIVATION_ALARM_DTO();
                for (DataColumnMappingDTO dataColumnMappingDTO : filedList) {
                    switch (DbSpecFiledEnum.getType(dataColumnMappingDTO.getFiled())) {
                        case PART_DAT:
                            String partName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (ParseDataFileUtil.judgeName(total, partName, DbSpecFiledEnum.PART_DAT.getDesc())) {
                                log.info("产品名称为空 执行下一条");
                                continue outerLoop;
                            }
                            String ptrvName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (ParseDataFileUtil.judgeName(total, ptrvName, DbSpecFiledEnum.PREV_DAT.getDesc())) {
                                SysyemGlobalConfig systemConfig = null;
                                try {
                                    systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
                                } catch (Exception e) {
                                    log.error("获取系统配置信息失败 ex:{}", e.getMessage(), e);
                                    throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
                                }
                                ptrvName = systemConfig.getBasicConfig().getPartRevName();
                            }
                            String partKey = partName + dataFileAutoCollectConfigDTO.getF_PLNT();
                            String finalPtrvName = ptrvName;
                            PART_INF_DTO partInfDto = partMap.computeIfAbsent(partKey, k -> {
                                PART_INF newPart = partInfService.findByName(partName, dataFileAutoCollectConfigDTO.getF_PLNT());
                                PART_INF_DTO newPartDto = new PART_INF_DTO();
                                if (newPart == null) {
                                    newPart = new PART_INF();
                                    newPart.setF_NAME(partName);
                                    newPart.setF_PLNT(dataFileAutoCollectConfigDTO.getF_PLNT());
                                    newPart.setF_EDUE(InitConfig.empl);
                                    newPart.setF_CRUE(InitConfig.empl);
                                    partInfService.save(newPart);

                                    PART_REV partRev = new PART_REV();
                                    partRev.setF_PART(newPart.getF_PART());
                                    partRev.setF_STTM(DateUtils.toUtc(DateUtils.getNowDate()));
                                    partRev.setF_FNTM(DateUtils.parseDate("2099-01-01"));
                                    partRev.setF_CRUE(InitConfig.empl);
                                    partRev.setF_EDUE(InitConfig.empl);
                                    partRev.setF_NAME(finalPtrvName);
                                    partRevService.save(partRev);
                                    newPartDto = BeanUtil.copyProperties(newPart, PART_INF_DTO.class);
                                    newPartDto.setPartRevId(partRev.getF_PTRV());
                                } else {
                                    /*未指定版本则拿产品最新的版本*/
                                    LambdaQueryWrapper<PART_REV> revQueryWrapper = new LambdaQueryWrapper<>();
                                    revQueryWrapper.eq(PART_REV::getF_PART, newPart.getF_PART())
                                            .eq(PART_REV::getF_DEL, DelFlagEnum.USE.getType())
                                            .eq(PART_REV::getF_NAME, finalPtrvName)
                                            .le(PART_REV::getF_STTM, DateUtils.toUtc(DateUtils.getNowDate()))
                                            .orderByDesc(PART_REV::getF_STTM);
                                    List<PART_REV> revList = partRevService.list(revQueryWrapper);
                                    newPartDto = BeanUtil.copyProperties(newPart, PART_INF_DTO.class);
                                    if (CollectionUtils.isNotEmpty(revList)) {
                                        newPartDto.setPartRevId(revList.get(0).getF_PTRV());
                                    } else {
                                        PART_REV partRev = new PART_REV();
                                        partRev.setF_PART(newPart.getF_PART());
                                        partRev.setF_STTM(DateUtils.toUtc(DateUtils.getNowDate()));
                                        partRev.setF_FNTM(DateUtils.parseDate("2099-01-01"));
                                        partRev.setF_CRUE(InitConfig.empl);
                                        partRev.setF_EDUE(InitConfig.empl);
                                        SysyemGlobalConfig systemConfig = null;
                                        try {
                                            systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
                                        } catch (Exception e) {
                                            log.error("获取系统配置信息失败 ex:{}", e.getMessage(), e);
                                            throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
                                        }
                                        final String partRevName = systemConfig.getBasicConfig().getPartRevName();
                                        partRev.setF_NAME(partRevName);
                                        partRev.setF_PTRV(JudgeUtils.defaultIdentifierGenerator.nextId(null));
                                        partRevService.save(partRev);
                                        newPartDto.setPartRevId(partRev.getF_PTRV());
                                    }
                                }
                                return newPartDto;
                            });
                            specInf.setF_PART(partInfDto.getF_PART()).setF_PTRV(partInfDto.getPartRevId());
                            break;
                        case TEST_DAT:
                            String testName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (ParseDataFileUtil.judgeName(total, testName, DbFiledEnum.TEST_DAT.getDesc())) {
                                log.info("测试名称为空 执行下一条");
                                continue outerLoop;
                            }
                            String testKey = testName + dataFileAutoCollectConfigDTO.getF_PLNT();
                            TEST_INF testInf = testMap.computeIfAbsent(testKey, k -> {
                                /*先查询*/
                                LambdaQueryWrapper<TEST_INF> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(TEST_INF::getF_NAME, testName)
                                        .eq(TEST_INF::getF_PLNT, dataFileAutoCollectConfigDTO.getF_PLNT())
                                        .eq(TEST_INF::getF_DEL, DelFlagEnum.USE.getType());
                                List<TEST_INF> list = testInfService.list(queryWrapper);
                                TEST_INF newTestInf = new TEST_INF();
                                if (CollectionUtils.isEmpty(list)) {
                                    newTestInf.setF_NAME(testName);
                                    newTestInf.setF_EDUE(InitConfig.empl);
                                    newTestInf.setF_CRUE(InitConfig.empl);
                                    newTestInf.setF_PLNT(dataFileAutoCollectConfigDTO.getF_PLNT());
                                    testInfService.save(newTestInf);
                                } else {
                                    newTestInf = list.get(0);
                                }
                                return newTestInf;
                            });
                            specInf.setF_TEST(testInf.getF_TEST());
                            break;
                        case PRCS_DAT:
                            String prcsName = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (ParseDataFileUtil.judgeName(total, prcsName, DbFiledEnum.PRCS_DAT.getDesc())) continue;
                            String prcsKey = prcsName + dataFileAutoCollectConfigDTO.getF_PLNT();
                            PRCS_INF prcsInf = prcsMap.computeIfAbsent(prcsKey, k -> {
                                /*先查询*/
                                LambdaQueryWrapper<PRCS_INF> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(PRCS_INF::getF_NAME, prcsName)
                                        .eq(PRCS_INF::getF_PLNT, dataFileAutoCollectConfigDTO.getF_PLNT())
                                        .eq(PRCS_INF::getF_DEL, DelFlagEnum.USE.getType());
                                List<PRCS_INF> list = prcsInfService.list(queryWrapper);
                                PRCS_INF newPrcs = new PRCS_INF();
                                if (CollectionUtils.isEmpty(list)) {
                                    newPrcs.setF_NAME(prcsName);
                                    newPrcs.setF_PLNT(dataFileAutoCollectConfigDTO.getF_PLNT());
                                    newPrcs.setF_EDUE(InitConfig.empl);
                                    newPrcs.setF_CRUE(InitConfig.empl);
                                    prcsInfService.save(newPrcs);
                                } else {
                                    newPrcs = list.get(0);
                                }
                                return newPrcs;
                            });
                            specInf.setF_PRCS(prcsInf.getF_PRCS());
                            break;
                        case USL:
                            String usl = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(usl)) {
                                specInf.setF_USL(Double.valueOf(usl.trim()));
                            }
                            String uslAlarm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (StringUtils.isNotBlank(uslAlarm)) {
                                specActivationAlarmDto.setF_USL_ALARM(Integer.valueOf(uslAlarm.trim()));
                            }
                            break;
                        case TAR:
                            String tar = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(tar)) {
                                specInf.setF_TAR(Double.valueOf(tar.trim()));
                            }
                            break;
                        case LSL:
                            String lsl = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(lsl)) {
                                specInf.setF_LSL(Double.valueOf(lsl.trim()));
                            }
                            String lslAlarm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (StringUtils.isNotBlank(lslAlarm)) {
                                specActivationAlarmDto.setF_LSL_ALARM(Integer.valueOf(lslAlarm.trim()));
                            }
                            break;
                        case URL:
                            String url = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(url)) {
                                specInf.setF_URL(Double.valueOf(url.trim()));
                            }
                            String urlAlarm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (StringUtils.isNotBlank(urlAlarm)) {
                                specActivationAlarmDto.setF_URL_ALARM(Integer.valueOf(urlAlarm));
                            }
                            break;
                        case LRL:
                            String lrl = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(lrl)) {
                                specInf.setF_LRL(Double.valueOf(lrl.trim()));
                            }
                            String lrlAlarm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (StringUtils.isNotBlank(lrlAlarm)) {
                                specActivationAlarmDto.setF_LRL_ALARM(Integer.valueOf(lrlAlarm.trim()));
                            }
                            break;
                        case UWL:
                            String uwl = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(uwl)) {
                                specInf.setF_UWL(Double.valueOf(uwl.trim()));
                            }
                            String uwlAlarm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (StringUtils.isNotBlank(uwlAlarm)) {
                                specActivationAlarmDto.setF_UWL_ALARM(Integer.valueOf(uwlAlarm.trim()));
                            }
                            break;
                        case LWL:
                            String lwl = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(lwl)) {
                                specInf.setF_LWL(Double.valueOf(lwl.trim()));
                            }
                            String lwlAlarm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (StringUtils.isNotBlank(lwlAlarm)) {
                                specActivationAlarmDto.setF_LWL_ALARM(Integer.valueOf(lwlAlarm.trim()));
                            }
                            break;
                        case UWP:
                            String uwp = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(uwp)) {
                                specInf.setF_UWP(Double.valueOf(uwp.trim()));
                            }
                            String uwpAlarm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (StringUtils.isNotBlank(uwpAlarm)) {
                                specActivationAlarmDto.setF_UWP_ALARM(Integer.valueOf(uwpAlarm.trim()));
                            }
                            break;
                        case LWP:
                            String lwp = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(lwp)) {
                                specInf.setF_LWP(Double.valueOf(lwp.trim()));
                            }
                            String lwpAlarm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (StringUtils.isNotBlank(lwpAlarm)) {
                                specActivationAlarmDto.setF_LWP_ALARM(Integer.valueOf(lwpAlarm.trim()));
                            }
                            break;
                        case UAL:
                            String ual = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(ual)) {
                                specInf.setF_UAL(Double.valueOf(ual.trim()));
                            }
                            String ualAlarm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (StringUtils.isNotBlank(ualAlarm)) {
                                specActivationAlarmDto.setF_UAL_ALARM(Integer.valueOf(ualAlarm.trim()));
                            }
                            break;
                        case LAL:
                            String lal = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(lal)) {
                                specInf.setF_LAL(Double.valueOf(lal.trim()));
                            }
                            String lalAlarm = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getGroupType(),
                                    dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getAppointColumn(),
                                    dataColumnMappingDTO.getGroupName(), dataLine);
                            if (StringUtils.isNotBlank(lalAlarm)) {
                                specActivationAlarmDto.setF_LAL_ALARM(Integer.valueOf(lalAlarm.trim()));
                            }
                            break;
                        case CP:
                            String cp = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(cp)) {
                                specInf.setF_CP(Double.valueOf(cp.trim()));
                            }
                            break;
                        case CPK:
                            String cpk = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(cpk)) {
                                specInf.setF_CPK(Double.valueOf(cpk.trim()));
                            }
                            break;
                        case PP:
                            String pp = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(pp)) {
                                specInf.setF_PP(Double.valueOf(pp.trim()));
                            }
                            break;
                        case PPK:
                            String ppk = ParseDataFileUtil.analyzeField(dataColumnMappingDTO.getType(),
                                    dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumn(),
                                    dataColumnMappingDTO.getColumnName(), dataLine);
                            if (StringUtils.isNotBlank(ppk)) {
                                specInf.setF_PPK(Double.valueOf(ppk.trim()));
                            }
                            break;
                        default:
                    }
                }
                specInf.setSpecActivationAlarmDto(specActivationAlarmDto);
                addSpec(specSkip, specInf, total);
            } catch (NumberFormatException e) {
                log.error("上传文档 数据解析 添加公差限 第{}行数据解析失败 类型转换异常 ex:{}", total, e.getMessage(), e);
            } catch (Exception e) {
                log.error("上传文档 数据解析 添加公差限 第{}行数据解析失败 ex:{}", total, e.getMessage(), e);
            }
        }
    }

    private void addSpec(Integer specSkip, SPEC_INF_VO specInf, int total) {
        if (specSkip == null) {
            specSkip = 0;
        }
        specInf.setF_CRUE(InitConfig.empl);
        specInf.setF_EDUE(InitConfig.empl);
        /*判断公差限是否已经存在*/
        LambdaQueryWrapper<SPEC_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SPEC_INF::getF_PART, specInf.getF_PART())
                .eq(SPEC_INF::getF_PTRV, specInf.getF_PTRV())
                .eq(SPEC_INF::getF_TEST, specInf.getF_TEST())
                .eq(SPEC_INF::getF_DEL, DelFlagEnum.USE.getType());
        if (StringUtils.isNotEmpty(specInf.getF_PRCS()))
            queryWrapper.eq(SPEC_INF::getF_PRCS, specInf.getF_PRCS());
        List<SPEC_INF> specInfList = specInfMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(specInfList)) {
            /*判断是修改还是跳过*/
            if (specSkip == 1) {
                specInf.setF_SPEC(specInfList.get(0).getF_SPEC());
                R<?> edit = remoteSpecService.edit(specInf);
                if (edit.getCode() != Constants.SUCCESS) {
                    log.info("第{}行修改公差限失败,请检查!", total);
                }
            } else {
                log.info("第{}行公差限已存在跳过", total);
            }
        } else {
            /*新增*/
            R<?> add = remoteSpecService.add(specInf);
            if (add.getCode() != Constants.SUCCESS) {
                log.info("第{}行新增公差限失败,请检查!", total);
            } else {
                log.info("第{}行新增公差限成功", total);
            }
        }
    }

    @Override
    public void autoSpecCollectDb(DataCollectionVO dataCollectionVO, SCHEDULE_JOB_INF scheduleJob) {
        String handleSql = dataCollectionVO.getHandleSql();

        /*启动条件缓存*/
        String startValKey = startVal + scheduleJob.getF_SJOB();
        if (dataCollectionVO.getStartType() == 1) {
            /*改成从缓存取*/
            String startSql = cacheMap.get(startValKey);
            /*处理sql占位符  动态匹配*/
            String placeholder = cacheMap.get(startKey + scheduleJob.getF_SJOB());
            if (startSql == null) {
                /*判断redis里是否有值*/
                String cache = redisService.getCacheObject(startValKey);
                if (StringUtils.isNotEmpty(cache)) {
                    String[] split = cache.split("@-@");
                    startSql = split[1];
                    placeholder = split[0];
                    if (cacheMap.get(startValKey) == null) {
                        cacheMap.put(startValKey, startSql);
                        cacheMap.put(startKey + scheduleJob.getF_SJOB(), split[0]);
                    }
                } else {
                    /*启动sql数据*/
                    List<Map<String, Object>> mapList = dbConfigInfService.testSql(scheduleJob.getF_BUID(), dataCollectionVO.getStartSql());
                    /*只取一个*/
                    String key = mapList.get(0).keySet().toArray()[0].toString();
                    cacheMap.put(startValKey, mapList.get(0).get(key).toString());
                    redisService.setCacheObject(startValKey, key + "@-@" + mapList.get(0).get(key).toString());
                    cacheMap.put(startKey + scheduleJob.getF_SJOB(), key);
                    startSql = cacheMap.get(startValKey);
                    placeholder = key;
                }
            }
            /*处理sql数据*/
            handleSql = handleSql.replace("{" + placeholder + "}", startSql).replace("&gt;", ">").replace("&lt;", "<");
        }
        if (handleSql.contains("{")) {
            log.info("sql解析失败");
            return;
        }

        /*获取数据*/
        List<Map<String, Object>> mapList = dbConfigInfService.testSql(scheduleJob.getF_BUID(), handleSql);
        DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO = dataCollectionVO.getDataFileAutoCollectConfigDTO();
        List<DataColumnMappingDTO> dataColumnMappingDTOList = dataFileAutoCollectConfigDTO.getDataColumnMappingDTOList();

        /*产品缓存*/
        ConcurrentHashMap<String, PART_INF_DTO> partMap = new ConcurrentHashMap<>();
        /*过程缓存*/
        ConcurrentHashMap<String, PRCS_INF> prcsMap = new ConcurrentHashMap<>();
        /*测试缓存*/
        ConcurrentHashMap<String, TEST_INF> testMap = new ConcurrentHashMap<>();
        int total = 0;
        outerLoop:
        for (Map<String, Object> map : mapList) {
            try {
                total++;
                SPEC_INF_VO specInfVo = new SPEC_INF_VO();
                SPEC_ACTIVATION_ALARM_DTO specActivationAlarmDto = new SPEC_ACTIVATION_ALARM_DTO();
                for (DataColumnMappingDTO dataColumnMappingDTO : dataColumnMappingDTOList) {
                    switch (DbSpecFiledEnum.getType(dataColumnMappingDTO.getFiled())) {
                        case PART_DAT:
                            String partName = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);

                            if (ParseDataFileUtil.judgeName(total, partName, DbSpecFiledEnum.PART_DAT.getDesc())) {
                                log.info("产品名称为空 执行下一条");
                                continue outerLoop;
                            }
                            String ptrvName = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (ParseDataFileUtil.judgeName(total, ptrvName, DbSpecFiledEnum.PREV_DAT.getDesc())) {
                                SysyemGlobalConfig systemConfig = null;
                                try {
                                    systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
                                } catch (Exception e) {
                                    log.error("获取系统配置信息失败 ex:{}", e.getMessage(), e);
                                    throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
                                }
                                ptrvName = systemConfig.getBasicConfig().getPartRevName();
                            }
                            String partKey = partName + dataCollectionVO.getF_PLNT();
                            String finalPtrvName = ptrvName;
                            PART_INF_DTO partInfDto = partMap.computeIfAbsent(partKey, k -> {
                                PART_INF newPart = partInfService.findByName(partName, dataCollectionVO.getF_PLNT());
                                PART_INF_DTO newPartDto = new PART_INF_DTO();
                                if (newPart == null) {
                                    newPart = new PART_INF();
                                    newPart.setF_NAME(partName);
                                    newPart.setF_PLNT(dataCollectionVO.getF_PLNT());
                                    newPart.setF_EDUE(InitConfig.empl);
                                    newPart.setF_CRUE(InitConfig.empl);
                                    partInfService.save(newPart);

                                    PART_REV partRev = new PART_REV();
                                    partRev.setF_PART(newPart.getF_PART());
                                    partRev.setF_STTM(DateUtils.toUtc(DateUtils.getNowDate()));
                                    partRev.setF_FNTM(DateUtils.parseDate("2099-01-01"));
                                    partRev.setF_CRUE(InitConfig.empl);
                                    partRev.setF_EDUE(InitConfig.empl);
                                    partRev.setF_NAME(finalPtrvName);
                                    partRevService.save(partRev);
                                    newPartDto = BeanUtil.copyProperties(newPart, PART_INF_DTO.class);
                                    newPartDto.setPartRevId(partRev.getF_PTRV());
                                } else {
                                    /*未指定版本则拿产品最新的版本*/
                                    LambdaQueryWrapper<PART_REV> revQueryWrapper = new LambdaQueryWrapper<>();
                                    revQueryWrapper.eq(PART_REV::getF_PART, newPart.getF_PART())
                                            .eq(PART_REV::getF_DEL, DelFlagEnum.USE.getType())
                                            .eq(PART_REV::getF_NAME, finalPtrvName)
                                            .le(PART_REV::getF_STTM, DateUtils.toUtc(DateUtils.getNowDate()))
                                            .orderByDesc(PART_REV::getF_STTM);
                                    List<PART_REV> revList = partRevService.list(revQueryWrapper);
                                    newPartDto = BeanUtil.copyProperties(newPart, PART_INF_DTO.class);
                                    if (CollectionUtils.isNotEmpty(revList)) {
                                        newPartDto.setPartRevId(revList.get(0).getF_PTRV());
                                    } else {
                                        //没有最新版本使用 使用系统默认版本号 创建新版本号
                                        PART_REV partRev = new PART_REV();
                                        partRev.setF_PART(newPart.getF_PART());
                                        partRev.setF_STTM(DateUtils.toUtc(DateUtils.getNowDate()));
                                        partRev.setF_FNTM(DateUtils.parseDate("2099-01-01"));
                                        partRev.setF_CRUE(InitConfig.empl);
                                        partRev.setF_EDUE(InitConfig.empl);
                                        SysyemGlobalConfig systemConfig = null;
                                        try {
                                            systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
                                        } catch (Exception e) {
                                            log.error("获取系统配置信息失败 ex:{}", e.getMessage(), e);
                                            throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
                                        }
                                        final String partRevName = systemConfig.getBasicConfig().getPartRevName();
                                        partRev.setF_NAME(partRevName);
                                        partRev.setF_PTRV(JudgeUtils.defaultIdentifierGenerator.nextId(null));
                                        partRevService.save(partRev);
                                        newPartDto.setPartRevId(partRev.getF_PTRV());
                                    }
                                }
                                return newPartDto;
                            });

                            specInfVo.setF_PART(partInfDto.getF_PART()).setF_PTRV(partInfDto.getPartRevId());
                            break;
                        case TEST_DAT:
                            String testName = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);

                            if (ParseDataFileUtil.judgeName(total, testName, DbFiledEnum.TEST_DAT.getDesc())) {
                                log.info("测试名称为空 执行下一条");
                                continue outerLoop;
                            }
                            String testKey = testName + dataCollectionVO.getF_PLNT();
                            TEST_INF testInf = testMap.computeIfAbsent(testKey, k -> {
                                /*先查询*/
                                LambdaQueryWrapper<TEST_INF> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(TEST_INF::getF_NAME, testName)
                                        .eq(TEST_INF::getF_PLNT, dataCollectionVO.getF_PLNT())
                                        .eq(TEST_INF::getF_DEL, DelFlagEnum.USE.getType());
                                List<TEST_INF> list = testInfService.list(queryWrapper);
                                TEST_INF newTestInf = new TEST_INF();
                                if (CollectionUtils.isEmpty(list)) {
                                    newTestInf.setF_NAME(testName);
                                    newTestInf.setF_EDUE(InitConfig.empl);
                                    newTestInf.setF_CRUE(InitConfig.empl);
                                    newTestInf.setF_PLNT(dataCollectionVO.getF_PLNT());
                                    testInfService.save(newTestInf);
                                } else {
                                    newTestInf = list.get(0);
                                }
                                return newTestInf;
                            });
                            specInfVo.setF_TEST(testInf.getF_TEST());
                            break;
                        case PRCS_DAT:
                            String prcsName = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);

                            if (ParseDataFileUtil.judgeName(total, prcsName, DbFiledEnum.PRCS_DAT.getDesc())) continue;
                            String prcsKey = prcsName + dataCollectionVO.getF_PLNT();
                            PRCS_INF prcsInf = prcsMap.computeIfAbsent(prcsKey, k -> {
                                /*先查询*/
                                LambdaQueryWrapper<PRCS_INF> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(PRCS_INF::getF_NAME, prcsName)
                                        .eq(PRCS_INF::getF_PLNT, dataCollectionVO.getF_PLNT())
                                        .eq(PRCS_INF::getF_DEL, DelFlagEnum.USE.getType());
                                List<PRCS_INF> list = prcsInfService.list(queryWrapper);
                                PRCS_INF newPrcs = new PRCS_INF();
                                if (CollectionUtils.isEmpty(list)) {
                                    newPrcs.setF_NAME(prcsName);
                                    newPrcs.setF_PLNT(dataCollectionVO.getF_PLNT());
                                    newPrcs.setF_EDUE(InitConfig.empl);
                                    newPrcs.setF_CRUE(InitConfig.empl);
                                    prcsInfService.save(newPrcs);
                                } else {
                                    newPrcs = list.get(0);
                                }
                                return newPrcs;
                            });
                            specInfVo.setF_PRCS(prcsInf.getF_PRCS());
                            break;
                        case USL:
                            String usl = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);

                            if (StringUtils.isNotBlank(usl)) {
                                specInfVo.setF_USL(Double.valueOf(usl.trim()));
                            }
                            String uslAlarm = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (StringUtils.isNotBlank(uslAlarm)) {
                                specActivationAlarmDto.setF_USL_ALARM(Integer.valueOf(uslAlarm.trim()));
                            }
                            break;
                        case TAR:
                            String tar = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(tar)) {
                                specInfVo.setF_TAR(Double.valueOf(tar.trim()));
                            }
                            break;
                        case LSL:
                            String lsl = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(lsl)) {
                                specInfVo.setF_LSL(Double.valueOf(lsl.trim()));
                            }
                            String lslAlarm = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (StringUtils.isNotBlank(lslAlarm)) {
                                specActivationAlarmDto.setF_LSL_ALARM(Integer.valueOf(lslAlarm.trim()));
                            }
                            break;
                        case URL:
                            String url = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(url)) {
                                specInfVo.setF_URL(Double.valueOf(url.trim()));
                            }
                            String urlAlarm = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (StringUtils.isNotBlank(urlAlarm)) {
                                specActivationAlarmDto.setF_URL_ALARM(Integer.valueOf(urlAlarm.trim()));
                            }
                            break;
                        case LRL:
                            String lrl = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(lrl)) {
                                specInfVo.setF_LRL(Double.valueOf(lrl.trim()));
                            }
                            String lrlAlarm = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (StringUtils.isNotBlank(lrlAlarm)) {
                                specActivationAlarmDto.setF_LRL_ALARM(Integer.valueOf(lrlAlarm.trim()));
                            }
                            break;
                        case UWL:
                            String uwl = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(uwl)) {
                                specInfVo.setF_UWL(Double.valueOf(uwl.trim()));
                            }
                            String uwlAlarm = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (StringUtils.isNotBlank(uwlAlarm)) {
                                specActivationAlarmDto.setF_UWL_ALARM(Integer.valueOf(uwlAlarm.trim()));
                            }
                            break;
                        case LWL:
                            String lwl = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(lwl)) {
                                specInfVo.setF_LWL(Double.valueOf(lwl.trim()));
                            }
                            String lwlAlarm = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (StringUtils.isNotBlank(lwlAlarm)) {
                                specActivationAlarmDto.setF_LWL_ALARM(Integer.valueOf(lwlAlarm.trim()));
                            }
                            break;
                        case UWP:
                            String uwp = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(uwp)) {
                                specInfVo.setF_UWP(Double.valueOf(uwp.trim()));
                            }
                            String uwpAlarm = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (StringUtils.isNotBlank(uwpAlarm)) {
                                specActivationAlarmDto.setF_UWP_ALARM(Integer.valueOf(uwpAlarm.trim()));
                            }
                            break;
                        case LWP:
                            String lwp = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(lwp)) {
                                specInfVo.setF_LWP(Double.valueOf(lwp.trim()));
                            }
                            String lwpAlarm = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (StringUtils.isNotBlank(lwpAlarm)) {
                                specActivationAlarmDto.setF_LWP_ALARM(Integer.valueOf(lwpAlarm.trim()));
                            }
                            break;
                        case UAL:
                            String ual = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(ual)) {
                                specInfVo.setF_UAL(Double.valueOf(ual.trim()));
                            }
                            String ualAlarm = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (StringUtils.isNotBlank(ualAlarm)) {
                                specActivationAlarmDto.setF_UAL_ALARM(Integer.valueOf(ualAlarm.trim()));
                            }
                            break;
                        case LAL:
                            String lal = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(lal)) {
                                specInfVo.setF_LAL(Double.valueOf(lal.trim()));
                            }
                            String lalAlarm = DbCollectionUtil.getItemName(dataColumnMappingDTO.getGroupType(), dataColumnMappingDTO.getAnalyticRuleGrpList(), dataColumnMappingDTO.getGroupName(), map);
                            if (StringUtils.isNotBlank(lalAlarm)) {
                                specActivationAlarmDto.setF_LAL_ALARM(Integer.valueOf(lalAlarm.trim()));
                            }
                            break;
                        case CP:
                            String cp = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(cp)) {
                                specInfVo.setF_CP(Double.valueOf(cp.trim()));
                            }
                            break;
                        case CPK:
                            String cpk = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(cpk)) {
                                specInfVo.setF_CPK(Double.valueOf(cpk.trim()));
                            }
                            break;
                        case PP:
                            String pp = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(pp)) {
                                specInfVo.setF_PP(Double.valueOf(pp.trim()));
                            }
                            break;
                        case PPK:
                            String ppk = DbCollectionUtil.getItemName(dataColumnMappingDTO.getType(), dataColumnMappingDTO.getAnalyticRuleList(), dataColumnMappingDTO.getColumnName(), map);
                            if (StringUtils.isNotBlank(ppk)) {
                                specInfVo.setF_PPK(Double.valueOf(ppk.trim()));
                            }
                            break;
                        default:
                    }
                }
                specInfVo.setSpecActivationAlarmDto(specActivationAlarmDto);
                addSpec(dataCollectionVO.getSpecSkip(), specInfVo, total);
                /*更新启动sql的值*/
                if (map.get(cacheMap.get(startKey + scheduleJob.getF_SJOB())) != null) {
                    cacheMap.put(startValKey, map.get(cacheMap.get(startKey + scheduleJob.getF_SJOB())).toString());
                    redisService.setCacheObject(startValKey, cacheMap.get(startKey + scheduleJob.getF_SJOB()) + "@-@" + map.get(cacheMap.get(startKey + scheduleJob.getF_SJOB())).toString());
                }
            } catch (NumberFormatException e) {
                log.error("数据库数据解析 添加公差限 第{}行数据解析失败 类型转换异常 ex:{}", total, e.getMessage(), e);
            } catch (Exception e) {
                log.error("数据库数据解析 添加公差限 第{}行数据解析失败 ex:{}", total, e.getMessage(), e);
            }
        }
    }
}
