package com.yingfei.dataManagement.service;

import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.LAYOUT_INF;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.LAYOUT_INF_DTO;
import com.yingfei.entity.vo.LAYOUT_INF_VO;

/**
 * @description 针对表【LAYOUT_INF(用户菜单列配置)】的数据库操作Service
 * @createDate 2024-11-20 14:43:35
 */
public interface LAYOUT_INFService extends IService<LAYOUT_INF>, BaseService<LAYOUT_INF_VO, LAYOUT_INF_DTO> {

    LAYOUT_INF_DTO getColumnConfig(LAYOUT_INF_VO layoutInfVo);
}
