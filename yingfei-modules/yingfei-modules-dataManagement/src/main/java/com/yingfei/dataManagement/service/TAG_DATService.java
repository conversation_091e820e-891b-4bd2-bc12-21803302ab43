package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.TAG_DAT;
import com.yingfei.entity.dto.TAG_DAT_DTO;
import com.yingfei.entity.vo.TAG_DAT_VO;

/**
 * <AUTHOR>
 * @description 针对表【TAG_DAT(储存标签信息表)】的数据库操作Service
 * @createDate 2024-05-08 16:28:51
 */
public interface TAG_DATService extends IService<TAG_DAT>, BaseService<TAG_DAT_VO, TAG_DAT_DTO> {

}
