package com.yingfei.dataManagement.controller.basic;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.PROCESSING_TEMPLATE_INFService;
import com.yingfei.entity.domain.PROCESSING_TEMPLATE_INF;
import com.yingfei.entity.dto.PROCESSING_TEMPLATE_INF_DTO;
import com.yingfei.entity.vo.PROCESSING_TEMPLATE_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "数据处理模板信息API")
@RestController
@RequestMapping("/processing_template_inf")
public class PROCESSING_TEMPLATE_INFController extends BaseController {
    
    @Resource
    private PROCESSING_TEMPLATE_INFService processingTemplateInfService;

    /**
     * 获取数据处理模板信息列表
     */
    @ApiOperation("获取数据处理模板信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody PROCESSING_TEMPLATE_INF_VO processingTemplateInfVo) {
        List<PROCESSING_TEMPLATE_INF_DTO> list = processingTemplateInfService.getList(processingTemplateInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(processingTemplateInfService.getTotal(processingTemplateInfVo));
        return dataTable;
    }

    /**
     * 新增数据处理模板信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:processingTemplateInf:add")
    @Log(title = "数据处理模板管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增数据处理模板信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody PROCESSING_TEMPLATE_INF_VO processingTemplateInfVo) {
        processingTemplateInfService.checkParam(processingTemplateInfVo);
        processingTemplateInfService.add(processingTemplateInfVo);
        return R.ok();
    }

    /**
     * 修改数据处理模板信息
     */
    @RequiresPermissions("dataManagement:processingTemplateInf:edit")
    @Log(title = "数据处理模板管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改数据处理模板信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody PROCESSING_TEMPLATE_INF_VO processingTemplateInfVo) {
        processingTemplateInfService.checkParam(processingTemplateInfVo);
        PROCESSING_TEMPLATE_INF processingTemplateInf = new PROCESSING_TEMPLATE_INF();
        BeanUtils.copyPropertiesIgnoreNull(processingTemplateInfVo, processingTemplateInf);
        processingTemplateInfService.updateById(processingTemplateInf);
        return R.ok();
    }

    /**
     * 批量删除数据处理模板信息
     */
    @RequiresPermissions("dataManagement:processingTemplateInf:remove")
    @Log(title = "数据处理模板管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除数据处理模板信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        processingTemplateInfService.del(ids);
        return R.ok();
    }
}
