package com.yingfei.dataManagement.controller.bpm;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.dataManagement.service.bpm.BpmTaskAssignRuleService;
import com.yingfei.entity.enums.BpmTaskAssignRuleTypeEnum;
import com.yingfei.entity.vo.BPM_TASK_RULE_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@Api(tags = "工作流程 - 任务分配规则")
@RestController
@RequestMapping("/bpm/task-assign-rule")
@Validated
public class BpmTaskAssignRuleController {

    @Resource
    private BpmTaskAssignRuleService taskAssignRuleService;

    @GetMapping("/list")
    @ApiOperation("获得任务分配规则列表")
    @Parameters({
            @Parameter(name = "modelId", description = "模型编号", example = "1024"),
            @Parameter(name = "processDefinitionId", description = "流程定义的编号", example = "2048")
    })
    public R<?> getTaskAssignRuleList(
            @RequestParam(value = "modelId", required = false) Long modelId,
            @RequestParam(value = "processDefinitionId", required = false) String processDefinitionId) {
        return R.ok(taskAssignRuleService.getTaskAssignRuleList(modelId, processDefinitionId));
    }

    @CreateUpdateBy
    @NotResubmit
    @PostMapping("/create")
    @ApiOperation("创建任务分配规则")
    public R<?> createTaskAssignRule(@RequestBody BPM_TASK_RULE_VO reqVO) {
        taskAssignRuleService.createTaskAssignRule(reqVO);
        return R.ok();
    }

    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @PutMapping("/update")
    @ApiOperation("更新任务分配规则")
    public R<?> updateTaskAssignRule(@RequestBody BPM_TASK_RULE_VO reqVO) {
        taskAssignRuleService.updateTaskAssignRule(reqVO);
        return R.ok(true);
    }

    @GetMapping("/formList")
    @ApiOperation("获得任务分配表单列表")
    @Parameters({
            @Parameter(name = "modelId", description = "模型编号", example = "1024"),
            @Parameter(name = "processDefinitionId", description = "流程定义的编号", example = "2048")
    })
    public R<?> getTaskAssignFormList(
            @RequestParam(value = "modelId", required = false) Long modelId,
            @RequestParam(value = "processDefinitionId", required = false) String processDefinitionId) {
        return R.ok(taskAssignRuleService.getTaskAssignFormList(modelId, processDefinitionId));
    }

    @CreateUpdateBy
    @NotResubmit
    @PostMapping("/createForm")
    @ApiOperation("创建任务分配表单")
    public R<?> createTaskAssignForm(@RequestBody BPM_TASK_RULE_VO reqVO) {
        taskAssignRuleService.createTaskAssignForm(reqVO);
        return R.ok();
    }

    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @PutMapping("/updateForm")
    @ApiOperation("更新任务分配规则")
    public R<?> updateTaskAssignForm(@RequestBody BPM_TASK_RULE_VO reqVO) {
        taskAssignRuleService.updateTaskAssignForm(reqVO);
        return R.ok(true);
    }

    @GetMapping("/getRuleEnum")
    @ApiOperation("获得规则类型枚举")
    public R<?> getRuleEnum() {
        return R.ok(BpmTaskAssignRuleTypeEnum.getMap());
    }
}
