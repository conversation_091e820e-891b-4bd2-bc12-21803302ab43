package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.TAG_GRP;
import com.yingfei.entity.dto.TAG_GRP_DTO;
import com.yingfei.entity.vo.TAG_GRP_VO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【TAG_GRP(储存标签组信息表)】的数据库操作Service
 * @createDate 2024-05-08 16:28:55
 */
public interface TAG_GRPService extends IService<TAG_GRP>, BaseService<TAG_GRP_VO, TAG_GRP_DTO> {

    public TAG_GRP_DTO getInfo(Long id, List<Long> childIds);

    List<TAG_GRP_DTO> tagGrpTreeList(TAG_GRP_VO tagGrpVo);
}
