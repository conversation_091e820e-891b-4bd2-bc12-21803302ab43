package com.yingfei.dataManagement.service.manufacturing.impl;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.mapper.MANUFACTURING_NODE_INFMapper;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.impl.PARAMETER_SET_INFServiceImpl;
import com.yingfei.dataManagement.service.manufacturing.INSPECTION_PLAN_INFService;
import com.yingfei.dataManagement.service.manufacturing.MANUFACTURING_NODE_INFService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.DESC_DAT_DTO;
import com.yingfei.entity.dto.MANUFACTURING_NODE_INF_DTO;
import com.yingfei.entity.dto.PARAMETER_CHILD_DTO;
import com.yingfei.entity.enums.PARAMETER_CHILDTypeEnum;
import com.yingfei.entity.vo.MANUFACTURING_NODE_INF_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 针对表【MANUFACTURING_NODE_INF(流程节点表)】的数据库操作Service实现
 * @createDate 2024-06-13 15:01:14
 */
@Service
public class MANUFACTURING_NODE_INFServiceImpl extends ServiceImpl<MANUFACTURING_NODE_INFMapper, MANUFACTURING_NODE_INF>
        implements MANUFACTURING_NODE_INFService {

    @Resource
    private INSPECTION_PLAN_INFService inspectionPlanInfService;
    @Resource
    private TAG_LINKService tagLinkService;
    @Resource
    private PART_INFService partInfService;
    @Resource
    private PRCS_INFService prcsInfService;
    @Resource
    private TEST_INFService testInfService;
    @Resource
    private SHIFT_DATService shiftDatService;
    @Resource
    private JOB_DATService jobDatService;
    @Resource
    private DESC_DATService descDatService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo) {
        MANUFACTURING_NODE_INF manufacturingNodeInf = new MANUFACTURING_NODE_INF();
        BeanUtils.copyPropertiesIgnoreNull(manufacturingNodeInfVo, manufacturingNodeInf);
        if (CollectionUtils.isNotEmpty(manufacturingNodeInfVo.getParameterChildDtoList())) {
            JSONArray jsonArray = new JSONArray(manufacturingNodeInfVo.getParameterChildDtoList());
            manufacturingNodeInf.setF_DATA(jsonArray.toJSONString());
        }
        baseMapper.insert(manufacturingNodeInf);
        return manufacturingNodeInf.getF_MFND();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        /*判断是否有检查计划*/
        LambdaQueryWrapper<INSPECTION_PLAN_INF> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(INSPECTION_PLAN_INF::getF_MFND, ids).eq(INSPECTION_PLAN_INF::getF_DEL, DelFlagEnum.USE.getType());
        List<INSPECTION_PLAN_INF> list = inspectionPlanInfService.list(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            throw new BusinessException(DataManagementExceptionEnum.DELETE_THE_SUBPLAN_FIRST_EXCEPTION);
        }

        LambdaQueryWrapper<MANUFACTURING_NODE_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MANUFACTURING_NODE_INF::getF_MFND, ids).eq(MANUFACTURING_NODE_INF::getF_DEL, DelFlagEnum.USE.getType());
        /*根据id列表修改对应的删除状态*/
        baseMapper.delete(queryWrapper);
    }

    @Override
    public MANUFACTURING_NODE_INF_DTO getInfo(Long id) {
        MANUFACTURING_NODE_INF manufacturingNodeInf = baseMapper.selectById(id);
        MANUFACTURING_NODE_INF_DTO manufacturingNodeInfDto = new MANUFACTURING_NODE_INF_DTO();
        if (Objects.isNull(manufacturingNodeInf)) return manufacturingNodeInfDto;
        BeanUtils.copyPropertiesIgnoreNull(manufacturingNodeInf, manufacturingNodeInfDto);
        if (StringUtils.isNotEmpty(manufacturingNodeInf.getF_DATA())) {
            List<PARAMETER_CHILD_DTO> parameterChildDtos =
                    JSONArray.parseArray(manufacturingNodeInf.getF_DATA(), PARAMETER_CHILD_DTO.class);
            manufacturingNodeInfDto.setParameterChildDtoList(parameterChildDtos);
        }
        return manufacturingNodeInfDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCount(Long fMfnd) {
        baseMapper.addCount(fMfnd);
    }

    @Override
    public void edit(MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo) {
        MANUFACTURING_NODE_INF manufacturingNodeInf = new MANUFACTURING_NODE_INF();
        BeanUtils.copyPropertiesIgnoreNull(manufacturingNodeInfVo, manufacturingNodeInf);
        if (CollectionUtils.isNotEmpty(manufacturingNodeInfVo.getParameterChildDtoList())) {
            JSONArray jsonArray = new JSONArray(manufacturingNodeInfVo.getParameterChildDtoList());
            manufacturingNodeInf.setF_DATA(jsonArray.toJSONString());
        }
        baseMapper.updateById(manufacturingNodeInf);
    }

    @Override
    public Map<String, Object> getSearchCondition(List<PARAMETER_CHILD_DTO> parameterChildDtos) {
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtils.isEmpty(parameterChildDtos)) return map;
        for (PARAMETER_CHILD_DTO parameterChildDto : parameterChildDtos) {
            switch (PARAMETER_CHILDTypeEnum.getType(parameterChildDto.getType())) {
                case PART_DAT:
                    extracted(parameterChildDto);
                    /*判断是否包含*/
                    List<PART_INF> partInfList = partInfService.getSearchCondition(parameterChildDto.getIsInclude(), parameterChildDto.getDataList().stream().map(Long::valueOf).collect(Collectors.toList()), null);
                    map.put(Constants.partList, partInfList);
                    break;
                case PRCS_DAT:
                    extracted(parameterChildDto);
                    List<PRCS_INF> prcsInfList = prcsInfService.getSearchCondition(parameterChildDto.getIsInclude(), parameterChildDto.getDataList().stream().map(Long::valueOf).collect(Collectors.toList()), null);
                    map.put(Constants.prcsList, prcsInfList);
                    break;
                case TEST_DAT:
                    extracted(parameterChildDto);
                    List<TEST_INF> testInfList = testInfService.getSearchCondition(parameterChildDto.getIsInclude(), parameterChildDto.getDataList().stream().map(Long::valueOf).collect(Collectors.toList()), null);
                    map.put(Constants.testList, testInfList);
                    break;
                case SHIFT_DAT:
                    List<Long> dataList = getList(parameterChildDto);
                    List<SHIFT_DAT> shiftDatList = shiftDatService.getSearchCondition(parameterChildDto.getIsInclude(), dataList);
                    map.put(Constants.shiftList, shiftDatList);
                    break;
                case JOB_DAT:
                    List<Long> jobList = getList(parameterChildDto);
                    List<JOB_DAT> jobDatList = jobDatService.getSearchCondition(parameterChildDto.getIsInclude(), jobList);
                    map.put(Constants.jobList, jobDatList);
                    break;
                case DESC_DAT:
                    List<Long> descList = getList(parameterChildDto);
                    List<DESC_DAT_DTO> descDatList = descDatService.getSearchCondition(parameterChildDto.getIsInclude(), descList);
                    map.put(Constants.descList, descDatList);
                    break;
            }
        }
        return map;
    }

    @Override
    public Map<String, Object> judgeConditionIsDelete(List<PARAMETER_CHILD_DTO> parameterChildDtos) {
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtils.isEmpty(parameterChildDtos)) return map;
        for (PARAMETER_CHILD_DTO parameterChildDto : parameterChildDtos) {
            switch (PARAMETER_CHILDTypeEnum.getType(parameterChildDto.getType())) {
                case PART_DAT:
                    if (parameterChildDto.getIsDynamic() == 1) continue;
                    List<PART_INF> partInfList = partInfService.getSearchCondition(parameterChildDto.getIsInclude(), parameterChildDto.getDataList().stream().map(Long::valueOf).collect(Collectors.toList()), null);
                    if (partInfList.size() != parameterChildDto.getDataList().size())
                        map.put(Constants.partList, true);
                    break;
                case PRCS_DAT:
                    if (parameterChildDto.getIsDynamic() == 1) continue;
                    List<PRCS_INF> prcsInfList = prcsInfService.getSearchCondition(parameterChildDto.getIsInclude(), parameterChildDto.getDataList().stream().map(Long::valueOf).collect(Collectors.toList()), null);
                    if (prcsInfList.size() != parameterChildDto.getDataList().size())
                        map.put(Constants.prcsList, true);
                    break;
                case TEST_DAT:
                    if (parameterChildDto.getIsDynamic() == 1) continue;
                    List<TEST_INF> testInfList = testInfService.getSearchCondition(parameterChildDto.getIsInclude(), parameterChildDto.getDataList().stream().map(Long::valueOf).collect(Collectors.toList()), null);
                    if (testInfList.size() != parameterChildDto.getDataList().size())
                        map.put(Constants.testList, true);
                    break;
                case SHIFT_DAT:
                    List<Long> dataList = getList(parameterChildDto);
                    List<SHIFT_DAT> shiftDatList = shiftDatService.getSearchCondition(parameterChildDto.getIsInclude(), dataList);
                    if (shiftDatList.size() != dataList.size())
                        map.put(Constants.shiftList, true);
                    break;
                case JOB_DAT:
                    List<Long> jobList = getList(parameterChildDto);
                    List<JOB_DAT> jobDatList = jobDatService.getSearchCondition(parameterChildDto.getIsInclude(), jobList);
                    if (jobDatList.size() != jobList.size())
                        map.put(Constants.jobList, true);
                    break;
                case DESC_DAT:
                    List<Long> descList = getList(parameterChildDto);
                    List<DESC_DAT_DTO> descDatList = descDatService.getSearchCondition(parameterChildDto.getIsInclude(), descList);
                    if (descDatList.size() != descList.size())
                        map.put(Constants.descList, true);
                    break;
            }
        }
        return map;
    }

    @Override
    public List<MANUFACTURING_NODE_INF_DTO> getList(MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo) {
        return baseMapper.getList(manufacturingNodeInfVo);
    }

    public static List<Long> getList(PARAMETER_CHILD_DTO parameterChildDto) {
        return PARAMETER_SET_INFServiceImpl.getList(parameterChildDto);
    }

    private void extracted(PARAMETER_CHILD_DTO parameterChildDto) {
        /*判断是否是动态*/
        if (parameterChildDto.getIsDynamic() == 1) {
            List<Long> dataList = getList(parameterChildDto);
            if (CollectionUtils.isEmpty(dataList)) return;
            /*判断是否包含*/
            List<TAG_LINK> tagLinkList = tagLinkService.getSearchCondition(parameterChildDto.getIsInclude(),
                    parameterChildDto.getType() + 1, dataList);
            /*获取对应资源列表*/
            List<Long> collect = tagLinkList.stream().map(TAG_LINK::getF_RESOURCE).collect(Collectors.toList());
            parameterChildDto.setIsInclude(1);
            parameterChildDto.setDataList(collect.stream().map(String::valueOf).collect(Collectors.toList()));
        }
    }
}




