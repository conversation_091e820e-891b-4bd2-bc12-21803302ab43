package com.yingfei.dataManagement.controller.bpm;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.dataManagement.service.bpm.BpmModelService;
import com.yingfei.entity.dto.BPM_MODE_DTO;
import com.yingfei.entity.vo.BPM_MODE_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;


@Api(tags = "工作流程 - 流程模型")
@RestController
@RequestMapping("/bpm/model")
@Validated
public class BpmModelController {

    @Resource
    private BpmModelService modelService;

    /**
     * 完成
     * @param bpmModeVo
     * @return
     */
    @PostMapping("/page")
    @ApiOperation("获得模型分页")
    public R<?> getModelPage(@RequestBody BPM_MODE_VO bpmModeVo) {
        return R.ok(modelService.getModelPage(bpmModeVo));
    }

    /**
     * 适配完成
     * @param id
     * @return
     */
    @GetMapping("/get")
    @ApiOperation("获得模型")
    public R<?> getModel(@RequestParam("id") Long id) {
        BPM_MODE_DTO model = modelService.getModel(id);
        return R.ok(model);
    }
    /**
     * 适配完成
     * @return
     */
    @CreateUpdateBy
    @NotResubmit
    @PostMapping("/create")
    @ApiOperation("新建模型")
    public R<?> createModel(@RequestBody BPM_MODE_VO bpmModeVo) {
        modelService.createModel(bpmModeVo, null);
        return R.ok();
    }
    /**
     * 适配完成
     * @return
     */
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @PutMapping("/update")
    @ApiOperation("修改模型")
    public R<Boolean> updateModel(@RequestBody BPM_MODE_VO bpmModeVo) {
        modelService.updateModel(bpmModeVo);
        return R.ok(true);
    }
    /**
     * 适配完成
     * @return
     */
    @PostMapping("/import")
    @ApiOperation("导入模型")
    public R<String> importModel(@RequestBody BPM_MODE_VO bpmModeVo) throws IOException {
//        BpmModelCreateReqVO createReqVO = BpmModelConvert.INSTANCE.convert(importReqVO);
//        // 读取文件
//        String bpmnXml = IoUtils.readUtf8(importReqVO.getBpmnFile().getInputStream(), false);
//        return R.ok(modelService.createModel(createReqVO, bpmnXml));
        return R.ok();
    }
    /**
     * 适配完成
     * @return
     */
    @PostMapping("/deploy")
    @ApiOperation("部署模型")
    public R<?> deployModel(@RequestParam("id") Long id) {
        modelService.deployModel(id);
        return R.ok();
    }
    /**
     * 修改模型的状态  实际更新的部署的流程定义的状态
     * @return
     */
    @PutMapping("/update-state")
    @ApiOperation("修改模型的状态")
    public R<?> updateModelState(@Valid @RequestBody BPM_MODE_VO bpmModeVo) {
        modelService.updateModelState(bpmModeVo.getF_MODE(), bpmModeVo.getState());
        return R.ok(true);
    }
    /**
     * 适配完成
     * @return
     */
    @DeleteMapping("/delete")
    @ApiOperation("删除模型")
    public R<?> deleteModel(@RequestParam("id") String id) {
        modelService.deleteModel(id);
        return R.ok();
    }
}
