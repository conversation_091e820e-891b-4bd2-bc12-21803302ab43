package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.LOT_INFService;
import com.yingfei.dataManagement.service.TEMP_TABLE_INFService;
import com.yingfei.entity.domain.LOT_INF;
import com.yingfei.entity.domain.TEMP_TABLE_INF;
import com.yingfei.entity.dto.LOT_INF_DTO;
import com.yingfei.entity.vo.LOT_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "临时表API")
@RestController
@RequestMapping("/temp_table_inf")
public class TEMP_TABLE_INFController extends BaseController {

    @Resource
    private TEMP_TABLE_INFService tempTableInfService;


    @ApiOperation("临时表记录")
    @PostMapping("/batchAdd")
    public R<?> batchAdd(@RequestBody TEMP_TABLE_INF tempTableInf) {
        tempTableInfService.batchAdd(tempTableInf.getIdList(), tempTableInf.getUuid());
        return R.ok();
    }

    /**
     * 临时表记录删除
     */
    @ApiOperation("临时表记录删除")
    @PostMapping("/delete")
    public R<?> delete(@RequestBody TEMP_TABLE_INF tempTableInf) {
        tempTableInfService.delete(tempTableInf.getUuid());
        return R.ok();
    }

}
