package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.dataManagement.service.chart.LinearRegressionService;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.LinearRegressionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "图表:线性回归API")
@RequestMapping("/linearRegression")
public class LinearRegressionController {

    @Resource
    private LinearRegressionService linearRegressionService;

    @PostMapping("/info")
    @ApiOperation("线性回归")
    public R<?> getInfo(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO){
        LinearRegressionDTO linearRegressionDTO = linearRegressionService.getInfo(subgroupDataSelectionDTO);
        return R.ok(linearRegressionDTO);
    }

    @PostMapping("/infoList")
    @ApiOperation("线性回归")
    public R<?> getInfoList(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO){
        subgroupDataSelectionDTO.setIsRange(YesOrNoEnum.YES.getType());
        List<LinearRegressionDTO> linearRegressionDTO = linearRegressionService.getInfoList(subgroupDataSelectionDTO);
        return R.ok(linearRegressionDTO);
    }
}
