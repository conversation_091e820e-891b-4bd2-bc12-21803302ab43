package com.yingfei.dataManagement.controller.basic;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.DEF_DATService;
import com.yingfei.entity.domain.DEF_DAT;
import com.yingfei.entity.dto.DEF_DAT_DTO;
import com.yingfei.entity.vo.DEF_DAT_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "缺陷代码信息API")
@RestController
@RequestMapping("/def_dat")
public class DEF_DATController extends BaseController {

    @Resource
    private DEF_DATService defDatService;

    /**
     * 获取缺陷代码信息列表
     */
    @ApiOperation("获取缺陷代码信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody DEF_DAT_VO defDatVo) {
        List<DEF_DAT_DTO> list = defDatService.getList(defDatVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(defDatService.getTotal(defDatVo));
        return dataTable;
    }

    /**
     * 新增缺陷代码信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:defDat:add")
    @Log(title = "缺陷代码管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增缺陷代码信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody DEF_DAT_VO defDatVo) {
        defDatService.checkParam(defDatVo);
        DEF_DAT defDat = defDatService.addDefDat(defDatVo);
        return R.ok(defDat);
    }

    /**
     * 修改缺陷代码信息
     */
    @RequiresPermissions("dataManagement:defDat:edit")
    @Log(title = "缺陷代码管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改缺陷代码信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody DEF_DAT_VO defDatVo) {
        defDatService.checkParam(defDatVo);
        DEF_DAT defDat = new DEF_DAT();
        BeanUtils.copyPropertiesIgnoreNull(defDatVo, defDat);
        defDatService.updateById(defDat);
        return R.ok();
    }

    /**
     * 批量删除缺陷代码信息
     */
    @RequiresPermissions("dataManagement:defDat:remove")
    @Log(title = "缺陷代码管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除缺陷代码信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        defDatService.del(ids);
        return R.ok();
    }
}
