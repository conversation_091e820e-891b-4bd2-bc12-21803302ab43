
package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.SHIFT_DATService;
import com.yingfei.entity.domain.SHIFT_DAT;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.SHIFT_DAT_DTO;
import com.yingfei.entity.dto.TEST_INF_DTO;
import com.yingfei.entity.vo.SHIFT_DAT_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "班次信息API")
@RestController
@RequestMapping("/shift_dat")
public class SHIFT_DATController extends BaseController {
    
    @Resource
    private SHIFT_DATService shiftDatService;

    /**
     * 获取班次信息列表
     */
    @ApiOperation("获取班次信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody SHIFT_DAT_VO shiftDatVo) {
        List<SHIFT_DAT_DTO> list = shiftDatService.getList(shiftDatVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(shiftDatService.getTotal(shiftDatVo));
        return dataTable;
    }

    /**
     * 新增班次信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:shiftDat:add")
    @Log(title = "班次管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增班次信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody SHIFT_DAT_VO shiftDatVo) {
        shiftDatService.checkParam(shiftDatVo);
        shiftDatService.add(shiftDatVo);
        return R.ok();
    }

    /**
     * 修改班次信息
     */
    @RequiresPermissions("dataManagement:shiftDat:edit")
    @Log(title = "班次管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改班次信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody SHIFT_DAT_VO shiftDatVo) {
        shiftDatService.checkParam(shiftDatVo);
        SHIFT_DAT shiftDat = new SHIFT_DAT();
        BeanUtils.copyPropertiesIgnoreNull(shiftDatVo, shiftDat);
        shiftDatService.updateById(shiftDat);
        return R.ok();
    }

    /**
     * 批量删除班次信息
     */
    @RequiresPermissions("dataManagement:shiftDat:remove")
    @Log(title = "班次管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除班次信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        shiftDatService.del(ids);
        return R.ok();
    }

    @ApiOperation("获取班次信息")
    @PostMapping("/getInfo/{id}")
    public R<?> getInfo(@PathVariable Long id) {
        SHIFT_DAT_DTO shiftDatDto = shiftDatService.info(id);
        return R.ok(shiftDatDto);
    }

    @ApiOperation("获取班次删除影响的关联信息")
    @PostMapping("/getOperationAssociation/{ids}")
    public R<?> getOperationAssociation(@PathVariable List<Long> ids) {
        OperationAssociationDTO operationAssociationDTO = shiftDatService.getOperationAssociation(ids);
        return R.ok(operationAssociationDTO);
    }
}
