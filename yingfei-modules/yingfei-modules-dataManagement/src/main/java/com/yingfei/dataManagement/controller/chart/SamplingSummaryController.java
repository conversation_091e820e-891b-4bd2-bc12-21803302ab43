package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.service.chart.SamplingSummaryService;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.BoxPlotsDataDTO;
import com.yingfei.entity.dto.chart.InstrumentBoardDTO;
import com.yingfei.entity.dto.chart.SamplingSummaryDataDTO;
import com.yingfei.entity.enums.BoxPlotsAnalyseTypeEnum;
import com.yingfei.entity.enums.SamplingSummaryAnalyseTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@Api(tags = "图表:抽样汇总API")
@RequestMapping("/samplingSummary")
public class SamplingSummaryController {

    @Resource
    private SamplingSummaryService samplingSummaryService;

    @PostMapping("/info")
    @ApiOperation("获取抽样汇总详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = SamplingSummaryDataDTO.class)
    })
    public R<?> getInfo(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        List<SamplingSummaryDataDTO> info = samplingSummaryService.getInfo(subgroupDataSelectionDTO);
        return R.ok(info);
    }

    /**
     * 获取箱线图分析角度类型
     */
    @PostMapping("/getAnalysisType")
    @ApiOperation("获取抽样汇总分析角度类型")
    public R<?> getAnalysisType() {
        Map<String, String> map = SamplingSummaryAnalyseTypeEnum.getMap();
        return R.ok(map);
    }
}
