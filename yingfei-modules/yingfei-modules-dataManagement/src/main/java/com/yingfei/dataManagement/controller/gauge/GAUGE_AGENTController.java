package com.yingfei.dataManagement.controller.gauge;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.dataManagement.service.gauge.GAUGE_AGENTService;
import com.yingfei.dataManagement.service.gauge.GAUGE_COMMONService;
import com.yingfei.entity.dto.GAUGE_AGENT_DTO;
import com.yingfei.entity.vo.GAUGE_AGENT_VO;
import com.yingfei.entity.vo.SerialDebuggingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "量具Agent信息API")
@RestController
@RequestMapping("/gauge_agent")
public class GAUGE_AGENTController extends BaseController {

    @Resource
    private GAUGE_AGENTService gaugeFormatService;
    @Resource
    private GAUGE_COMMONService gaugeCommonService;

    /**
     * 获取量具Agent信息列表
     */
    @ApiOperation("获取量具Agent信息列表")
    @PostMapping("/list")
    public R<?> list(@RequestBody GAUGE_AGENT_VO gaugeAgentVo) {
        List<GAUGE_AGENT_DTO> list = gaugeFormatService.getList(gaugeAgentVo);
        return R.ok(list);
    }

    /**
     * 前端清空串口读取数据缓存
     */
    @ApiOperation("前端清空串口读取数据缓存")
    @PostMapping("/clean")
    public R<?> clean(@RequestBody SerialDebuggingVO serialDebuggingVO) {
        gaugeCommonService.clean(serialDebuggingVO);
        return R.ok();
    }

    /**
     * 量具数据解析
     */
    @ApiOperation("量具数据解析")
    @PostMapping("/analysis")
    public R<?> analysis(@RequestBody SerialDebuggingVO serialDebuggingVO) {
        if (StringUtils.isEmpty(serialDebuggingVO.getSerialPort()))
            return R.fail("串口信息未填写");
        if (StringUtils.isEmpty(serialDebuggingVO.getHardwareId()))
            return R.fail("硬件信息未填写");
        if (StringUtils.isEmpty(serialDebuggingVO.getGaugeDeviceId()))
            return R.fail("量具设备未填写");
        return R.ok(gaugeCommonService.serialAnalysis(serialDebuggingVO));
    }
}
