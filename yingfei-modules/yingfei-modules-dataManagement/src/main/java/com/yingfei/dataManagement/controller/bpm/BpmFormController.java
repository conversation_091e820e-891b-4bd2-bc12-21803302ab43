package com.yingfei.dataManagement.controller.bpm;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.dataManagement.service.bpm.BpmFormService;
import com.yingfei.entity.dto.BPM_FROM_DTO;
import com.yingfei.entity.vo.BPM_FROM_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@Api(tags = "工作流程 - 动态表单")
@RestController
@RequestMapping("/bpm/form")
@Validated
public class BpmFormController {

    @Resource
    private BpmFormService bpmFormService;

    @CreateUpdateBy
    @NotResubmit
    @PostMapping("/create")
    @ApiOperation("创建动态表单")
    public R<Long> createForm(@RequestBody BPM_FROM_VO bpmFromVo) {
        bpmFormService.createForm(bpmFromVo);
        return R.ok();
    }

    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @PutMapping("/update")
    @ApiOperation("更新动态表单")
    public R<Boolean> updateForm(@RequestBody BPM_FROM_VO bpmFromVo) {
        bpmFormService.updateForm(bpmFromVo);
        return R.ok(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除动态表单")
    public R<Boolean> deleteForm(@RequestParam("id") String id) {
        bpmFormService.deleteForm(id);
        return R.ok();
    }

    @GetMapping("/get")
    @ApiOperation("获得动态表单")
    public R<?> getForm(@RequestParam("id") Long id) {
        BPM_FROM_DTO form = bpmFormService.getForm(id);
        return R.ok(form);
    }

    @PostMapping("/list")
    @ApiOperation("获得动态表单分页")
    public R<?> getFormPage(@RequestBody BPM_FROM_VO bpmFromVo) {
        List<BPM_FROM_DTO> bpmFromDtoList = bpmFormService.getFormPage(bpmFromVo);
        return R.ok(bpmFromDtoList);
    }

}
