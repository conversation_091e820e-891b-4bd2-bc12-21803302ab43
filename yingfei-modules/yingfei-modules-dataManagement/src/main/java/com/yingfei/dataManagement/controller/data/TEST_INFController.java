package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.utils.poi.ExcelUtil;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.datascope.annotation.DataScope;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.TAG_LINKService;
import com.yingfei.dataManagement.service.TEST_INFService;
import com.yingfei.entity.domain.TEST_INF;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.TEST_INF_DTO;
import com.yingfei.entity.enums.TAG_LINKTypeEnum;
import com.yingfei.entity.vo.TEST_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

@Api(tags = "测试信息API")
@Slf4j
@RestController
@RequestMapping("/test_inf")
public class TEST_INFController extends BaseController {

    @Resource
    private TEST_INFService testInfService;
    @Resource
    private TAG_LINKService tagLinkService;

    /**
     * 获取测试信息列表
     */
    @DataScope
    @ApiOperation("获取测试信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody TEST_INF_VO testInfVo) {
        List<TEST_INF_DTO> list = testInfService.getList(testInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(testInfService.getTotal(testInfVo));
        return dataTable;
    }

    /**
     * 新增测试信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:testInf:add")
    @Log(title = "测试管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增测试信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody TEST_INF_VO testInfVo) {
        testInfService.checkParam(testInfVo);
        testInfService.add(testInfVo);
        return R.ok();
    }

    /**
     * 修改测试信息
     */
    @RequiresPermissions("dataManagement:testInf:edit")
    @Log(title = "测试管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改测试信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody TEST_INF_VO testInfVo) {
        //todo 如果测试关联了子组数据就不能进行修改
        testInfService.checkParam(testInfVo);
        TEST_INF testInf = new TEST_INF();
        BeanUtils.copyPropertiesIgnoreNull(testInfVo, testInf);
        if (testInfVo.getF_DFGP() == null) testInf.setF_DFGP(0L);
        testInfService.updateById(testInf);

        tagLinkService.saveByType(testInfVo.getF_TEST(), testInfVo.getTagDatDtoList(), TAG_LINKTypeEnum.TEST_DAT.getCode());

        return R.ok();
    }

    /**
     * 批量删除测试信息
     */
    @RequiresPermissions("dataManagement:testInf:remove")
    @Log(title = "测试管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除测试信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        //todo 对于产品、过程和测试来说，删除任意一个，跟这些关联的子组数据都会被删掉
        testInfService.del(ids);
        return R.ok();
    }

    /**
     * 查询已删除的名称数据
     */
    @ApiOperation("查询已删除的名称数据")
    @PostMapping("/recycleBin")
    public R<?> recycleBin(@RequestBody TEST_INF_VO testInfVo) {
        TEST_INF testInf = testInfService.recycleBin(testInfVo);
        return R.ok(testInf);
    }

    /**
     * 恢复已删除的数据
     */
    @RequiresPermissions("dataManagement:testInf:edit")
    @Log(title = "测试管理-恢复", businessType = BusinessType.UPDATE)
    @ApiOperation("恢复已删除的数据")
    @PostMapping("/recovery/{id}")
    public R<?> recovery(@PathVariable Long id) {
        testInfService.recovery(id);
        return R.ok();
    }

    /**
     * 覆盖已删除的数据
     */
    @RequiresPermissions("dataManagement:testInf:edit")
    @Log(title = "测试管理-覆盖", businessType = BusinessType.UPDATE)
    @ApiOperation("覆盖已删除的数据")
    @PostMapping("/cover/{id}")
    public R<?> cover(@PathVariable Long id) {
        testInfService.cover(id);
        return R.ok();
    }

    @ApiOperation("下载测试导入模板")
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TEST_INF_VO> util = new ExcelUtil<>(TEST_INF_VO.class);
        util.importTemplateExcel(response, "测试数据");
    }

    @ApiOperation("导入测试信息")
    @Log(title = "测试管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("dataManagement:testInf:import")
    @PostMapping("/importData")
    public R<?> importData(@RequestPart("file") MultipartFile file) throws Exception {
        try (InputStream inputStream = file.getInputStream()) {
            ExcelUtil<TEST_INF_VO> util = new ExcelUtil<>(TEST_INF_VO.class);
            List<TEST_INF_VO> testInfVoList = util.importExcel(inputStream);
            Map<String, String> map = testInfService.importTest(testInfVoList);
            return R.ok(map);
        } catch (BusinessException b) {
            log.error("导入产品信息失败");
            return R.fail(b.getMessage());
        } catch (Exception e) {
            log.error("导入产品信息失败");
            e.printStackTrace();
        }
        return R.fail("导入失败");
    }

    @ApiOperation("获取层级对应测试")
    @PostMapping("/getBelongTestList")
    public R<?> getBelongTestList(@RequestBody TEST_INF_VO testInfVo) {
        HIERARCHY_INF_DTO hierarchyInfDto = testInfService.getBelongTestList(testInfVo);
        return R.ok(hierarchyInfDto);
    }

    @ApiOperation("批量修改接口")
    @Log(title = "测试管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions("dataManagement:testInf:edit")
    @PostMapping("/batchEdit")
    public R<?> batchEdit(@RequestBody TEST_INF_VO testInfVo) {
        testInfService.batchEdit(testInfVo);
        return R.ok();
    }

    @ApiOperation("获取测试信息")
    @PostMapping("/getInfo/{id}")
    public R<?> getInfo(@PathVariable Long id) {
        TEST_INF_DTO testInfDto = testInfService.getInfo(id);
        return R.ok(testInfDto);
    }

    @ApiOperation("获取测试删除影响的关联信息")
    @PostMapping("/getOperationAssociation/{ids}")
    public R<?> getOperationAssociation(@PathVariable List<Long> ids) {
        OperationAssociationDTO operationAssociationDTO = testInfService.getOperationAssociation(ids);
        return R.ok(operationAssociationDTO);
    }
}
