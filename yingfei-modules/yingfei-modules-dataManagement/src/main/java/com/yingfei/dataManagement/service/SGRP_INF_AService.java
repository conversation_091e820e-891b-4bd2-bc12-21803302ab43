package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.domain.SGRP_INF;
import com.yingfei.entity.domain.SGRP_INF_A;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.entity.vo.SubgroupFilterVO;

import java.util.List;

/**
 *
 * @description 针对表【SGRP_INF_A(子组主信息缓存表)】的数据库操作Service
 * @createDate 2024-06-20 13:45:49
 */
public interface SGRP_INF_AService extends IService<SGRP_INF_A> {

    SGRP_INF_A getCacheMaxMinTime(boolean b, int dbType);

    List<SubgroupDataDTO> getList(SubgroupFilterVO subgroupFilterVO);

    List<SubgroupDataDTO> getDataHistorical(SubgroupDataVO subgroupDataVO);

    SubgroupDataDTO getDataHistoricalInfo(SubgroupDataDTO subgroupDataDTO);
}
