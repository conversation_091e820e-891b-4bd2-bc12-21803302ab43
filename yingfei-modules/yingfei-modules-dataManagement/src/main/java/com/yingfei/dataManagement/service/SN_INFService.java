package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.SN_INF;
import com.yingfei.entity.dto.SN_INF_DTO;
import com.yingfei.entity.vo.SN_INF_VO;

/**
* <AUTHOR>
* @description 针对表【SN_INF(储存序列号信息表)】的数据库操作Service
* @createDate 2024-05-08 16:28:43
*/
public interface SN_INFService extends IService<SN_INF>, BaseService<SN_INF_VO, SN_INF_DTO> {

}
