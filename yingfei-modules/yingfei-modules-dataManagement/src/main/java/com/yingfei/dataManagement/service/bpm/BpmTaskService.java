package com.yingfei.dataManagement.service.bpm;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.utils.CollectionToMapUtils;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.entity.domain.BPM_TASK;
import com.yingfei.entity.dto.CamundaTaskDTO;
import com.yingfei.entity.dto.EVNT_INF_DTO;
import com.yingfei.entity.vo.*;
import org.camunda.bpm.engine.task.Task;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 流程任务实例 Service 接口
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public interface BpmTaskService extends IService<BPM_TASK> {

    /**
     * 获得待办的流程任务分页
     *
     * @param userId    用户编号
     * @param pageReqVO 分页请求
     *
     * @return 流程任务分页
     */
    TableDataInfo<BpmTaskTodoPageItemRespVO> getTodoTaskPage(Long userId, BpmTaskTodoPageReqVO pageReqVO);

    /**
     * 获得已办的流程任务分页
     *
     * @param userId    用户编号
     * @param pageReqVO 分页请求
     *
     * @return 流程任务分页
     */
    TableDataInfo<BpmTaskDonePageItemRespVO> getDoneTaskPage(Long userId, BpmTaskTodoPageReqVO pageReqVO);

    /**
     * 获得流程任务 Map
     *
     * @param processInstanceIds 流程实例的编号数组
     *
     * @return 流程任务 Map
     */
    default Map<String, List<Task>> getTaskMapByProcessInstanceIds(List<String> processInstanceIds) {
        return CollectionToMapUtils.convertMultiMap(getTasksByProcessInstanceIds(processInstanceIds),
            Task::getProcessInstanceId);
    }

    /**
     * 获得流程任务列表
     *
     * @param processInstanceIds 流程实例的编号数组
     *
     * @return 流程任务列表
     */
    List<Task> getTasksByProcessInstanceIds(List<String> processInstanceIds);

    /**
     * 获得指令流程实例的流程任务列表，包括所有状态的
     *
     * @param processInstanceId 流程实例的编号
     *
     * @return 流程任务列表
     */
    List<BpmTaskRespVO>  getTaskListByProcessInstanceId(String processInstanceId);

    /**
     * 通过任务
     *
     * @param userId 用户编号
     * @param reqVO  通过请求
     */
    void approveTask(Long userId, @Valid BpmTaskApproveReqVO reqVO);

    /**
     * 不通过任务
     *
     * @param userId 用户编号
     * @param reqVO  不通过请求
     */
    void rejectTask(Long userId, @Valid BpmTaskApproveReqVO reqVO);

    /**
     * 将流程任务分配给指定用户
     *
     * @param userId 用户编号
     * @param reqVO  分配请求
     */
    void updateTaskAssignee(Long userId, BpmTaskApproveReqVO reqVO);

    /**
     * 将流程任务分配给指定用户
     *
     * @param id     流程任务编号
     * @param userId 用户编号
     */
    void updateTaskAssignee(String id, String userId);

    /**
     * 创建 Task 拓展记录
     *
     * @param task 任务实体
     */
    void createTaskExt(CamundaTaskDTO task);

    void createTaskExt(BPM_TASK task);

    /**
     * 更新 Task 拓展记录为完成
     *
     * @param task 任务实体
     */
    void updateTaskExtComplete(CamundaTaskDTO task);

    /**
     * 更新 Task 拓展记录为已取消
     *
     * @param taskId 任务的编号
     */
    void updateTaskExtCancel(String taskId);

    /**
     * 更新 Task 拓展记录，并发送通知
     *
     * @param task 任务实体
     */
    void updateTaskExtAssign(CamundaTaskDTO task);

    List<BpmTaskRespVO> getTaskList(String processInstanceId);

    void returns(Long userId, BpmTaskApproveReqVO reqVO);

    EVNT_INF_DTO getEvntInf(String processInstanceId);
}
