package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.CTRL_INF;
import com.yingfei.entity.dto.CTRL_INF_DTO;
import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.PARAMETER_SET_INF_DTO;
import com.yingfei.entity.dto.chart.ControlChartDTO;
import com.yingfei.entity.enums.ParetoAnalyseTypeEnum;
import com.yingfei.entity.vo.CTRL_INF_VO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【CTRL_INF(储存控制限表)】的数据库操作Service
* @createDate 2024-05-08 16:26:53
*/
public interface CTRL_INFService extends IService<CTRL_INF>, BaseService<CTRL_INF_VO, CTRL_INF_DTO> {

    void edit(CTRL_INF ctrlInf);

    void delCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum);

    List<ControlChartDTO> ctrlGenerate(CTRL_INF_VO ctrlInfVo);

    ControlLimitDTO backstepping(ControlLimitDTO controlLimitDTO, Integer type);

    void calculateControlLimit(CTRL_INF_VO ctrlInfVo);

    List<ControlChartDTO> batchCalculateControlLimit(PARAMETER_SET_INF_DTO parameterSetInfDto);

    void batchAdd(List<CTRL_INF_VO> ctrlInfVoList);

    List<CTRL_INF> getCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum);
    /**
     * 根据产品、过程、测试和版本查询历史图表类型
     * @param ctrlInfVo 查询参数
     * @return 图表类型
     */
    Integer queryHistoricalChartType(CTRL_INF_VO ctrlInfVo);
}
