package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.SHIFT_GRP;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.SHIFT_GRP_DTO;
import com.yingfei.entity.vo.SHIFT_GRP_VO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【SHIFT_GRP(储存班次组信息表)】的数据库操作Service
 * @createDate 2024-05-08 16:28:39
 */
public interface SHIFT_GRPService extends IService<SHIFT_GRP>, BaseService<SHIFT_GRP_VO, SHIFT_GRP_DTO> {

    SHIFT_GRP_DTO getInfo(Long id, List<Long> childIds);

}
