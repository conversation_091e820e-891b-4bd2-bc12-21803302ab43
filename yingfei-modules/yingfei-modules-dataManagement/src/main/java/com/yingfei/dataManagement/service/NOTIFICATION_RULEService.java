package com.yingfei.dataManagement.service;

import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.NOTIFICATION_RULE;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.NOTIFICATION_RULE_DTO;
import com.yingfei.entity.vo.NOTIFICATION_RULE_VO;

import java.util.List;
import java.util.Map;

/**
* 
* @description 针对表【NOTIFICATION_RULE(报警通知配置表)】的数据库操作Service
* @createDate 2024-08-19 15:36:07
*/
public interface NOTIFICATION_RULEService extends IService<NOTIFICATION_RULE>, BaseService<NOTIFICATION_RULE_VO, NOTIFICATION_RULE_DTO> {

    Map<String, List<Long>> getFilterCondition(String dataJson);

    void edit(NOTIFICATION_RULE_VO notificationRuleVo);
}
