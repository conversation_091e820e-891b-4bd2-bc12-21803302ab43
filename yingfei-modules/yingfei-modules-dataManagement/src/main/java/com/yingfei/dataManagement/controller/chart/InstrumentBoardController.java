package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.dataManagement.Timing.CapabilityTrendTiming;
import com.yingfei.dataManagement.service.chart.InstrumentBoardService;
import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.InstrumentBoardDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "图表:能力仪表盘API")
@RequestMapping("/instrumentBoard")
public class InstrumentBoardController {

    @Resource
    private InstrumentBoardService aggregateAnalysisService;
    @Resource
    private CapabilityTrendTiming capabilityTrendTiming;

    /**
     * 获取仪表盘详情
     *
     * @return
     */
    @PostMapping("/info")
    @ApiOperation("获取能力仪表盘详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = InstrumentBoardDTO.class)
    })
    public R<?> getClusterAnalysisInfo(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        List<InstrumentBoardDTO> list = aggregateAnalysisService.clusterAnalysisInfo(subgroupDataSelectionDTO);
        return R.ok(list);
    }

    /**
     * 获取能力趋势
     */
    @PostMapping("/trend")
    @ApiOperation("获取能力趋势")
    public R<?> getTrend(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        List<STREAM_TREND_INF_DTO> capabilityTrendInfList = aggregateAnalysisService.getTrend(subgroupDataSelectionDTO);
        return R.ok(capabilityTrendInfList);
    }

    /**
     * 获取能力趋势
     */
    @PostMapping("/trendList")
    @ApiOperation("获取能力趋势")
    public R<?> getTrendList(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        subgroupDataSelectionDTO.setIsRange(YesOrNoEnum.YES.getType());
        List<List<STREAM_TREND_INF_DTO>> capabilityTrendInfList = aggregateAnalysisService.getTrendList(subgroupDataSelectionDTO);
        return R.ok(capabilityTrendInfList);
    }

    /**instrumentBoard/trendList
     * 指定时间计算能力趋势
     */
    @PostMapping("/appointedTime")
    @ApiOperation("指定时间计算能力趋势")
    public R<?> appointedTime(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        capabilityTrendTiming.getStartAndEnd(subgroupDataSelectionDTO.getStartDate(), subgroupDataSelectionDTO.getEndDate(),0);
        return R.ok();
    }
}
