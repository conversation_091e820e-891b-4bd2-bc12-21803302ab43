package com.yingfei.dataManagement.mapper;

import com.yingfei.entity.domain.IQC_GB2828_INF;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【IQC_GB2828_INF】的数据库操作Mapper
* @createDate 2025-04-23 13:58:24
* @Entity com.yingfei.entity.domain.IQC_GB2828_INF
*/
public interface IQC_GB2828_INFMapper extends BaseMapper<IQC_GB2828_INF> {

    List<IQC_GB2828_INF> getList(IQC_GB2828_INF iqcGb2828Inf);

    long getTotal(IQC_GB2828_INF iqcGb2828Inf);
}




