package com.yingfei.dataManagement.controller.chart;

import com.alibaba.fastjson2.JSONArray;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_TEMPLATE_INF;
import com.yingfei.entity.dto.ANALYSIS_DASHBOARD_TEMPLATE_INF_DTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.vo.ANALYSIS_DASHBOARD_TEMPLATE_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api(tags = "分析页面模板API")
@RestController
@RequestMapping("/analysis_dashboard_template_inf")
public class ANALYSIS_DASHBOARD_TEMPLATE_INFController extends BaseController {

    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;

    /**
     * 获取分析页面模板列表
     */
    @ApiOperation("获取分析页面模板列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody ANALYSIS_DASHBOARD_TEMPLATE_INF_VO analysisDashboardTemplateInfVo) {
        List<ANALYSIS_DASHBOARD_TEMPLATE_INF_DTO> list = analysisDashboardTemplateInfService.getList(analysisDashboardTemplateInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(analysisDashboardTemplateInfService.getTotal(analysisDashboardTemplateInfVo));
        return dataTable;
    }

    /**
     * 新增分析页面模板
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:analysisDashboardTemplateInf:add")
    @Log(title = "分析页面模板管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增分析页面模板")
    @PostMapping("/add")
    public R<?> add(@RequestBody ANALYSIS_DASHBOARD_TEMPLATE_INF_VO analysisDashboardTemplateInfVo) {
        analysisDashboardTemplateInfService.checkParam(analysisDashboardTemplateInfVo);
        analysisDashboardTemplateInfService.add(analysisDashboardTemplateInfVo);
        return R.ok();
    }

    /**
     * 修改分析页面模板
     */
    @RequiresPermissions("dataManagement:analysisDashboardTemplateInf:edit")
    @Log(title = "分析页面模板管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改分析页面模板")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody ANALYSIS_DASHBOARD_TEMPLATE_INF_VO analysisDashboardTemplateInfVo) {
        analysisDashboardTemplateInfService.checkParam(analysisDashboardTemplateInfVo);
        ANALYSIS_DASHBOARD_TEMPLATE_INF analysisDashboardTemplateInf = new ANALYSIS_DASHBOARD_TEMPLATE_INF();
        BeanUtils.copyPropertiesIgnoreNull(analysisDashboardTemplateInfVo, analysisDashboardTemplateInf);
        if (CollectionUtils.isNotEmpty(analysisDashboardTemplateInfVo.getAnalysisChartConfigDTOList())) {
            analysisDashboardTemplateInf.setF_DATA(JSONArray.toJSONString(analysisDashboardTemplateInfVo.getAnalysisChartConfigDTOList()));
        }
        analysisDashboardTemplateInfService.updateById(analysisDashboardTemplateInf);
        return R.ok();
    }

    /**
     * 批量删除分析页面模板
     */
    @RequiresPermissions("dataManagement:analysisDashboardTemplateInf:remove")
    @Log(title = "分析页面模板管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除分析页面模板")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        analysisDashboardTemplateInfService.del(ids);
        return R.ok();
    }

    /**
     * 获取所有图表类型
     */
    @ApiOperation("获取所有图表类型")
    @GetMapping("/getChartType")
    public R<?> getChartType() {
        Map<String, String> map = ChartTypeEnum.getMap();
        return R.ok(map);
    }
}
