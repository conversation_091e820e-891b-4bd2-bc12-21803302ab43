package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.ROOT_CAUSE_DAT;
import com.yingfei.entity.dto.ROOT_CAUSE_DAT_DTO;
import com.yingfei.entity.vo.ROOT_CAUSE_DAT_VO;

/**
* <AUTHOR>
* @description 针对表【ROOT_CAUSE_DAT(储存异常原因信息表)】的数据库操作Service
* @createDate 2024-05-08 16:28:25
*/
public interface ROOT_CAUSE_DATService extends IService<ROOT_CAUSE_DAT>, BaseService<ROOT_CAUSE_DAT_VO, ROOT_CAUSE_DAT_DTO> {

}
