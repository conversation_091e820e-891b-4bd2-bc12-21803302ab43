package com.yingfei.dataManagement.service.manufacturing;

import com.yingfei.entity.domain.MANUFACTURING_NODE_INF;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.MANUFACTURING_NODE_INF_DTO;
import com.yingfei.entity.dto.PARAMETER_CHILD_DTO;
import com.yingfei.entity.vo.MANUFACTURING_NODE_INF_VO;

import java.util.List;
import java.util.Map;

/**
* 
* @description 针对表【MANUFACTURING_NODE_INF(流程节点表)】的数据库操作Service
* @createDate 2024-06-13 15:01:14
*/
public interface MANUFACTURING_NODE_INFService extends IService<MANUFACTURING_NODE_INF> {

    Long add(MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo);

    void del(List<Long> ids);

    MANUFACTURING_NODE_INF_DTO getInfo(Long id);

    void addCount(Long fMfnd);

    void edit(MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo);

    Map<String, Object> getSearchCondition(List<PARAMETER_CHILD_DTO> parameterChildDtos);

    Map<String, Object> judgeConditionIsDelete(List<PARAMETER_CHILD_DTO> parameterChildDtos);

    List<MANUFACTURING_NODE_INF_DTO> getList(MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo);
}
