package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.SHIFT_GRPService;
import com.yingfei.entity.domain.SHIFT_GRP;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.SHIFT_DAT_DTO;
import com.yingfei.entity.dto.SHIFT_GRP_DTO;
import com.yingfei.entity.vo.SHIFT_GRP_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "班次组信息API")
@RestController
@RequestMapping("/shift_grp")
public class SHIFT_GRPController extends BaseController {
    
    @Resource
    private SHIFT_GRPService shiftGrpService;

    /**
     * 获取班次组信息列表
     */
    @ApiOperation("获取班次组信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody SHIFT_GRP_VO shiftGrpVo) {
        List<SHIFT_GRP_DTO> list = shiftGrpService.getList(shiftGrpVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(shiftGrpService.getTotal(shiftGrpVo));
        return dataTable;
    }

    /**
     * 新增班次组信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:shiftGrp:add")
    @Log(title = "班次组管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增班次组信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody SHIFT_GRP_VO shiftGrpVo) {
        shiftGrpService.checkParam(shiftGrpVo);
        shiftGrpService.add(shiftGrpVo);
        return R.ok();
    }

    /**
     * 修改班次组信息
     */
    @RequiresPermissions("dataManagement:shiftGrp:edit")
    @Log(title = "班次组管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改班次组信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody SHIFT_GRP_VO shiftGrpVo) {
        shiftGrpService.checkParam(shiftGrpVo);
        SHIFT_GRP shiftGrp = new SHIFT_GRP();
        BeanUtils.copyPropertiesIgnoreNull(shiftGrpVo, shiftGrp);
        shiftGrpService.updateById(shiftGrp);
        return R.ok();
    }

    /**
     * 批量删除班次组信息
     */
    @RequiresPermissions("dataManagement:shiftGrp:remove")
    @Log(title = "班次组管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除班次组信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        shiftGrpService.del(ids);
        return R.ok();
    }
}
