package com.yingfei.dataManagement.controller.bpm;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.dataManagement.service.bpm.BpmProcessInstanceService;
import com.yingfei.entity.dto.BPM_PROCESS_INSTANCE_DTO;
import com.yingfei.entity.vo.BPM_PROCESS_INSTANCE_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "工作流程 - 流程实例") // 流程实例，通过流程定义创建的一次“申请”
@RestController
@RequestMapping("/bpm/process-instance")
@Validated
public class BpmProcessInstanceController extends BaseController {

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @PostMapping("/my-page")
    @ApiOperation("获得我的实例分页列表")
    public TableDataInfo<?> getMyProcessInstancePage(@RequestBody BPM_PROCESS_INSTANCE_VO pageReqVO) {
        List<BPM_PROCESS_INSTANCE_DTO> myProcessInstancePage = processInstanceService.getMyProcessInstancePage(pageReqVO);
        TableDataInfo<?> dataTable = getDataTable(myProcessInstancePage);
        dataTable.setTotal(processInstanceService.getTotal(pageReqVO));
        return dataTable;
    }

    @CreateUpdateBy
    @NotResubmit
    @PostMapping("/create")
    @ApiOperation("新建流程实例")
    public R<?> createProcessInstance(@RequestBody BPM_PROCESS_INSTANCE_VO createReqVO) {
        return R.ok(processInstanceService.createProcessInstance(createReqVO));
    }


    @PostMapping("/createAlarm")
    @ApiOperation("新建报警流程实例")
    public R<?> createAlarmProcessInstance(@RequestBody BPM_PROCESS_INSTANCE_VO createReqVO) {
        return R.ok(processInstanceService.createAlarmProcessInstance(createReqVO));
    }

    @GetMapping("/get")
    @ApiOperation("获得指定流程实例")
    public R<?> getProcessInstance(@RequestParam("id") String id) {
        return R.ok(processInstanceService.getProcessInstanceVO(id));
    }

    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @DeleteMapping("/cancel")
    @ApiOperation("取消流程实例,撤回发起的流程")
    public R<Boolean> cancelProcessInstance(@RequestBody BPM_PROCESS_INSTANCE_VO cancelReqVO) {
        processInstanceService.cancelProcessInstance(cancelReqVO);
        return R.ok(true);
    }

    /**
     * 删除流程实例
     */
    @DeleteMapping("/delete/{processInstance}")
    @ApiOperation("取消流程实例,撤回发起的流程")
    public R<?> deleteProcessInstance(@PathVariable String processInstance) {
        JudgeUtils.isEmpty(processInstance, CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        processInstanceService.deleteProcessInstance(processInstance);
        return R.ok();
    }

    /**
     * 批量删除流程实例
     */
    @DeleteMapping("/deleteIds/{processInstanceList}")
    @ApiOperation("批量删除流程实例")
    public R<?> deleteProcessInstances(@PathVariable List<String> processInstanceList) {
        JudgeUtils.isTrue(CollectionUtils.isEmpty(processInstanceList), CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        processInstanceList.forEach(processInstance -> {
            processInstanceService.deleteProcessInstance(processInstance);
        });
        return R.ok();
    }
}
