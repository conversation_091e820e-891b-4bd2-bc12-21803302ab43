package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.dataManagement.service.chart.HistogramService;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.HistogramDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 直方图Controller
 */
@Slf4j
@RestController
@Api(tags = "图表:直方图API")
@RequestMapping("/histogram")
public class HistogramController {

    @Resource
    private HistogramService histogramService;

    @PostMapping("/info")
    @ApiOperation("直方图")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = HistogramDTO.class)
    })
    public R<HistogramDTO> histogram(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        HistogramDTO histogramDTO = histogramService.getInfo(subgroupDataSelectionDTO);
        return R.ok(histogramDTO);
    }

    @PostMapping("/infoList")
    @ApiOperation("多直方图")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = HistogramDTO.class)
    })
    public R<List<HistogramDTO>> histogramList(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        subgroupDataSelectionDTO.setIsRange(YesOrNoEnum.YES.getType());
        List<HistogramDTO> list = histogramService.getInfoList(subgroupDataSelectionDTO);
        return R.ok(list);
    }


}
