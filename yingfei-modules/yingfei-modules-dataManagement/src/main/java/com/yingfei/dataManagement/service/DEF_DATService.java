package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.DEF_DAT;
import com.yingfei.entity.dto.DEF_DAT_DTO;
import com.yingfei.entity.vo.DEF_DAT_VO;

/**
* <AUTHOR>
* @description 针对表【DEF_DAT(储存缺陷信息表)】的数据库操作Service
* @createDate 2024-05-08 16:26:56
*/
public interface DEF_DATService extends IService<DEF_DAT>, BaseService<DEF_DAT_VO, DEF_DAT_DTO> {

    DEF_DAT addDefDat(DEF_DAT_VO defDatVo);
}
