package com.yingfei.dataManagement.service;

import com.yingfei.entity.domain.IQC_GB2828_INF;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.vo.excel.IQC_GB2828_EXCEL_VO;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【IQC_GB2828_INF】的数据库操作Service
* @createDate 2025-04-23 13:58:24
*/
public interface IQC_GB2828_INFService extends IService<IQC_GB2828_INF> {

    Map<String, String> importFile(List<IQC_GB2828_EXCEL_VO> iqcGb2828ExcelVoList);

    List<IQC_GB2828_INF> getList(IQC_GB2828_INF iqcGb2828Inf);

    long getTotal(IQC_GB2828_INF iqcGb2828Inf);
}
