package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.PARAMETER_SET_INF;
import com.yingfei.entity.dto.PARAMETER_CHILD_DTO;
import com.yingfei.entity.dto.PARAMETER_SET_INF_DTO;
import com.yingfei.entity.vo.PARAMETER_SET_INF_VO;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【PARAMETER_SET_INF(参数集表)】的数据库操作Service
* @createDate 2024-05-08 16:27:57
*/
public interface PARAMETER_SET_INFService extends IService<PARAMETER_SET_INF>, BaseService<PARAMETER_SET_INF_VO, PARAMETER_SET_INF_DTO> {

    PARAMETER_SET_INF_DTO info(Long id);

    List<Object> getParameter(PARAMETER_CHILD_DTO parameterChildDto);

    Map<String, List<Long>> getSearchCondition(List<PARAMETER_CHILD_DTO> parameterChildDtoList);
    Map<String, Object> getSearchConditionObject(List<PARAMETER_CHILD_DTO> parameterChildDtoList);
}
