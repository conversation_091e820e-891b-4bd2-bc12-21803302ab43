package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.service.chart.SingleAnalysisService;
import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@Api(tags = "图表:单项分析API")
@RequestMapping("/singleAnalysis")
public class SingleAnalysisController {
    @Resource
    private SingleAnalysisService singleAnalysisService;
    @Resource
    private RedisService redisService;

    /**
     * 单项分析: 子组查看数据
     */
    @ApiOperation("单项分析: 子组查看数据")
    @PostMapping("/viewData")
    public R<?> viewData(@Valid @RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        Map<String, Object> map = singleAnalysisService.viewData(subgroupDataSelectionDTO);
        return R.ok(map);
    }
    /**
     * 单项分析: 子组查看数据
     */
    @ApiOperation("单项分析: 子组查看数据")
    @PostMapping("/viewDataList")
    public R<?> viewDataList(@Valid @RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        subgroupDataSelectionDTO.setIsRange(YesOrNoEnum.YES.getType());
        Map<String, Object> map = singleAnalysisService.viewDataList(subgroupDataSelectionDTO);
        return R.ok(map);
    }

    /**
     * 异常汇总列表
     */
    @ApiOperation("单项分析: 异常汇总列表")
    @PostMapping("/exceptionSummary")
    public R<?> exceptionSummary(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        Map<String, Object> map = singleAnalysisService.exceptionSummary(subgroupDataSelectionDTO);
        return R.ok(map);
    }
    /**
     * 异常汇总列表
     */
    @ApiOperation("单项分析: 异常汇总列表")
    @PostMapping("/exceptionSummaryList")
    public R<?> exceptionSummaryList(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        subgroupDataSelectionDTO.setIsRange(YesOrNoEnum.YES.getType());
        Map<String, Object> map = singleAnalysisService.exceptionSummaryList(subgroupDataSelectionDTO);
        return R.ok(map);
    }

    /**
     * 获取单项分析搜索条件
     */
    @ApiOperation("单项分析: 获取单项分析搜索条件")
    @PostMapping("/getSearch")
    public R<?> getSearch(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        subgroupDataSelectionDTO = singleAnalysisService.getSearch(subgroupDataSelectionDTO);
        return R.ok(subgroupDataSelectionDTO);
    }

    /**
     * 实时能力趋势
     */
    @ApiOperation("单项分析: 实时能力趋势")
    @PostMapping("/realTimeCapabilityTrend")
    public R<?> realTimeCapabilityTrend(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        List<STREAM_TREND_INF_DTO> streamTrendInfDtos = singleAnalysisService.realTimeCapabilityTrend(subgroupDataSelectionDTO);
        return R.ok(streamTrendInfDtos);
    }
    /**
     * 实时能力趋势
     */
    @ApiOperation("单项分析: 实时能力趋势")
    @PostMapping("/realTimeCapabilityTrendList")
    public R<?> realTimeCapabilityTrendList(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        subgroupDataSelectionDTO.setIsRange(YesOrNoEnum.YES.getType());
        List<List<STREAM_TREND_INF_DTO>> streamTrendInfs = singleAnalysisService.realTimeCapabilityTrendList(subgroupDataSelectionDTO);
        return R.ok(streamTrendInfs);
    }

    /**
     * 导出实时能力趋势图数据
     */
    @PostMapping("/exportStreamTrend/{parameterId}")
    @ApiOperation("导出实时能力趋势图数据")
    public void exportStreamTrend(@PathVariable("parameterId") String parameterId,
                                  @RequestParam(value = "type",required = false) Integer type,
                                  @RequestParam(value ="file",required = false) MultipartFile file, HttpServletResponse response) throws IOException {

        singleAnalysisService.exportStreamTrend(parameterId,type, file,response);
    }

}
