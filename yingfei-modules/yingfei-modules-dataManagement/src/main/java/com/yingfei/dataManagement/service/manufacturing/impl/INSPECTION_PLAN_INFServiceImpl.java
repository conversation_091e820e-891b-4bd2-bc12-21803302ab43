package com.yingfei.dataManagement.service.manufacturing.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.ListUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.mapper.INSPECTION_PLAN_INFMapper;
import com.yingfei.dataManagement.mapper.INSPECTION_PLAN_RECORD_INFMapper;
import com.yingfei.dataManagement.mapper.ROLE_INFMapper;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.gauge.GAUGE_AGENTService;
import com.yingfei.dataManagement.service.gauge.GAUGE_CONNECTIONService;
import com.yingfei.dataManagement.service.gauge.GAUGE_DEVICEService;
import com.yingfei.dataManagement.service.gauge.GAUGE_FORMATService;
import com.yingfei.dataManagement.service.manufacturing.INSPECTION_PLAN_INFService;
import com.yingfei.dataManagement.service.manufacturing.MANUFACTURING_NODE_INFService;
import com.yingfei.dataManagement.service.schedule.SCHEDULE_JOB_INFService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.enums.DataValTypeEnum;
import com.yingfei.entity.enums.SpecialHandlingTypeEnum;
import com.yingfei.entity.enums.TimeEnum;
import com.yingfei.entity.util.cron.CronUtil;
import com.yingfei.entity.vo.INSPECTION_PLAN_INF_VO;
import com.yingfei.entity.vo.SerialDebuggingVO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.entity.vo.SubgroupFilterVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.Collator;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @description 针对表【INSPECTION_PLAN_INF(检查计划表)】的数据库操作Service实现
 * @createDate 2024-06-13 15:00:00
 */
@Slf4j
@Service
public class INSPECTION_PLAN_INFServiceImpl extends ServiceImpl<INSPECTION_PLAN_INFMapper, INSPECTION_PLAN_INF>
        implements INSPECTION_PLAN_INFService {

    @Resource
    private MANUFACTURING_NODE_INFService manufacturingNodeInfService;
    @Resource
    private RedisService redisService;
    @Resource
    private GAUGE_DEVICEService gaugeDeviceService;
    @Resource
    private GAUGE_FORMATService gaugeFormatService;
    @Resource
    private GAUGE_CONNECTIONService gaugeConnectionService;
    @Resource
    private GAUGE_AGENTService gaugeAgentService;
    @Resource
    private SGRP_INF_AService sgrpInfAService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private PART_INFService partInfService;
    @Resource
    private PART_REVService partRevService;
    @Resource
    private PRCS_INFService prcsInfService;
    @Resource
    private SCHEDULE_JOB_INFService scheduleJobInfService;
    @Resource
    private INSPECTION_PLAN_RECORD_INFMapper inspectionPlanRecordInfMapper;
    @Resource
    private ROLE_INFMapper roleInfMapper;

    @Override
    public INSPECTION_PLAN_INF_DTO getInfo(Long id) {
        INSPECTION_PLAN_INF inspectionPlanInf = baseMapper.selectById(id);
        INSPECTION_PLAN_INF_DTO inspectionPlanInfDto = new INSPECTION_PLAN_INF_DTO();
        if (Objects.isNull(inspectionPlanInf)) return inspectionPlanInfDto;
        BeanUtils.copyPropertiesIgnoreNull(inspectionPlanInf, inspectionPlanInfDto);
        if (StringUtils.isNotEmpty(inspectionPlanInf.getF_DATA())) {
            INSPECTION_PLAN_CONFIGURATION_DTO inspectionPlanConfigurationDto =
                    JSONObject.parseObject(inspectionPlanInf.getF_DATA(), INSPECTION_PLAN_CONFIGURATION_DTO.class);
            inspectionPlanInfDto.setInspectionPlanConfigurationDto(inspectionPlanConfigurationDto);
        }
        if (StringUtils.isNotEmpty(inspectionPlanInf.getF_CHILD())) {
            Map<String, List<INSPECTION_PLAN_CHILD_DTO>> map =
                    JSONObject.parseObject(inspectionPlanInf.getF_CHILD())
                            .entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, entry ->
                                    JSONArray.parseArray(String.valueOf(entry.getValue()), INSPECTION_PLAN_CHILD_DTO.class)));

            inspectionPlanInfDto.setInspectionPlanChildDtoList(map);
        }
        return inspectionPlanInfDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        INSPECTION_PLAN_INF inspectionPlanInf = new INSPECTION_PLAN_INF();
        BeanUtils.copyPropertiesIgnoreNull(inspectionPlanInfVo, inspectionPlanInf);
        if (inspectionPlanInfVo.getInspectionPlanConfigurationDto() != null) {
            inspectionPlanInf.setF_DATA(JSONObject.toJSONString(inspectionPlanInfVo.getInspectionPlanConfigurationDto()));
        }
        if (MapUtils.isNotEmpty(inspectionPlanInfVo.getInspectionPlanChildDtoList())) {
            JSONObject jsonObject = new JSONObject(inspectionPlanInfVo.getInspectionPlanChildDtoList());
            inspectionPlanInf.setF_CHILD(jsonObject.toJSONString());
            inspectionPlanInf.setF_COUNT(inspectionPlanInfVo.getInspectionPlanChildDtoList().size());
        }

        baseMapper.insert(inspectionPlanInf);
        inspectionPlanInfVo.setF_PLAN(inspectionPlanInf.getF_PLAN());
        addPlanJob(inspectionPlanInfVo);

        /*新增成功后把对应节点检查数量加一*/
        manufacturingNodeInfService.addCount(inspectionPlanInfVo.getF_MFND());
        return inspectionPlanInfVo.getF_PLAN();
    }

    /**
     * 添加检查计划对应调度任务
     *
     * @param inspectionPlanInfVo
     */
    private void addPlanJob(INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        if (inspectionPlanInfVo.getInspectionPlanConfigurationDto() != null) {
            /*判断当前时间是否大于任务开始时间*/
            INSPECTION_PLAN_CONFIGURATION_DTO inspectionPlanConfigurationDto = inspectionPlanInfVo.getInspectionPlanConfigurationDto();
            if (inspectionPlanConfigurationDto.getStartExecutionTime() != null) {
                if (inspectionPlanConfigurationDto.getStartExecutionTime().getTime() > DateUtils.getNowDate().getTime()) {
                    /*放入redis监听*/
                    long l = (inspectionPlanConfigurationDto.getStartExecutionTime().getTime() - DateUtils.getNowDate().getTime()) / 1000;
                    redisService.set(RedisConstant.CHECK_PLAN_IDENTIFY + "|" + inspectionPlanInfVo.getF_PLAN(), inspectionPlanInfVo.getF_PLAN(), l);
                } else {
                    saveScheduleJob(inspectionPlanInfVo);
                }
            }
        }
    }

    public void saveScheduleJob(INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        /*创建定时任务执行*/
        /*根据时间类型和时间间隔生成cron表达式*/
        TaskScheduleModel taskScheduleModel = new TaskScheduleModel();
        INSPECTION_PLAN_CONFIGURATION_DTO inspectionPlanConfigurationDto = inspectionPlanInfVo.getInspectionPlanConfigurationDto();
        TimeEnum timeEnum = TimeEnum.getType(inspectionPlanConfigurationDto.getTimeType());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inspectionPlanConfigurationDto.getStartExecutionTime());
        int seconds = calendar.get(Calendar.SECOND);
        int minute = calendar.get(Calendar.MINUTE);
        int hour = calendar.get(Calendar.HOUR);
        taskScheduleModel.setTimeEnum(timeEnum)
                .setSecond(seconds)
                .setMinute(minute)
                .setHour(hour)
                .setBeApart(inspectionPlanConfigurationDto.getTimeInterval());
        String cron = CronUtil.createCronExpression(taskScheduleModel);

        SCHEDULE_JOB_INF scheduleJobInf = new SCHEDULE_JOB_INF();
        scheduleJobInf.setF_NAME(inspectionPlanInfVo.getF_NAME());
        scheduleJobInf.setF_TASK_IDENTIFY(Constants.checkPlanIdentify);
        scheduleJobInf.setF_CRON(cron);
        scheduleJobInf.setF_BUID(inspectionPlanInfVo.getF_PLAN());
        scheduleJobInf.setF_CRUE(inspectionPlanInfVo.getF_CRUE());
        scheduleJobInf.setF_EDUE(inspectionPlanInfVo.getF_CRUE());
        scheduleJobInfService.add(scheduleJobInf);

//        /*生成报警定时任务*/
//        if (inspectionPlanConfigurationDto.getStartAfterTime() != null && inspectionPlanConfigurationDto.getStartAfterTime() != 0) {
//            TimeEnum anEnum = TimeEnum.getType(inspectionPlanConfigurationDto.getTipTimeType());
//            taskScheduleModel.setTimeEnum(anEnum);
//            taskScheduleModel.setBeApart(inspectionPlanConfigurationDto.getTimeInterval() + inspectionPlanConfigurationDto.getStartAfterTime());
//            String afterCron = CronUtil.createCronExpression(taskScheduleModel);
//            SCHEDULE_JOB_INF afterScheduleJobInf = new SCHEDULE_JOB_INF();
//            afterScheduleJobInf.setF_NAME(inspectionPlanInfVo.getF_NAME());
//            afterScheduleJobInf.setF_TASK_IDENTIFY(Constants.checkPlanAlarmIdentify);
//            afterScheduleJobInf.setF_CRON(afterCron);
//            afterScheduleJobInf.setF_BUID(inspectionPlanInfVo.getF_PLAN());
//            afterScheduleJobInf.setF_CRUE(inspectionPlanInfVo.getF_CRUE());
//            afterScheduleJobInf.setF_EDUE(inspectionPlanInfVo.getF_CRUE());
//            scheduleJobInfService.add(afterScheduleJobInf);
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        /*删除对应的调度任务*/
        delPlanJob(ids);

        LambdaQueryWrapper<INSPECTION_PLAN_INF> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(INSPECTION_PLAN_INF::getF_PLAN, ids).eq(INSPECTION_PLAN_INF::getF_DEL, DelFlagEnum.USE.getType());
        baseMapper.delete(wrapper);


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        INSPECTION_PLAN_INF inspectionPlanInf = new INSPECTION_PLAN_INF();
        BeanUtils.copyPropertiesIgnoreNull(inspectionPlanInfVo, inspectionPlanInf);
        if (inspectionPlanInfVo.getInspectionPlanConfigurationDto() != null) {
            inspectionPlanInf.setF_DATA(JSONObject.toJSONString(inspectionPlanInfVo.getInspectionPlanConfigurationDto()));
        }

        if (MapUtils.isNotEmpty(inspectionPlanInfVo.getInspectionPlanChildDtoList())) {
            JSONObject jsonObject = new JSONObject(inspectionPlanInfVo.getInspectionPlanChildDtoList());
            inspectionPlanInf.setF_CHILD(jsonObject.toJSONString());
        }
        inspectionPlanInf.setF_COUNT(inspectionPlanInfVo.getInspectionPlanChildDtoList().size());

        delPlanJob(Collections.singletonList(inspectionPlanInfVo.getF_PLAN()));
        /*创建新调度任务*/
        addPlanJob(inspectionPlanInfVo);

        baseMapper.updateById(inspectionPlanInf);
    }

    /**
     * 删除检查计划对应的调度任务
     *
     * @param planIds
     */
    private void delPlanJob(List<Long> planIds) {
        /*删除调度任务*/
        LambdaQueryWrapper<SCHEDULE_JOB_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SCHEDULE_JOB_INF::getF_BUID, planIds)
                .eq(SCHEDULE_JOB_INF::getF_TASK_IDENTIFY, Constants.checkPlanIdentify);
        List<SCHEDULE_JOB_INF> list = scheduleJobInfService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            scheduleJobInfService.deleteBatch(list.stream().map(SCHEDULE_JOB_INF::getF_SJOB).collect(Collectors.toList()));
        }
    }

    @Override
    public List<INSPECTION_PLAN_INF_DTO> getList(INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        List<INSPECTION_PLAN_INF_DTO> list = baseMapper.getList(inspectionPlanInfVo);
        List<INSPECTION_PLAN_INF_DTO> arrayList = new ArrayList<>();
        LinkedHashMap<Long, List<INSPECTION_PLAN_INF_DTO>> collect = list.stream().collect(Collectors.groupingBy(INSPECTION_PLAN_INF_DTO::getF_MFPS,
                LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<Long, List<INSPECTION_PLAN_INF_DTO>> stringListEntry : collect.entrySet()) {
            LinkedHashMap<Long, List<INSPECTION_PLAN_INF_DTO>> map = stringListEntry.getValue().stream().collect(Collectors.groupingBy(INSPECTION_PLAN_INF_DTO::getF_MFND,
                    LinkedHashMap::new, Collectors.toList()));
            for (Map.Entry<Long, List<INSPECTION_PLAN_INF_DTO>> mfnd : map.entrySet()) {
                arrayList.addAll(mfnd.getValue());
            }
        }
        return arrayList;
    }

    @Override
    public long getTotal(INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        return baseMapper.getTotal(inspectionPlanInfVo);
    }

    @Override
    public void publish(SerialDebuggingVO serialDebuggingVO) {
        /*硬件id+ 对应的命令名称  + */
        /*获取对应的量具解析配置*/
        GAUGE_DEVICE gaugeDevice = gaugeDeviceService.getById(serialDebuggingVO.getGaugeDeviceId());
        if (gaugeDevice == null)
            throw new BusinessException(DataManagementExceptionEnum.GAUGE_DEVICE_NOT_EXISTS);
        GAUGE_FORMAT gaugeFormat = gaugeFormatService.getById(gaugeDevice.getF_GAFO());
        if (gaugeFormat == null)
            throw new BusinessException(DataManagementExceptionEnum.GAUGE_FORMAT_NOT_EXISTS);
        GAUGE_CONNECTION gaugeConnection = gaugeConnectionService.getById(gaugeDevice.getF_GICP());
        if (gaugeConnection == null)
            throw new BusinessException(DataManagementExceptionEnum.GAUGE_CONNECTION_NOT_EXISTS);
        GAUGE_AGENT gaugeAgent = gaugeAgentService.getById(gaugeConnection.getF_GAAG());
        if (gaugeAgent == null)
            throw new BusinessException(DataManagementExceptionEnum.GAUGE_HARDWARE_ID_NOT_EXISTS);
        if (StringUtils.isEmpty(gaugeFormat.getF_ADVANCED())) {
            return;
        }
        GAUGE_FORMAT_ADVANCED_DTO gaugeFormatAdvancedDto =
                JSONObject.parseObject(gaugeFormat.getF_ADVANCED(), GAUGE_FORMAT_ADVANCED_DTO.class);
        String channel = gaugeAgent.getF_HARDWARE()
                + "." + "COMMAND" + "." + gaugeFormatAdvancedDto.getMeasureRead()
                + "." + serialDebuggingVO.getSerialPort();
        /*UPDATE*/
        redisService.publish(channel, gaugeFormatAdvancedDto.getMeasureRead());
    }

    @Override
    public Double getDatabaseHistoricalVal(DataValConfigDTO dataValConfigDTO) {
        SubgroupFilterVO subgroupFilterVO = new SubgroupFilterVO();

        /*测试编号单独判断*/
        AtomicReference<String> testNum = new AtomicReference<>();
        dataValConfigDTO.getMap().forEach((k, v) -> {
            Object o = v.get("id");
            if (o == null || StringUtils.isEmpty(o.toString())) return;
            String id = o.toString();
            switch (DataValTypeEnum.getType(Integer.valueOf(k))) {
                case STAFF:
                    subgroupFilterVO.setF_CRUE(Long.valueOf(id));
                    break;
                case PRCS_DAT:
                    subgroupFilterVO.setPrcsList(Collections.singletonList(id));
                    break;
                case PART_DAT:
                    subgroupFilterVO.setPartList(Collections.singletonList(id));
                    break;
                case LOT:
                    subgroupFilterVO.setLotList(Collections.singletonList(id));
                    break;
                case TEST_DAT:
                    subgroupFilterVO.setTestList(Collections.singletonList(id));
                    break;
                case TEST_NO:
                    testNum.set(id);
            }
        });

        /*检查缓存表数据是否满足时间条件*/
        SGRP_INF_A sgrpInfMin = sgrpInfAService.getCacheMaxMinTime(false, dataValConfigDTO.getDbType());
        Date endDate = DateUtils.getNowDate();
        Date startDate;
        if (dataValConfigDTO.getTimeType() == 0) {
            startDate = DateUtils.addSeconds(endDate, -dataValConfigDTO.getTimeNum());
        } else if (dataValConfigDTO.getTimeType() == 1) {
            startDate = DateUtils.addMinutes(endDate, -dataValConfigDTO.getTimeNum());
        } else if (dataValConfigDTO.getTimeType() == 2) {
            startDate = DateUtils.addHours(endDate, -dataValConfigDTO.getTimeNum());
        } else {
            startDate = DateUtils.addDays(endDate, -dataValConfigDTO.getTimeNum());
        }
        subgroupFilterVO.setEndDate(endDate);
        subgroupFilterVO.setStartDate(startDate);
        subgroupFilterVO.setDbType(InitConfig.getDriverType());
        List<SubgroupDataDTO> subgroupDataDTOList;
        /*判断开始时间是否在最小时间范围内 */
        if (sgrpInfMin != null && startDate.getTime() > sgrpInfMin.getF_SGTM().getTime()) {
            /*在范围内 查询缓存表*/
            subgroupDataDTOList = sgrpInfAService.getList(subgroupFilterVO);
        } else {
            /*查询主表*/
            subgroupDataDTOList = sgrpInfService.getFilterList(subgroupFilterVO);
        }

        if (CollectionUtils.isEmpty(subgroupDataDTOList)) {
            /*没有数据*/
            if (dataValConfigDTO.getNullReturn() != null) {
                return dataValConfigDTO.getNullReturn();
            } else {
                return null;
            }
        }

        subgroupDataDTOList = subgroupDataDTOList.stream()
                .sorted(Comparator.comparing(SubgroupDataDTO::getF_SGTM)
                        .thenComparing(SubgroupDataDTO::getF_SGRP).reversed())
                .collect(Collectors.toList());
        /*最后子组*/
        SubgroupDataDTO endSubgroupDataDTO = subgroupDataDTOList.get(0);
        SGRP_VAL_CHILD_DTO endValChildDto =
                JSONObject.parseObject(endSubgroupDataDTO.getTestData(), SGRP_VAL_CHILD_DTO.class);
        if (dataValConfigDTO.getStandardType() == 0) {
            List<SGRP_VAL_CHILD_DTO.Test> testList = endValChildDto.getTestList();
            if (StringUtils.isNotEmpty(testNum.get())) {
                List<SGRP_VAL_CHILD_DTO.Test> collect =
                        testList.stream().filter(t -> t.getTestNo().toString().equals(testNum.get()))
                                .collect(Collectors.toList());
                endValChildDto.setTestList(collect);
                return collect.get(0).getTestVal();
            } else {
                Integer max = testList.stream().map(SGRP_VAL_CHILD_DTO.Test::getTestNo).max(Integer::compareTo).orElse(0);
                List<SGRP_VAL_CHILD_DTO.Test> collect =
                        testList.stream().filter(t -> t.getTestNo().equals(max)).collect(Collectors.toList());
                return collect.get(0).getTestVal();
            }
        }


        List<SGRP_VAL_CHILD_DTO> list = new ArrayList<>();
        subgroupDataDTOList.forEach(subgroupDataDTO -> {
            SGRP_VAL_CHILD_DTO sgrpValChildDto =
                    JSONObject.parseObject(subgroupDataDTO.getTestData(), SGRP_VAL_CHILD_DTO.class);
            if (StringUtils.isNotEmpty(testNum.get())) {
                List<SGRP_VAL_CHILD_DTO.Test> testList = sgrpValChildDto.getTestList();
                List<SGRP_VAL_CHILD_DTO.Test> collect =
                        testList.stream().filter(t -> t.getTestNo().equals(Integer.parseInt(testNum.get()) - 1))
                                .collect(Collectors.toList());
                sgrpValChildDto.setTestList(collect);
            }
            sgrpValChildDto.setF_SGTM(subgroupDataDTO.getF_SGTM());
            list.add(sgrpValChildDto);
        });
        /*根据子组时间排序*/
        List<SGRP_VAL_CHILD_DTO> collect =
                list.stream().sorted(Comparator.comparing(SGRP_VAL_CHILD_DTO::getF_SGTM)).collect(Collectors.toList());
        List<SGRP_VAL_CHILD_DTO.Test> testList = new ArrayList<>();
        for (SGRP_VAL_CHILD_DTO sgrpValChildDto : collect) {
            testList.addAll(sgrpValChildDto.getTestList());
        }

        if (dataValConfigDTO.getIsDisposeEndSgrp()) {
            /*将特殊处理限制在最后输入的子组*/
            testList = endValChildDto.getTestList();
        }
        List<Double> doubles = testList.stream().map(SGRP_VAL_CHILD_DTO.Test::getTestVal).collect(Collectors.toList());
        return SpecialHandlingTypeEnum.calculate(SpecialHandlingTypeEnum.getType(dataValConfigDTO.getSpecialHandlingType()), doubles);
    }

    @Override
    public List<INSPECTION_PLAN_INF_DTO> getWorkBoardPlan(INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        long start = System.currentTimeMillis();
        final EMPL_INF_DTO sysUser = SecurityUtils.getLoginUser().getSysUser();
        List<Long> factoryIds = HIERARCHY_INF_DTO.getHierarchyInfIds(SecurityUtils.getLoginUser().getSysUser().getHierarchyInfDto());
        if (CollectionUtils.isEmpty(factoryIds)) return new ArrayList<>();
        inspectionPlanInfVo.setHierarchyInfIds(factoryIds);
        List<INSPECTION_PLAN_INF_DTO> workBoardPlan = baseMapper.getWorkBoardPlan(inspectionPlanInfVo);
        workBoardPlan.forEach(inspectionPlanInfDto -> {
            /*获取临时保存子组*/
            List<SubgroupDataVO> tempSaveInfo = sgrpInfService.getTempSaveInfo(inspectionPlanInfDto.getF_PLAN());
            tempSaveInfo.forEach(subgroupDataVO -> {
                PART_INF partInf = partInfService.getById(subgroupDataVO.getF_PART());
                subgroupDataVO.setPartName(partInf.getF_NAME());
                PART_REV partRev = partRevService.getById(subgroupDataVO.getF_REV());
                subgroupDataVO.setPtrvName(partRev.getF_NAME());
                PRCS_INF prcsInf = prcsInfService.getById(subgroupDataVO.getF_PRCS());
                subgroupDataVO.setPrcsName(prcsInf.getF_NAME());
            });
            inspectionPlanInfDto.setTempSaveInfoList(tempSaveInfo);
            /*获取待完成子计划子组*/
            Integer waitDealNum = sgrpInfService.getWaitDealInfo(inspectionPlanInfDto.getF_PLAN());
            inspectionPlanInfDto.setWaitDealNum(waitDealNum);
            inspectionPlanInfDto.setDbType(inspectionPlanInfVo.getDbType());
            /*获取检查计划定时执行记录最新数据*/
            INSPECTION_PLAN_RECORD_INF inspectionPlanRecordInf = inspectionPlanRecordInfMapper.getLatestRecord(inspectionPlanInfDto);
            if (inspectionPlanRecordInf != null) {
                inspectionPlanInfDto.setInspectionPlanRecordInf(inspectionPlanRecordInf);
            }
        });
        //数据去重
        workBoardPlan= ListUtils.distinctByKey(workBoardPlan, INSPECTION_PLAN_INF_DTO::getF_PLAN);
        long end = System.currentTimeMillis();
        log.info("获取缓存待处理耗时：" + (end - start) + "ms");
        Set<INSPECTION_PLAN_INF_DTO> set = new LinkedHashSet<>();
        List<INSPECTION_PLAN_INF_DTO> list = workBoardPlan.stream()
                .sorted(new Comparator<INSPECTION_PLAN_INF_DTO>() {
                    @Override
                    public int compare(INSPECTION_PLAN_INF_DTO o1, INSPECTION_PLAN_INF_DTO o2) {
                        String s1 = o1.getF_NAME();
                        String s2 = o2.getF_NAME();
                        //该排序为正序排序，如果倒序排序则将compare中的s2和s1互换位置
                        return Collator.getInstance(Locale.UK).compare(s1, s2);
                    }
                }).collect(Collectors.toList());
        set.addAll(list);
        end = System.currentTimeMillis();
        log.info("获取工作看板计划耗时：" + (end - start) + "ms");
        return list;
    }

    @Override
    public List<SGRP_INF_UNFINISHED> getUnfinishedList(INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        List<SGRP_INF_UNFINISHED> sgrpInfUnfinishedList = baseMapper.getUnfinishedList(inspectionPlanInfVo);
        if (CollectionUtils.isEmpty(sgrpInfUnfinishedList)) {
            return sgrpInfUnfinishedList;
        }
        SubgroupDataSelectionDTO subgroupDataSelectionDTO = new SubgroupDataSelectionDTO();
        Set<String> collect = sgrpInfUnfinishedList.stream().map(SGRP_INF_UNFINISHED::getF_SAMPLE_ID).collect(Collectors.toSet());
        subgroupDataSelectionDTO.setSampleIdList(new ArrayList<>(collect));
        List<SubgroupDataDTO> subgroupDataDTOList = sgrpInfService.getSubgroupDataDTOList(subgroupDataSelectionDTO);
        Map<String, List<SubgroupDataDTO>> map = subgroupDataDTOList.stream().collect(Collectors.groupingBy(SubgroupDataDTO::getF_SAMPLE_ID));
        sgrpInfUnfinishedList.forEach(sgrpInfUnfinished -> {
            if (map.get(sgrpInfUnfinished.getF_SAMPLE_ID()) != null) {
                List<SubgroupDataDTO> dataDTOS = map.get(sgrpInfUnfinished.getF_SAMPLE_ID());
                sgrpInfUnfinished.setSubgroupDataDTOList(dataDTOS);
                sgrpInfUnfinished.setPartName(dataDTOS.get(0).getPartName());
                sgrpInfUnfinished.setPrcsName(dataDTOS.get(0).getPrcsName());
                sgrpInfUnfinished.setPtrvName(dataDTOS.get(0).getPtrvName());
            }
        });
        return sgrpInfUnfinishedList;
    }

    @Override
    public List<INSPECTION_PLAN_INF> getDynamicInspectionPlan(INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        LambdaQueryWrapper<INSPECTION_PLAN_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(INSPECTION_PLAN_INF::getF_TYPE, 1).eq(INSPECTION_PLAN_INF::getF_DEL, DelFlagEnum.USE.getType());
        return baseMapper.selectList(queryWrapper);
    }
}




