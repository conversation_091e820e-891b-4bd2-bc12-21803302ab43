package com.yingfei.dataManagement.service;

import com.yingfei.entity.domain.PART_REV;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.vo.PART_INF_VO;
import com.yingfei.entity.vo.PART_REV_VO;

import java.util.List;

/**
* 
* @description 针对表【PART_REV(储存产品版本信息表)】的数据库操作Service
* @createDate 2024-05-11 16:41:13
*/
public interface PART_REVService extends IService<PART_REV> {

    /**
     * 根据产品信息添加产品版本
     * @param partInfVo
     */
    void saveByPart(PART_INF_VO partInfVo);

    List<PART_REV> findByPartId(Long id);

    void upgrade(PART_REV_VO partRevVo);

    void editByPart(PART_INF_VO partInfVo);

    void add(PART_REV partRev);
}
