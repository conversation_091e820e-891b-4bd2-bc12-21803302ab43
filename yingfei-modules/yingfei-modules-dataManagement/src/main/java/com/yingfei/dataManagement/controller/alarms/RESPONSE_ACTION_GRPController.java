package com.yingfei.dataManagement.controller.alarms;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.RESPONSE_ACTION_GRPService;
import com.yingfei.entity.domain.RESPONSE_ACTION_GRP;
import com.yingfei.entity.dto.RESPONSE_ACTION_GRP_DTO;
import com.yingfei.entity.vo.RESPONSE_ACTION_GRP_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "改善措施组信息API")
@RestController
@RequestMapping("/response_action_grp")
public class RESPONSE_ACTION_GRPController extends BaseController {
    
    @Resource
    private RESPONSE_ACTION_GRPService responseActionGrpService;

    /**
     * 获取改善措施组信息列表
     */
    @ApiOperation("获取改善措施组信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody RESPONSE_ACTION_GRP_VO responseActionGrpVo) {
        List<RESPONSE_ACTION_GRP_DTO> list = responseActionGrpService.getList(responseActionGrpVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(responseActionGrpService.getTotal(responseActionGrpVo));
        return dataTable;
    }

    /**
     * 新增改善措施组信息
     */
    @CreateUpdateBy
    @RequiresPermissions("dataManagement:responseActionGrp:add")
    @Log(title = "改善措施组管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增改善措施组信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody RESPONSE_ACTION_GRP_VO responseActionGrpVo) {
        responseActionGrpService.checkParam(responseActionGrpVo);
        responseActionGrpService.add(responseActionGrpVo);
        return R.ok();
    }

    /**
     * 修改改善措施组信息
     */
    @RequiresPermissions("dataManagement:responseActionGrp:edit")
    @Log(title = "改善措施组管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改改善措施组信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody RESPONSE_ACTION_GRP_VO responseActionGrpVo) {
        responseActionGrpService.checkParam(responseActionGrpVo);
        RESPONSE_ACTION_GRP responseActionGrp = new RESPONSE_ACTION_GRP();
        BeanUtils.copyPropertiesIgnoreNull(responseActionGrpVo, responseActionGrp);
        responseActionGrpService.updateById(responseActionGrp);
        return R.ok();
    }

    /**
     * 批量删除改善措施组信息
     */
    @RequiresPermissions("dataManagement:responseActionGrp:remove")
    @Log(title = "改善措施组管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除改善措施组信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        responseActionGrpService.del(ids);
        return R.ok();
    }
}
