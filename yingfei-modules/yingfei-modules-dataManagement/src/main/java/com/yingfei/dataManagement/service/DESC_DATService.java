package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.DESC_DAT;
import com.yingfei.entity.dto.DESC_DAT_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.vo.DESC_DAT_VO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DESC_DAT(储存自定义描述符信息表)】的数据库操作Service
 * @createDate 2024-05-08 16:27:06
 */
public interface DESC_DATService extends IService<DESC_DAT>, BaseService<DESC_DAT_VO, DESC_DAT_DTO> {
    DESC_DAT addDescDat(DESC_DAT_VO descDatVo);

    List<DESC_DAT_DTO> getSearchCondition(Integer isInclude, List<Long> descList);

    DESC_DAT_DTO info(Long id);

    OperationAssociationDTO getOperationAssociation(List<Long> ids);
}
