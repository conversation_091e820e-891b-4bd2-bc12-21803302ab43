package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.service.chart.CalendarService;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.CalendarDTO;
import com.yingfei.entity.dto.chart.LinearRegressionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@Api(tags = "图表:日历图API")
@RequestMapping("/calendar")
public class CalendarController {

    @Resource
    private CalendarService calendarService;

    @PostMapping("/info")
    @ApiOperation("日历图")
    public R<?> getInfo(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO){
        CalendarDTO CalendarDTO = calendarService.getInfo(subgroupDataSelectionDTO);
        return R.ok(CalendarDTO);
    }
}
