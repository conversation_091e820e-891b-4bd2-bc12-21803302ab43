package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.INSPECTION_TYPE_DAT;
import com.yingfei.entity.dto.INSPECTION_TYPE_DAT_DTO;
import com.yingfei.entity.vo.INSPECTION_TYPE_DAT_VO;

public interface INSPECTION_TYPE_DATService extends IService<INSPECTION_TYPE_DAT>, BaseService<INSPECTION_TYPE_DAT_VO, INSPECTION_TYPE_DAT_DTO> {
}
