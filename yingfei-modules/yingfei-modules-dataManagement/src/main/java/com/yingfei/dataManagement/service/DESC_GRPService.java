package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.DESC_GRP;
import com.yingfei.entity.dto.DESC_GRP_DTO;
import com.yingfei.entity.vo.DESC_GRP_VO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DESC_GRP(储存自定义描述符组信息表)】的数据库操作Service
 * @createDate 2024-05-08 16:27:12
 */
public interface DESC_GRPService extends IService<DESC_GRP>, BaseService<DESC_GRP_VO, DESC_GRP_DTO> {

    DESC_GRP_DTO getInfo(Long id, List<Long> childIds);
}
