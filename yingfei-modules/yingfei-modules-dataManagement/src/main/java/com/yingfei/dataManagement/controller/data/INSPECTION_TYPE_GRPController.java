package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.INSPECTION_TYPE_GRPService;
import com.yingfei.entity.domain.INSPECTION_TYPE_GRP;
import com.yingfei.entity.dto.INSPECTION_TYPE_GRP_DTO;
import com.yingfei.entity.vo.INSPECTION_TYPE_GRP_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "检验类型组信息API")
@RestController
@RequestMapping("/inspection_type_grp")
public class INSPECTION_TYPE_GRPController extends BaseController {
    
    @Resource
    private INSPECTION_TYPE_GRPService inspectionTypeGrpService;

    /**
     * 获取检验类型组信息列表
     */
    @ApiOperation("获取检验类型组信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody INSPECTION_TYPE_GRP_VO inspectionTypeGrpVo) {
        List<INSPECTION_TYPE_GRP_DTO> list = inspectionTypeGrpService.getList(inspectionTypeGrpVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(inspectionTypeGrpService.getTotal(inspectionTypeGrpVo));
        return dataTable;
    }

    /**
     * 新增检验类型组信息
     */
    @CreateUpdateBy
    @NotResubmit
    @Log(title = "检验类型组管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增检验类型组信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody INSPECTION_TYPE_GRP_VO inspectionTypeGrpVo) {
        inspectionTypeGrpService.checkParam(inspectionTypeGrpVo);
        inspectionTypeGrpService.add(inspectionTypeGrpVo);
        return R.ok();
    }

    /**
     * 修改检验类型组信息
     */
    @Log(title = "检验类型组管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改检验类型组信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody INSPECTION_TYPE_GRP_VO inspectionTypeGrpVo) {
        inspectionTypeGrpService.checkParam(inspectionTypeGrpVo);
        INSPECTION_TYPE_GRP inspectionTypeGrp = new INSPECTION_TYPE_GRP();
        BeanUtils.copyPropertiesIgnoreNull(inspectionTypeGrpVo, inspectionTypeGrp);
        inspectionTypeGrpService.updateById(inspectionTypeGrp);
        return R.ok();
    }

    /**
     * 批量删除检验类型组信息
     */
    @Log(title = "检验类型组管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除检验类型组信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        inspectionTypeGrpService.del(ids);
        return R.ok();
    }

}
