package com.yingfei.dataManagement.controller.basic;

import com.alibaba.fastjson2.JSONArray;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.datascope.annotation.DataScope;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.PARAMETER_EMPL_LINKService;
import com.yingfei.dataManagement.service.PARAMETER_SET_INFService;
import com.yingfei.entity.domain.PARAMETER_SET_INF;
import com.yingfei.entity.dto.PARAMETER_CHILD_DTO;
import com.yingfei.entity.dto.PARAMETER_SET_INF_DTO;
import com.yingfei.entity.vo.PARAMETER_SET_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "参数集信息API")
@RestController
@RequestMapping("/parameter_set_inf")
public class PARAMETER_SET_INFController extends BaseController {
    
    @Resource
    private PARAMETER_SET_INFService parameterSetInfService;
    @Resource
    private PARAMETER_EMPL_LINKService parameterEmplLinkService;

    /**
     * 获取参数集信息列表
     */
    @DataScope
    @ApiOperation("获取参数集信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody PARAMETER_SET_INF_VO parameterSetInfVo) {
        List<PARAMETER_SET_INF_DTO> list = parameterSetInfService.getList(parameterSetInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(parameterSetInfService.getTotal(parameterSetInfVo));
        return dataTable;
    }

    /**
     * 获取参数集信息
     */
    @ApiOperation("获取参数集信息")
    @PostMapping("/info/{id}")
    public R<?> info(@PathVariable Long id){
        PARAMETER_SET_INF_DTO parameterSetInfDto = parameterSetInfService.info(id);
        return R.ok(parameterSetInfDto);
    }

    /**
     * 新增参数集信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:parameterSetInf:add")
    @Log(title = "参数集管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增参数集信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody PARAMETER_SET_INF_VO parameterSetInfVo) {
        parameterSetInfService.checkParam(parameterSetInfVo);
        parameterSetInfService.add(parameterSetInfVo);
        return R.ok();
    }

    /**
     * 修改参数集信息
     */
    @RequiresPermissions("dataManagement:parameterSetInf:edit")
    @Log(title = "参数集管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改参数集信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody PARAMETER_SET_INF_VO parameterSetInfVo) {
        parameterSetInfService.checkParam(parameterSetInfVo);
        PARAMETER_SET_INF parameterSetInf = new PARAMETER_SET_INF();
        BeanUtils.copyPropertiesIgnoreNull(parameterSetInfVo, parameterSetInf);
        if (CollectionUtils.isNotEmpty(parameterSetInfVo.getParameterChildDtoList())) {
            JSONArray jsonArray = new JSONArray(parameterSetInfVo.getParameterChildDtoList());
            parameterSetInf.setF_DATA_SET(jsonArray.toJSONString());
        }
        if (CollectionUtils.isNotEmpty(parameterSetInfVo.getParameterEmplLinkList())) {
            parameterEmplLinkService.add(parameterSetInfVo.getParameterEmplLinkList(), parameterSetInf.getF_PRST());
        }
        parameterSetInfService.updateById(parameterSetInf);
        return R.ok();
    }

    /**
     * 批量删除参数集信息
     */
    @RequiresPermissions("dataManagement:parameterSetInf:remove")
    @Log(title = "参数集管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除参数集信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        parameterSetInfService.del(ids);
        return R.ok();
    }

    /**
     * 获取对应的组项信息
     */
    @ApiOperation("获取对应的组项信息")
    @PostMapping("/getParameter")
    public R<?> getParameter(@RequestBody PARAMETER_CHILD_DTO parameterChildDto) {
        List<Object> list = parameterSetInfService.getParameter(parameterChildDto);
        return R.ok(list);
    }

}
