package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.dataManagement.service.SGRP_INFService;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.vo.SGRP_INF_UNFINISHED_VO;
import com.yingfei.entity.vo.SubgroupDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "子组信息API")
@RestController
@RequestMapping("/sgrp_inf")
public class SGRP_INFController extends BaseController {

    @Resource
    private SGRP_INFService sgrpInfService;

    /**
     * 获取子组信息列表
     */
    @ApiOperation("获取子组信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody SubgroupDataVO subgroupDataVO) {
        List<SubgroupDataDTO> list = sgrpInfService.getList(subgroupDataVO);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(sgrpInfService.getTotal(subgroupDataVO));
        return dataTable;
    }

    /**
     * 新增子组信息
     */
    @Log(title = "子组管理", businessType = BusinessType.INSERT)
    @NotResubmit
    @ApiOperation("新增子组信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody List<SubgroupDataVO> subgroupDataVOList) {
        sgrpInfService.add(subgroupDataVOList);
        return R.ok();
    }

    /**
     * 修改子组信息
     */
    @Log(title = "子组管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改子组信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody SubgroupDataVO subgroupDataVO) {
        sgrpInfService.checkParam(subgroupDataVO);
        sgrpInfService.edit(subgroupDataVO);
        return R.ok();
    }

    /**
     * 批量修改子组信息
     */
    @Log(title = "子组管理", businessType = BusinessType.UPDATE)
    @ApiOperation("批量修改子组信息")
    @PostMapping("/batchEdit")
    public R<?> batchEdit(@RequestBody List<SubgroupDataVO> subgroupDataVOList) {
        subgroupDataVOList.forEach(subgroupDataVO -> {
            sgrpInfService.checkParam(subgroupDataVO);
            sgrpInfService.edit(subgroupDataVO);
        });
        return R.ok();
    }

    /**
     * 子组失效
     */
    @ApiOperation("子组失效")
    @PostMapping("/disable/{ids}")
    public R<?> disable(@PathVariable List<Long> ids) {
        sgrpInfService.disable(ids);
        return R.ok();
    }


    /**
     * 批量删除子组信息
     */
    @Log(title = "子组管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除子组信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        sgrpInfService.del(ids);
        return R.ok();
    }

    /**
     * 临时保存子组信息
     */
    @ApiOperation("临时保存子组信息")
    @PostMapping("/tempSave/{planId}")
    public R<?> tempSave(@PathVariable String planId, @RequestBody List<SubgroupDataVO> subgroupDataVOList) {
        sgrpInfService.tempSave(planId, subgroupDataVOList);
        return R.ok();
    }

    /**
     * 获取临时保存子组信息
     */
    @ApiOperation("获取临时保存子组信息")
    @PostMapping("/getTempSaveInfo/{planId}")
    public R<?> getTempSaveInfo(@PathVariable Long planId) {
        List<SubgroupDataVO> subgroupDataVOList = sgrpInfService.getTempSaveInfo(planId);
        return R.ok(subgroupDataVOList);
    }

    /**
     * 删除临时保存子组信息
     */
    @ApiOperation("删除临时保存子组信息")
    @PostMapping("/delTempSaveInfo/{planId}")
    public R<?> delTempSaveInfo(@PathVariable Long planId, @RequestBody SubgroupDataVO subgroupDataVO) {
        sgrpInfService.delTempSaveInfo(planId, subgroupDataVO);
        return R.ok();
    }


    /**
     * 删除待处理计划
     */
    @ApiOperation("删除待处理计划")
    @PostMapping("/delUnfinished")
    public R<?> delUnfinished(@RequestBody SGRP_INF_UNFINISHED_VO vo) {
        sgrpInfService.delUnfinished(vo);
        return R.ok();
    }


    /**
     * 根据子组id获取子组信息
     */
    @ApiOperation("根据子组id获取子组信息")
    @PostMapping("/findById/{id}")
    public R<?> findById(@PathVariable Long id) {
        List<SubgroupDataDTO> list = sgrpInfService.findById(id);
        return R.ok(list);
    }

    /**
     * 记录正在处理的缓存子组
     */
    @ApiOperation("记录正在处理的缓存子组")
    @PostMapping("/processingTemp/{planId}")
    public R<?> processingTemp(@PathVariable Long planId, @RequestBody SubgroupDataVO subgroupDataVO) {
        boolean b = sgrpInfService.processingTemp(planId, subgroupDataVO);
        return R.ok(b);
    }

    /**
     * 取消正在处理的缓存子组
     */
    @ApiOperation("取消正在处理的缓存子组")
    @PostMapping("/cancelProcessingTemp/{planId}")
    public R<?> cancelProcessingTemp(@PathVariable Long planId, @RequestBody SubgroupDataVO subgroupDataVO) {
        sgrpInfService.cancelProcessingTemp(planId, subgroupDataVO);
        return R.ok();
    }

    /**
     * 记录正在处理的待处理子组
     */
    @ApiOperation("记录正在处理的待处理子组")
    @GetMapping("/processingPending")
    public R<?> processingPending(Long planId, String childId) {
        boolean b = sgrpInfService.processingPending(planId, childId);
        return R.ok(b);
    }

    /**
     * 取消正在处理的缓存子组
     */
    @ApiOperation("取消正在处理的缓存子组")
    @GetMapping("/cancelProcessingPending")
    public R<?> cancelProcessingPending(Long planId, String childId) {
        sgrpInfService.cancelProcessingPending(planId, childId);
        return R.ok();
    }

    /**
     * 获取正在处理的缓存子组
     */
    @ApiOperation("获取正在处理的缓存子组")
    @GetMapping("/getProcessingPending")
    public R<?> getProcessingPending(Long planId) {
        List<String> list = sgrpInfService.getProcessingPending(planId);
        return R.ok(list);
    }


    /**
     * 外部调用:获取对应子组id信息(根据测试组装)
     */
    @ApiOperation("外部调用:获取对应子组id信息(根据测试组装)")
    @GetMapping("/findBySgrpId")
    public R<?> findBySgrpId(Long sgrpId, Integer flag) {
        List<SubgroupDataDTO> list = sgrpInfService.findBySgrpId(sgrpId,flag);
        return R.ok(list);
    }
}
