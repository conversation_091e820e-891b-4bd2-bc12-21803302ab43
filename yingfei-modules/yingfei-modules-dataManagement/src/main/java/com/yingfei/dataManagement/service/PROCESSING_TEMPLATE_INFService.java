package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.PROCESSING_TEMPLATE_INF;
import com.yingfei.entity.dto.PROCESSING_TEMPLATE_INF_DTO;
import com.yingfei.entity.vo.PROCESSING_TEMPLATE_INF_VO;

/**
* <AUTHOR>
* @description 针对表【PROCESSING_TEMPLATE_INF(储存数据标准化信息表)】的数据库操作Service
* @createDate 2024-05-08 16:28:14
*/
public interface PROCESSING_TEMPLATE_INFService extends IService<PROCESSING_TEMPLATE_INF>, BaseService<PROCESSING_TEMPLATE_INF_VO, PROCESSING_TEMPLATE_INF_DTO> {

}
