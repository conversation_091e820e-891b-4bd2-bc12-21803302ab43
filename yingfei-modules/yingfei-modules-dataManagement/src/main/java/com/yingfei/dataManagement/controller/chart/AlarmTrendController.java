package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.dataManagement.service.chart.AlarmTrendService;
import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "图表:报警趋势API")
@RequestMapping("/alarmTrend")
public class AlarmTrendController {

    @Resource
    private AlarmTrendService alarmTrendService;


    @PostMapping("/getAlarmTrend")
    @ApiOperation("获取报警趋势详情")
    public R<?> getAlarmTrend(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {

        List<STREAM_TREND_INF_DTO> list = alarmTrendService.getAlarmTrend(subgroupDataSelectionDTO);
        return R.ok(list);
    }
    @PostMapping("/getAlarmTrendList")
    @ApiOperation("获取报警趋势详情")
    public R<?> getAlarmTrendList(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {

            subgroupDataSelectionDTO.setIsRange(YesOrNoEnum.YES.getType());

        List<List<STREAM_TREND_INF_DTO>> list = alarmTrendService.getAlarmTrendList(subgroupDataSelectionDTO);
        return R.ok(list);
    }

}
