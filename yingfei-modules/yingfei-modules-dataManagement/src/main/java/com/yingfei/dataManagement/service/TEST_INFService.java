package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.TEST_INF;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.TEST_INF_DTO;
import com.yingfei.entity.vo.TEST_INF_VO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【TEST_INF(测试信息表)】的数据库操作Service
 * @createDate 2024-05-08 16:29:04
 */
public interface TEST_INFService extends IService<TEST_INF>, BaseService<TEST_INF_VO, TEST_INF_DTO> {

    void recovery(Long id);

    void cover(Long id);

    TEST_INF recycleBin(TEST_INF_VO testInfVo);

    Map<String, String> importTest(List<TEST_INF_VO> testInfVoList);

    List<TEST_INF> getSearchCondition(Integer isInclude, List<Long> dataList, List<Long> hierIds);

    HIERARCHY_INF_DTO getBelongTestList(TEST_INF_VO testInfVo);

    void batchEdit(TEST_INF_VO testInfVo);

    TEST_INF_DTO getInfo(Long id);

    OperationAssociationDTO getOperationAssociation(List<Long> ids);
}
