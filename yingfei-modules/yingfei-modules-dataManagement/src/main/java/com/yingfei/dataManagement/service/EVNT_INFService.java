package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.EVNT_INF;
import com.yingfei.entity.dto.EVNT_INF_DTO;
import com.yingfei.entity.enums.ParetoAnalyseTypeEnum;
import com.yingfei.entity.vo.EVNT_INF_VO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【EVNT_INF(报警信息表)】的数据库操作Service
* @createDate 2024-05-08 16:27:26
*/
public interface EVNT_INFService extends IService<EVNT_INF>, BaseService<EVNT_INF_VO, EVNT_INF_DTO> {

    EVNT_INF_DTO getInfo(Long id);

    void delCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum);

    void exceptionCause(EVNT_INF_VO evntInfVo);

    List<EVNT_INF> findBySgrp(Long fSgrp);

    List<EVNT_INF_DTO> findByTagList(List<EVNT_INF> evntInfList);

    List<EVNT_INF> getCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum);
}
