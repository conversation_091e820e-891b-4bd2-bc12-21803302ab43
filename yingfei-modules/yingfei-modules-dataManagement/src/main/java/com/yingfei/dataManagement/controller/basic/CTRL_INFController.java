package com.yingfei.dataManagement.controller.basic;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.datascope.annotation.DataScope;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.CTRL_INFService;
import com.yingfei.entity.domain.CTRL_INF;
import com.yingfei.entity.dto.CTRL_INF_DTO;
import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.PARAMETER_SET_INF_DTO;
import com.yingfei.entity.dto.chart.ControlChartDTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.vo.CTRL_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "控制限信息API")
@RestController
@RequestMapping("/ctrl_inf")
public class CTRL_INFController extends BaseController {

    @Resource
    private CTRL_INFService ctrlInfService;

    /**
     * 获取控制限信息列表
     */
    @DataScope
    @ApiOperation("获取控制限信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody CTRL_INF_VO ctrlInfVo) {
        List<CTRL_INF_DTO> list = ctrlInfService.getList(ctrlInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(ctrlInfService.getTotal(ctrlInfVo));
        return dataTable;
    }

    /**
     * 新增控制限信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:ctrlInf:add")
    @Log(title = "控制限管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增控制限信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody CTRL_INF_VO ctrlInfVo) {
        ctrlInfService.checkParam(ctrlInfVo);
        ctrlInfService.add(ctrlInfVo);
        return R.ok();
    }

    /**
     * 修改控制限信息
     */
    @RequiresPermissions("dataManagement:ctrlInf:edit")
    @Log(title = "控制限管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改控制限信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody CTRL_INF_VO ctrlInfVo) {
        ctrlInfService.checkParam(ctrlInfVo);
        CTRL_INF ctrlInf = new CTRL_INF();
        BeanUtils.copyPropertiesIgnoreNull(ctrlInfVo, ctrlInf);
        ctrlInfService.edit(ctrlInf);
        return R.ok();
    }

    /**
     * 批量删除控制限信息
     */
    @RequiresPermissions("dataManagement:ctrlInf:remove")
    @Log(title = "控制限管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除控制限信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        ctrlInfService.del(ids);
        return R.ok();
    }

    @ApiOperation("获取图表类型")
    @GetMapping("getChartType")
    public R<?> getChartType() {
        return R.ok(ControlChartTypeEnum.getMap());
    }

    /**
     * 批量添加控制限生成
     */
    @ApiOperation("批量添加控制限生成")
    @PostMapping("/ctrlGenerate")
    public R<?> ctrlGenerate(@RequestBody CTRL_INF_VO ctrlInfVo) {
        List<ControlChartDTO> controlChartDTOList = ctrlInfService.ctrlGenerate(ctrlInfVo);
        return R.ok(controlChartDTOList);
    }

    /**
     * 批量新增控制限
     */
    @ApiOperation("批量新增控制限")
    @PostMapping("/batchAdd")
    public R<?> batchAdd(@RequestBody List<CTRL_INF_VO> ctrlInfVoList) {
        ctrlInfService.batchAdd(ctrlInfVoList);
        return R.ok();
    }

    /**
     * 控制限反推
     */
    @ApiOperation("控制限反推")
    @PostMapping("/backstepping/{type}")
    public R<?> backstepping(@RequestBody ControlLimitDTO controlLimitDTO, @PathVariable Integer type) {
        controlLimitDTO = ctrlInfService.backstepping(controlLimitDTO, type);
        return R.ok(controlLimitDTO);
    }

    /**
     * 使用历史值计算控制限
     */
    @ApiOperation("使用历史值计算控制限")
    @PostMapping("/calculateControlLimit")
    public R<?> calculateControlLimit(@RequestBody CTRL_INF_VO ctrlInfVo) {
        ctrlInfService.calculateControlLimit(ctrlInfVo);
        return R.ok(ctrlInfVo);
    }

    /**
     * 批量新增计算控制限
     */
    @ApiOperation("批量新增计算控制限")
    @PostMapping("/batchCalculateControlLimit")
    public R<?> batchCalculateControlLimit(@RequestBody PARAMETER_SET_INF_DTO parameterSetInfDto) {
        List<ControlChartDTO> controlChartDTOList = ctrlInfService.batchCalculateControlLimit(parameterSetInfDto);
        return R.ok(controlChartDTOList);
    }
    /**
     * 根据产品、过程、测试和版本查询历史图表类型
     */
    @ApiOperation("根据产品、过程、测试和版本查询历史图表类型")
    @PostMapping("/queryHistoricalChartType")
    public R<?> queryHistoricalChartType(@RequestBody CTRL_INF_VO ctrlInfVo ) {
        Integer chartType = ctrlInfService.queryHistoricalChartType(ctrlInfVo);
        return R.ok(chartType);
    }


}
