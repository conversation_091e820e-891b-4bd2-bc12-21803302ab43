package com.yingfei.dataManagement.controller.alarms;

import com.alibaba.fastjson2.JSONArray;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.ACTIVED_RULE_TEMPLATE_INFService;
import com.yingfei.entity.domain.ACTIVED_RULE_TEMPLATE_INF;
import com.yingfei.entity.dto.ACTIVED_RULE_TEMPLATE_INF_DTO;
import com.yingfei.entity.vo.ACTIVED_RULE_TEMPLATE_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "报警规则模板信息API")
@RestController
@RequestMapping("/actived_rule_template_inf")
public class ACTIVED_RULE_TEMPLATE_INFController extends BaseController {

    @Resource
    private ACTIVED_RULE_TEMPLATE_INFService activedRuleTemplateInfService;

    /**
     * 获取报警规则模板信息列表
     */
    @ApiOperation("获取报警规则模板信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody ACTIVED_RULE_TEMPLATE_INF_VO activedRuleTemplateInfVo) {
        List<ACTIVED_RULE_TEMPLATE_INF_DTO> list = activedRuleTemplateInfService.getList(activedRuleTemplateInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(activedRuleTemplateInfService.getTotal(activedRuleTemplateInfVo));
        return dataTable;
    }

    /**
     * 获取报警规则模板信息详情
     */
    @ApiOperation("获取报警规则模板信息详情")
    @PostMapping("/info/{id}")
    public R<?> info(@PathVariable Long id) {
        ACTIVED_RULE_TEMPLATE_INF_DTO activedRuleTemplateInfDto = activedRuleTemplateInfService.info(id);
        return R.ok(activedRuleTemplateInfDto);
    }

    /**
     * 新增报警规则模板信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:activedRuleTemplateInf:add")
    @Log(title = "报警规则模板管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增报警规则模板信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody ACTIVED_RULE_TEMPLATE_INF_VO activedRuleTemplateInfVo) {
        activedRuleTemplateInfService.checkParam(activedRuleTemplateInfVo);
        activedRuleTemplateInfService.add(activedRuleTemplateInfVo);
        return R.ok();
    }

    /**
     * 修改报警规则模板信息
     */
    @RequiresPermissions("dataManagement:activedRuleTemplateInf:edit")
    @Log(title = "报警规则模板管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改报警规则模板信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody ACTIVED_RULE_TEMPLATE_INF_VO activedRuleTemplateInfVo) {
        activedRuleTemplateInfService.checkParam(activedRuleTemplateInfVo);
        ACTIVED_RULE_TEMPLATE_INF activedRuleTemplateInf = new ACTIVED_RULE_TEMPLATE_INF();
        BeanUtils.copyPropertiesIgnoreNull(activedRuleTemplateInfVo, activedRuleTemplateInf);
        if (CollectionUtils.isNotEmpty(activedRuleTemplateInfVo.getChartOneList())) {
            activedRuleTemplateInf.setF_CHART_ONE(JSONArray.toJSONString(activedRuleTemplateInfVo.getChartOneList()));
        }
        if (CollectionUtils.isNotEmpty(activedRuleTemplateInfVo.getChartTwoList())) {
            activedRuleTemplateInf.setF_CHART_TWO(JSONArray.toJSONString(activedRuleTemplateInfVo.getChartTwoList()));
        }
        if (CollectionUtils.isNotEmpty(activedRuleTemplateInfVo.getChartThreeList())) {
            activedRuleTemplateInf.setF_CHART_THREE(JSONArray.toJSONString(activedRuleTemplateInfVo.getChartThreeList()));
        }
        activedRuleTemplateInfService.updateById(activedRuleTemplateInf);
        return R.ok();
    }

    /**
     * 批量删除报警规则模板信息
     */
    @RequiresPermissions("dataManagement:activedRuleTemplateInf:remove")
    @Log(title = "报警规则模板管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除报警规则模板信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        activedRuleTemplateInfService.del(ids);
        return R.ok();
    }
}
