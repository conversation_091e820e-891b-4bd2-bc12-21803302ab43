package com.yingfei.dataManagement.controller.gauge;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.gauge.GAUGE_CONNECTIONService;
import com.yingfei.entity.domain.GAUGE_CONNECTION;
import com.yingfei.entity.dto.GAUGE_CONNECTION_DTO;
import com.yingfei.entity.vo.GAUGE_CONNECTION_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "量具连接参数信息API")
@RestController
@RequestMapping("/gauge_connection")
public class GAUGE_CONNECTIONController extends BaseController {

    @Resource
    private GAUGE_CONNECTIONService gaugeConnectionService;

    /**
     * 获取量具连接参数信息列表
     */
    @ApiOperation("获取量具连接参数信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody GAUGE_CONNECTION_VO gaugeFormatVo) {
        List<GAUGE_CONNECTION_DTO> list = gaugeConnectionService.getList(gaugeFormatVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(gaugeConnectionService.getTotal(gaugeFormatVo));
        return dataTable;
    }

    @ApiOperation("获取量具连接参数信息详情")
    @PostMapping("/info/{id}")
    public R<?> info(@PathVariable Long id){
        GAUGE_CONNECTION_DTO gaugeConnectionDto = gaugeConnectionService.info(id);
        return R.ok(gaugeConnectionDto);
    }

    /**
     * 新增量具连接参数信息
     */
    @CreateUpdateBy
    @NotResubmit
    @Log(title = "量具连接参数管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增量具连接参数信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody GAUGE_CONNECTION_VO gaugeFormatVo) {
        gaugeConnectionService.checkParam(gaugeFormatVo);
        gaugeConnectionService.add(gaugeFormatVo);
        return R.ok();
    }

    /**
     * 修改量具连接参数信息
     */
    @Log(title = "量具连接参数管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改量具连接参数信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody GAUGE_CONNECTION_VO gaugeFormatVo) {
        gaugeConnectionService.checkParam(gaugeFormatVo);
        GAUGE_CONNECTION gaugeFormat = new GAUGE_CONNECTION();
        BeanUtils.copyPropertiesIgnoreNull(gaugeFormatVo, gaugeFormat);
        if (gaugeFormatVo.getGaugeConnectionConfigDto() != null){
            gaugeFormat.setF_CONFIG(JSONObject.toJSONString(gaugeFormatVo.getGaugeConnectionConfigDto()));
        }
        gaugeConnectionService.updateById(gaugeFormat);
        return R.ok();
    }

    /**
     * 批量删除量具连接参数信息
     */
    @Log(title = "量具连接参数管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除量具连接参数信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        gaugeConnectionService.del(ids);
        return R.ok();
    }
}
