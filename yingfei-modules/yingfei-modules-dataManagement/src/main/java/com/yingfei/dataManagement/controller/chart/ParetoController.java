package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.dataManagement.service.chart.ParetoService;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.ParetoDTO;
import com.yingfei.entity.enums.ParetoAnalyseTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@Api(tags = "图表:帕累托图API")
@RequestMapping("/pareto")
public class ParetoController {

    @Resource
    private ParetoService paretoService;

    @PostMapping("/info")
    @ApiOperation("获取帕累托图详情")
    public R<?> getInfo(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        List<ParetoDTO> info = paretoService.getInfo(subgroupDataSelectionDTO);
        return R.ok(info);
    }

    @PostMapping("/infoList")
    @ApiOperation("获取帕累托图详情")
    public R<?> getInfoList(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        subgroupDataSelectionDTO.setIsRange(YesOrNoEnum.YES.getType());
        List<ParetoDTO> info = paretoService.getInfoList(subgroupDataSelectionDTO);
        return R.ok(info);
    }


    /**
     * 获取箱线图分析角度类型
     */
    @GetMapping("/getAnalysisType")
    @ApiOperation("获取帕累托图分析角度类型")
    public R<?> getAnalysisType(Integer type) {
        Map<String, String> map = ParetoAnalyseTypeEnum.getMap(type);
        return R.ok(map);
    }
}
