package com.yingfei.dataManagement.controller.manufacturing;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.dataManagement.service.manufacturing.MANUFACTURING_NODE_INFService;
import com.yingfei.entity.dto.MANUFACTURING_NODE_INF_DTO;
import com.yingfei.entity.dto.PARAMETER_CHILD_DTO;
import com.yingfei.entity.vo.MANUFACTURING_NODE_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api(tags = "流程图节点信息API")
@RestController
@RequestMapping("/manufacturing_node_inf")
public class MANUFACTURING_NODE_INFController extends BaseController {
    
    @Resource
    private MANUFACTURING_NODE_INFService manufacturingNodeInfService;

    @ApiOperation("获取流程图节点列表")
    @PostMapping("/getList")
    public R<?> getList(@RequestBody MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo){
        List<MANUFACTURING_NODE_INF_DTO> nodeInfDtoList = manufacturingNodeInfService.getList(manufacturingNodeInfVo);
        return R.ok(nodeInfDtoList);
    }

    /**
     * 获取流程图节点信息
     */
    @ApiOperation("获取流程图节点信息")
    @PostMapping("/getInfo/{id}")
    public R<?> getInfo(@PathVariable Long id) {
        MANUFACTURING_NODE_INF_DTO manufacturingNodeInfDto = manufacturingNodeInfService.getInfo(id);
        return R.ok(manufacturingNodeInfDto);
    }

    /**
     * 新增流程图节点信息
     */
    @CreateUpdateBy
    @Log(title = "流程图节点管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增流程图节点信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo) {
        Long id = manufacturingNodeInfService.add(manufacturingNodeInfVo);
        return R.ok(id);
    }

    /**
     * 修改流程图节点信息
     */
    @Log(title = "流程图节点管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改流程图节点信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo) {
        manufacturingNodeInfService.edit(manufacturingNodeInfVo);
        return R.ok();
    }

    /**
     * 批量删除流程图节点信息
     */
    @Log(title = "流程图节点管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除流程图节点信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        manufacturingNodeInfService.del(ids);
        return R.ok();
    }

    /**
     * 获取对应节点搜索条件
     */
    @ApiOperation("获取对应节点搜索条件")
    @PostMapping("/getSearchCondition")
    public R<?> getSearchCondition(@RequestBody List<PARAMETER_CHILD_DTO> parameterChildDtos) {
        Map<String, Object> map = manufacturingNodeInfService.getSearchCondition(parameterChildDtos);
        return R.ok(map);
    }

    /**
     * 判断静态条件是否删除
     */
    @ApiOperation("判断静态条件是否删除")
    @PostMapping("/judgeConditionIsDelete")
    public R<?> judgeConditionIsDelete(@RequestBody List<PARAMETER_CHILD_DTO> parameterChildDtos) {
        Map<String, Object> map = manufacturingNodeInfService.judgeConditionIsDelete(parameterChildDtos);
        return R.ok(map);
    }
}
