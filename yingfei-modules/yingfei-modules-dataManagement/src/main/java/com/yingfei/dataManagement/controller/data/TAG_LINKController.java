package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.TAG_LINKService;
import com.yingfei.entity.domain.TAG_LINK;
import com.yingfei.entity.dto.TAG_GRP_DTO;
import com.yingfei.entity.dto.TAG_LINK_DTO;
import com.yingfei.entity.vo.TAG_GRP_VO;
import com.yingfei.entity.vo.TAG_LINK_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "标签关联信息API")
@RestController
@RequestMapping("/tag_link")
public class TAG_LINKController extends BaseController {

    @Resource
    private TAG_LINKService tagLinkService;

    /**
     * 获取标签关联信息列表
     */
    @ApiOperation("获取标签关联信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody TAG_LINK_VO tagLinkVo) {
        List<TAG_LINK_DTO> list = tagLinkService.getList(tagLinkVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(tagLinkService.getTotal(tagLinkVo));
        return dataTable;
    }


    /**
     * 修改标签关联信息
     */
    @RequiresPermissions("dataManagement:tagLink:edit")
    @Log(title = "标签关联管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改标签关联信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody TAG_LINK_VO tagLinkVo) {
        tagLinkService.checkParam(tagLinkVo);
        TAG_LINK tagLink = new TAG_LINK();
        BeanUtils.copyPropertiesIgnoreNull(tagLinkVo, tagLink);
        tagLinkService.updateById(tagLink);
        return R.ok();
    }

    /**
     * 批量删除标签关联信息
     */
    @RequiresPermissions("dataManagement:tagLink:remove")
    @Log(title = "标签关联管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除标签关联信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        tagLinkService.del(ids);
        return R.ok();
    }

    /**
     * 通过所选层级查询对应的标签
     */
    @ApiOperation("通过所选层级查询对应的标签")
    @PostMapping("/findByHierList")
    public R<?> findByHierList(@RequestBody TAG_LINK_VO tagLinkVo) {
        List<TAG_GRP_DTO> tagGrpDtoList = tagLinkService.findByHierList(tagLinkVo);
        return R.ok(tagGrpDtoList);
    }
}
