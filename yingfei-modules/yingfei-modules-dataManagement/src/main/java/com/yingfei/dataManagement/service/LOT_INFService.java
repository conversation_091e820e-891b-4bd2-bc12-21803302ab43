package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.LOT_INF;
import com.yingfei.entity.dto.LOT_INF_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.enums.ParetoAnalyseTypeEnum;
import com.yingfei.entity.vo.LOT_INF_VO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【LOT_INF(储存批次信息表)】的数据库操作Service
* @createDate 2024-05-08 16:27:47
*/
public interface LOT_INFService extends IService<LOT_INF>, BaseService<LOT_INF_VO, LOT_INF_DTO> {

    void batchClose(List<Long> ids);

    LOT_INF_DTO info(Long id);

    void delCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum);

    LOT_INF addLotInf(LOT_INF_VO lotInfVo);

    List<LOT_INF> getCondition(List<Long> strings, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum);

    OperationAssociationDTO getOperationAssociation(List<Long> ids);
}
