package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.LOT_INFService;
import com.yingfei.entity.domain.LOT_INF;
import com.yingfei.entity.dto.LOT_INF_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.vo.LOT_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "产品批次信息API")
@RestController
@RequestMapping("/lot_inf")
public class LOT_INFController extends BaseController {
    
    @Resource
    private LOT_INFService lotInfService;

    /**
     * 获取产品批次信息列表
     */
    @ApiOperation("获取产品批次信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody LOT_INF_VO lotInfVo) {
        List<LOT_INF_DTO> list = lotInfService.getList(lotInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(lotInfService.getTotal(lotInfVo));
        return dataTable;
    }

    /**
     * 新增产品批次信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:productBatch:add")
    @Log(title = "产品批次管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增产品批次信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody LOT_INF_VO lotInfVo) {
        lotInfService.checkParam(lotInfVo);
        LOT_INF lotInf = lotInfService.addLotInf(lotInfVo);
        return R.ok(lotInf);
    }

    /**
     * 修改产品批次信息
     */
    @RequiresPermissions("dataManagement:productBatch:edit")
    @Log(title = "产品批次管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改产品批次信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody LOT_INF_VO lotInfVo) {
        lotInfService.checkParam(lotInfVo);
        LOT_INF lotInf = new LOT_INF();
        BeanUtils.copyPropertiesIgnoreNull(lotInfVo, lotInf);
        lotInfService.updateById(lotInf);
        return R.ok();
    }

    /**
     * 批量删除产品批次信息
     */
    @RequiresPermissions("dataManagement:productBatch:remove")
    @Log(title = "产品批次管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除产品批次信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        lotInfService.del(ids);
        return R.ok();
    }

    /**
     * 批量关闭批次信息
     */
    @RequiresPermissions("dataManagement:productBatch:batchClose")
    @Log(title = "产品批次管理", businessType = BusinessType.DELETE)
    @ApiOperation("批量关闭批次信息")
    @DeleteMapping("/batchClose/{ids}")
    public R<?> batchClose(@PathVariable List<Long> ids) {
        lotInfService.batchClose(ids);
        return R.ok();
    }

    @ApiOperation("获取批次信息")
    @PostMapping("/getInfo/{id}")
    public R<?> getInfo(@PathVariable Long id) {
        LOT_INF_DTO lotInfDto = lotInfService.info(id);
        return R.ok(lotInfDto);
    }

    @ApiOperation("获取批次删除影响的关联信息")
    @PostMapping("/getOperationAssociation/{ids}")
    public R<?> getOperationAssociation(@PathVariable List<Long> ids) {
        OperationAssociationDTO operationAssociationDTO = lotInfService.getOperationAssociation(ids);
        return R.ok(operationAssociationDTO);
    }
}
