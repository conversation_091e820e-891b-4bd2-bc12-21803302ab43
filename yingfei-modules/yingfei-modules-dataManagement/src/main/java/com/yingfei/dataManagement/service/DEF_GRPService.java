package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.DEF_GRP;
import com.yingfei.entity.dto.DEF_GRP_DTO;
import com.yingfei.entity.vo.DEF_GRP_VO;

/**
 * <AUTHOR>
 * @description 针对表【DEF_GRP(储存缺陷组信息表)】的数据库操作Service
 * @createDate 2024-05-08 16:27:00
 */
public interface DEF_GRPService extends IService<DEF_GRP>, BaseService<DEF_GRP_VO, DEF_GRP_DTO> {

    DEF_GRP findByName(String fDfgpName);
}
