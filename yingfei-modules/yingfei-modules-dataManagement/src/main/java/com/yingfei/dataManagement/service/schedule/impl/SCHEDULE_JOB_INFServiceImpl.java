package com.yingfei.dataManagement.service.schedule.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.mapper.SCHEDULE_JOB_INFMapper;
import com.yingfei.dataManagement.service.schedule.SCHEDULE_JOB_INFService;
import com.yingfei.dataManagement.service.schedule.ScheduleUtils;
import com.yingfei.entity.domain.SCHEDULE_JOB_INF;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.Scheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SCHEDULE_JOB_INFServiceImpl extends ServiceImpl<SCHEDULE_JOB_INFMapper, SCHEDULE_JOB_INF> implements SCHEDULE_JOB_INFService {

    @Resource
    private Scheduler scheduler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(SCHEDULE_JOB_INF scheduleJob) {
        baseMapper.insert(scheduleJob);
        ScheduleUtils.createScheduleJob(scheduler, scheduleJob);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(SCHEDULE_JOB_INF scheduleJob) {
        ScheduleUtils.deleteScheduleJob(scheduler, scheduleJob.getF_SJOB());
        this.removeById(scheduleJob.getF_SJOB());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SCHEDULE_JOB_INF scheduleJob) {
        this.updateById(scheduleJob);
        scheduleJob = this.getById(scheduleJob.getF_SJOB());
        ScheduleUtils.updateScheduleJob(scheduler, scheduleJob);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(List<Long> jobIds) {
        for (Long id : jobIds) {
            ScheduleUtils.run(scheduler, this.baseMapper.selectById(id));
        }
        updateBatch(jobIds, Constants.NORMAL);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pause(List<Long> jobIds) {
        for (Long id : jobIds) {
            ScheduleUtils.pauseJob(scheduler, id);
        }
        //更新状态
        updateBatch(jobIds, Constants.PAUSE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resume(List<Long> jobIds) {
        for (Long id : jobIds) {
            ScheduleUtils.resumeJob(scheduler, id);
        }
        //更新状态
        updateBatch(jobIds, Constants.NORMAL);
    }


    /**
     * 更新状态
     */
    @Override
    public void updateBatch(List<Long> jobIds, Integer status) {
        for (Long jobId : jobIds) {
            SCHEDULE_JOB_INF scheduleJob = baseMapper.selectById(jobId);
            scheduleJob.setF_STATUS(status);
            scheduleJob.setF_EDUE(SecurityUtils.getUserId());
            baseMapper.updateById(scheduleJob);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIdentify(String identify, List<Long> ids) {
        LambdaQueryWrapper<SCHEDULE_JOB_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SCHEDULE_JOB_INF::getF_TASK_IDENTIFY, identify).in(SCHEDULE_JOB_INF::getF_BUID, ids);
        List<SCHEDULE_JOB_INF> scheduleJobInfList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(scheduleJobInfList)) {
            deleteBatch(scheduleJobInfList.stream().map(SCHEDULE_JOB_INF::getF_SJOB).collect(Collectors.toList()));
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(List<Long> jobIds) {
        for (Long jobId : jobIds) {
            ScheduleUtils.deleteScheduleJob(scheduler, jobId);
        }
        //批量删除
        this.baseMapper.deleteBatchIds(jobIds);
    }


}