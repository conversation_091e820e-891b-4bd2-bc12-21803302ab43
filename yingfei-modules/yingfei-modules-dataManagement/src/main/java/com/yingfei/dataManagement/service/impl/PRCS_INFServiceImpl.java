package com.yingfei.dataManagement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.mapper.PRCS_INFMapper;
import com.yingfei.dataManagement.service.*;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.PRCS_INF_DTO;
import com.yingfei.entity.dto.TAG_DAT_DTO;
import com.yingfei.entity.enums.HIERARCHY_INFTypeEnum;
import com.yingfei.entity.enums.LogTitleEnum;
import com.yingfei.entity.enums.ParetoAnalyseTypeEnum;
import com.yingfei.entity.enums.TAG_LINKTypeEnum;
import com.yingfei.entity.vo.PRCS_INF_VO;
import com.yingfei.entity.vo.TAG_DAT_VO;
import com.yingfei.system.api.RemoteUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【PRCS_INF(过程信息表)】的数据库操作Service实现
 * @createDate 2024-05-08 16:28:11
 */
@Service
public class PRCS_INFServiceImpl extends ServiceImpl<PRCS_INFMapper, PRCS_INF>
        implements PRCS_INFService {

    @Resource
    private TAG_LINKService tagLinkService;
    @Resource
    private TAG_DATService tagDatService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private CTRL_INFService ctrlInfService;
    @Resource
    private LOT_INFService lotInfService;
    @Resource
    private EVNT_INFService evntInfService;
    @Resource
    private RemoteUserService remoteUserService;

    @Override
    public List<PRCS_INF_DTO> getList(PRCS_INF_VO prcsInfVo) {
        if (CollectionUtils.isNotEmpty(prcsInfVo.getParameterHierIds())) {
            Set<HIERARCHY_INF_DTO> set = new HashSet<>();
            prcsInfVo.getParameterHierIds().forEach(hierId -> {
                R<HIERARCHY_INF_DTO> data = remoteUserService.findByHierId(hierId);
                if (data.getData() != null) {
                    List<HIERARCHY_INF_DTO> list = new ArrayList<>();
                    HIERARCHY_INF_DTO hierarchyInfDto = data.getData();
                    HIERARCHY_INF_DTO.getHierarchyInfList(list, hierarchyInfDto, HIERARCHY_INFTypeEnum.FACTORY.getType());
                    set.addAll(list);
                }
            });
            List<Long> hierarchyInfIds = prcsInfVo.getHierarchyInfIds();
            List<Long> collect = set.stream().map(HIERARCHY_INF_DTO::getF_HIER).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hierarchyInfIds))
                collect.retainAll(hierarchyInfIds);
            if (CollectionUtils.isEmpty(collect)) {
                return new ArrayList<>();
            } else {
                prcsInfVo.setHierarchyInfIds(collect);
            }
        }
        prcsInfVo.setDbType(InitConfig.getDriverType());
        List<PRCS_INF_DTO> list = baseMapper.getList(prcsInfVo);
        for (PRCS_INF_DTO prcsInfDto : list) {
            List<TAG_LINK> tagLinkList = tagLinkService.findByResourceIdAndType(prcsInfDto.getF_PRCS(), TAG_LINKTypeEnum.PRCS_DAT.getCode());
            if (CollectionUtils.isEmpty(tagLinkList)) continue;
            List<Long> collect = tagLinkList.stream().map(TAG_LINK::getF_TAG).collect(Collectors.toList());
            TAG_DAT_VO tagDatVo = new TAG_DAT_VO();
            tagDatVo.setIds(collect).setNext(Constants.NEXT);
            tagDatVo.setDbType(prcsInfVo.getDbType());
            List<TAG_DAT_DTO> tagDatList = tagDatService.getList(tagDatVo);
            prcsInfDto.setTagDatDtoList(tagDatList);
            prcsInfDto.setTagCount(tagDatList.size());
        }
        return list;
    }


    @Override
    public List<PRCS_INF_DTO> getTree(PRCS_INF_VO prcsInfVo) {
        return buildDeptTree(baseMapper.getList(prcsInfVo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recovery(Long id) {
        Long fEmpl = SecurityUtils.getLoginUser().getSysUser().getF_EMPL();
        LambdaUpdateWrapper<PRCS_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PRCS_INF::getF_PRCS, id)
                .set(PRCS_INF::getF_DEL, YesOrNoEnum.NO.getType()).set(PRCS_INF::getF_EDUE, fEmpl);
        baseMapper.update(null, updateWrapper);

        /*恢复*/
        baseMapper.emplPrcsRecovery(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cover(Long id) {
        baseMapper.deleteById(id);

        /*删除用户过程关联*/
        baseMapper.emplPrcsCover(id);

        /*删除关联标签数据*/
        LambdaQueryWrapper<TAG_LINK> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TAG_LINK::getF_RESOURCE, id).eq(TAG_LINK::getF_TYPE, TAG_LINKTypeEnum.PRCS_DAT.getCode());
        tagLinkService.remove(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> importPrcs(List<PRCS_INF_VO> prcsInfVoList) {
        if (StringUtils.isNull(prcsInfVoList) || prcsInfVoList.size() == 0) {
            throw new BusinessException(CommonExceptionEnum.IMPORT_DATA_NOT_NULL_EXCEPTION);
        }
        int successNum = 0;
        int failureNum = 1;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (PRCS_INF_VO prcsInfVo : prcsInfVoList) {
            try {
                // 验证是否存在这个过程名称
                List<PRCS_INF> prcsInfList = getPrcsInfs(prcsInfVo);
                if (CollectionUtils.isEmpty(prcsInfList)) {
                    /*判断删除中是否存在过程名称*/
                    PRCS_INF prcsInf = recycleBin(prcsInfVo);
                    if (prcsInf != null) {
                        if (prcsInfVo.getCoverOrRecover() == 0) {
                            cover(prcsInf.getF_PRCS());
                        } else {
                            recovery(prcsInf.getF_PRCS());
                            continue;
                        }
                    }

                    if (StringUtils.isEmpty(prcsInfVo.getF_PLNT_NAME()) && StringUtils.isEmpty(prcsInfVo.getParentName())) {
                        failureNum++;
                        failureMsg.append("<br/>").append("第").append(failureNum).append("行:").append("、过程 ").append(prcsInfVo.getF_NAME()).append(" 未填写车间名称或者上级过程名称");
                        continue;
                    } else if (StringUtils.isNotEmpty(prcsInfVo.getF_PLNT_NAME()) && StringUtils.isNotEmpty(prcsInfVo.getParentName())) {
                        failureNum++;
                        failureMsg.append("<br/>").append("第").append(failureNum).append("行:").append("、过程 ").append(prcsInfVo.getF_NAME()).append(" 车间名称和上级过程名称不能同时填写");
                        continue;
                    }

                    /*获取车间id*/
                    List<HIERARCHY_INF_DTO> list = new ArrayList<>();
                    HIERARCHY_INF_DTO.getHierarchyInfList(list, SecurityUtils.getLoginUser().getSysUser().getHierarchyInfDto(), HIERARCHY_INFTypeEnum.FACTORY.getType());
                    if (CollectionUtils.isEmpty(list)) {
                        failureNum++;
                        failureMsg.append("<br/>").append("第").append(failureNum).append("行:").append("、过程 ").append(prcsInfVo.getF_NAME()).append(" 该用户未分配车间");
                        continue;
                    } else {
                        if (StringUtils.isNotEmpty(prcsInfVo.getF_PLNT_NAME())) {
                            List<HIERARCHY_INF_DTO> collect = list.stream().filter(t -> t.getF_NAME().equals(prcsInfVo.getF_PLNT_NAME())).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(collect) || collect.size() > 1) {
                                failureNum++;
                                failureMsg.append("<br/>").append("第").append(failureNum).append("行:").append("、过程 ").append(prcsInfVo.getF_NAME()).append(" 车间名称不属于该用户");
                                continue;
                            }
                            prcsInfVo.setF_PLNT(collect.get(0).getF_HIER());
                            prcsInfVo.setF_PARENT_PRCS(0L);
                        } else {
                            LambdaQueryWrapper<PRCS_INF> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.eq(PRCS_INF::getF_NAME, prcsInfVo.getParentName()).eq(PRCS_INF::getF_DEL, DelFlagEnum.USE.getType());
                            PRCS_INF parentPrcs = baseMapper.selectOne(queryWrapper);
                            if (parentPrcs == null) {
                                failureNum++;
                                failureMsg.append("<br/>").append("第").append(failureNum).append("行:").append("、过程 ").append(prcsInfVo.getF_NAME()).append(" 上级过程名称不存在");
                                continue;
                            }
                            prcsInfVo.setF_PLNT(parentPrcs.getF_PLNT());
                            prcsInfVo.setF_PARENT_PRCS(parentPrcs.getF_PRCS());
                        }
                    }

                    prcsInfVo.setF_CRUE(SecurityUtils.getUserId());
                    prcsInfVo.setF_EDUE(SecurityUtils.getUserId());
                    this.add(prcsInfVo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、过程 ").append(prcsInfVo.getF_NAME()).append(" 导入成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append("第").append(failureNum).append("行:").append("、过程 ").append(prcsInfVo.getF_NAME()).append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>第" + failureNum + "行、过程 " + prcsInfVo.getF_NAME() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
                e.printStackTrace();
            }
        }
        if (failureNum > 1) {
            failureMsg.insert(0, "共 " + (failureNum - 1) + " 条数据格式不正确，错误如下：");
        }
        successMsg.insert(0, "共 " + successNum + " 条导入成功，数据如下：");
        Map<String, String> map = new HashMap<>();
        map.put("successMsg", successMsg.toString());
        map.put("failureMsg", failureMsg.toString());
        return map;
    }

    @Override
    public PRCS_INF recycleBin(PRCS_INF_VO prcsInfVo) {
        LambdaQueryWrapper<PRCS_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PRCS_INF::getF_NAME, prcsInfVo.getF_NAME())
                .eq(PRCS_INF::getF_DEL, DelFlagEnum.DELETE.getType())
                .orderByDesc(PRCS_INF::getF_CRTM);
        List<PRCS_INF> prcsInfList = baseMapper.selectList(queryWrapper);
        return CollectionUtils.isEmpty(prcsInfList) ? null : prcsInfList.get(0);
    }

    @Override
    public List<PRCS_INF> getSearchCondition(Integer isInclude, List<Long> dataList, List<Long> hierIds) {
        LambdaQueryWrapper<PRCS_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PRCS_INF::getF_DEL, DelFlagEnum.USE.getType());
        if (CollectionUtils.isNotEmpty(dataList)) {
            if (isInclude == 1) {
                queryWrapper.in(PRCS_INF::getF_PRCS, dataList);
            } else {
                queryWrapper.notIn(PRCS_INF::getF_PRCS, dataList);
            }
        }
        if (CollectionUtils.isNotEmpty(hierIds)) {
            queryWrapper.in(PRCS_INF::getF_PLNT, hierIds);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public HIERARCHY_INF_DTO getBelongPrcsList(PRCS_INF_VO prcsInfVo) {
        HIERARCHY_INF_DTO hierarchyInfDto = SecurityUtils.getLoginUser().getSysUser().getHierarchyInfDto();
        List<PRCS_INF_DTO> list = getList(prcsInfVo);
        recursionFn(hierarchyInfDto, list);
        return hierarchyInfDto;
    }

    @Override
    public void batchEdit(PRCS_INF_VO prcsInfVo) {
        if (CollectionUtils.isNotEmpty(prcsInfVo.getIds())) {
            if (prcsInfVo.getTagEdType() == 0) {
                tagLinkService.delByType(prcsInfVo.getIds(), TAG_LINKTypeEnum.PRCS_DAT.getCode());
            } else if (prcsInfVo.getTagEdType() == 1) {
                tagLinkService.addByType(prcsInfVo.getIds(), prcsInfVo.getTagDatDtoList(), TAG_LINKTypeEnum.PRCS_DAT.getCode());
            } else {
                prcsInfVo.getIds().forEach(prcsId -> {
                    tagLinkService.saveByType(prcsId, prcsInfVo.getTagDatDtoList(), TAG_LINKTypeEnum.PRCS_DAT.getCode());
                });
            }
        }
    }

    @Override
    public PRCS_INF_DTO getInfo(Long id) {
        PRCS_INF prcsInf = baseMapper.selectById(id);
        PRCS_INF_DTO prcsInfDto = new PRCS_INF_DTO();
        BeanUtils.copyPropertiesIgnoreNull(prcsInf, prcsInfDto);
        List<TAG_LINK> tagLinkList = tagLinkService.findByResourceIdAndType(prcsInf.getF_PRCS(), TAG_LINKTypeEnum.PRCS_DAT.getCode());
        if (CollectionUtils.isNotEmpty(tagLinkList)) {
            List<Long> collect = tagLinkList.stream().map(TAG_LINK::getF_TAG).collect(Collectors.toList());
            TAG_DAT_VO tagDatVo = new TAG_DAT_VO();
            tagDatVo.setIds(collect).setNext(Constants.NEXT);
            tagDatVo.setDbType(InitConfig.getDriverType());
            List<TAG_DAT_DTO> tagDatList = tagDatService.getList(tagDatVo);
            prcsInfDto.setTagDatDtoList(tagDatList);
        }
        return prcsInfDto;
    }

    @Override
    public OperationAssociationDTO getOperationAssociation(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        OperationAssociationDTO operationAssociationDTO = new OperationAssociationDTO();
        operationAssociationDTO.setInfluenceIdList(ids);
        operationAssociationDTO.setTitle(LogTitleEnum.PRCS.getTitle());
        operationAssociationDTO.setOperationType(BusinessType.DELETE.name());
        operationAssociationDTO.setTableName(LogTitleEnum.PRCS.getTableName());

        List<OperationAssociationDTO> list = new ArrayList<>();

        /*删除用户过程关联数据*/
        List<EMPL_PRCS_LINK> emplPrcsLinkList = baseMapper.getEmplPrcs(ids);
        if (CollectionUtils.isNotEmpty(emplPrcsLinkList)) {
            OperationAssociationDTO lotInfOperationAssociationDTO = new OperationAssociationDTO();
            lotInfOperationAssociationDTO.setInfluenceIdList(emplPrcsLinkList.stream().map(EMPL_PRCS_LINK::getF_EMPR).collect(Collectors.toList()));
            lotInfOperationAssociationDTO.setOperationType(Constants.CASCADE_DELETE);
            lotInfOperationAssociationDTO.setTableName(LogTitleEnum.EMPL_PRCS.getTableName());
            list.add(lotInfOperationAssociationDTO);
        }

        /*删除对应子组数据*/
        List<SGRP_INF> sgrpInfList = sgrpInfService.getCondition(ids, ParetoAnalyseTypeEnum.PRCS_DAT);
        if (CollectionUtils.isNotEmpty(sgrpInfList)) {
            OperationAssociationDTO sgrpInfOperationAssociationDTO = new OperationAssociationDTO();
            sgrpInfOperationAssociationDTO.setInfluenceIdList(sgrpInfList.stream().map(SGRP_INF::getF_SGRP).collect(Collectors.toList()));
            sgrpInfOperationAssociationDTO.setOperationType(Constants.CASCADE_DELETE);
            sgrpInfOperationAssociationDTO.setTableName(LogTitleEnum.SGRP.getTableName());
            list.add(sgrpInfOperationAssociationDTO);
        }

        /*删除对应的公差限*/
        List<SPEC_INF> specInfList = specInfService.getCondition(ids, ParetoAnalyseTypeEnum.PRCS_DAT);
        if (CollectionUtils.isNotEmpty(specInfList)) {
            OperationAssociationDTO specInfOperationAssociationDTO = new OperationAssociationDTO();
            specInfOperationAssociationDTO.setInfluenceIdList(specInfList.stream().map(SPEC_INF::getF_SPEC).collect(Collectors.toList()));
            specInfOperationAssociationDTO.setOperationType(Constants.CASCADE_DELETE);
            specInfOperationAssociationDTO.setTableName(LogTitleEnum.SPEC.getTableName());
            list.add(specInfOperationAssociationDTO);
        }

        /*删除对应控制限*/
        List<CTRL_INF> ctrlInfList = ctrlInfService.getCondition(ids, ParetoAnalyseTypeEnum.PRCS_DAT);
        if (CollectionUtils.isNotEmpty(ctrlInfList)) {
            OperationAssociationDTO ctrlInfOperationAssociationDTO = new OperationAssociationDTO();
            ctrlInfOperationAssociationDTO.setInfluenceIdList(ctrlInfList.stream().map(CTRL_INF::getF_CTRL).collect(Collectors.toList()));
            ctrlInfOperationAssociationDTO.setOperationType(Constants.CASCADE_DELETE);
            ctrlInfOperationAssociationDTO.setTableName(LogTitleEnum.CTRL.getTableName());
            list.add(ctrlInfOperationAssociationDTO);
        }

        /*删除对应批次*/
        List<LOT_INF> lotInfList = lotInfService.getCondition(ids, ParetoAnalyseTypeEnum.PRCS_DAT);
        if (CollectionUtils.isNotEmpty(lotInfList)) {
            OperationAssociationDTO lotInfOperationAssociationDTO = new OperationAssociationDTO();
            lotInfOperationAssociationDTO.setInfluenceIdList(lotInfList.stream().map(LOT_INF::getF_LOT).collect(Collectors.toList()));
            lotInfOperationAssociationDTO.setOperationType(Constants.CASCADE_DELETE);
            lotInfOperationAssociationDTO.setTableName(LogTitleEnum.LOT.getTableName());
            list.add(lotInfOperationAssociationDTO);
        }

        /*删除对应过程事件*/
        List<EVNT_INF> evntInfList = evntInfService.getCondition(ids, ParetoAnalyseTypeEnum.PRCS_DAT);
        if (CollectionUtils.isNotEmpty(evntInfList)) {
            OperationAssociationDTO evntInfOperationAssociationDTO = new OperationAssociationDTO();
            evntInfOperationAssociationDTO.setInfluenceIdList(evntInfList.stream().map(EVNT_INF::getF_EVNT).collect(Collectors.toList()));
            evntInfOperationAssociationDTO.setOperationType(Constants.CASCADE_DELETE);
            evntInfOperationAssociationDTO.setTableName(LogTitleEnum.EVNT.getTableName());
            list.add(evntInfOperationAssociationDTO);
        }
        operationAssociationDTO.setCascadeRecordList(list);
        return operationAssociationDTO;
    }

    public void recursionFn(HIERARCHY_INF_DTO hierarchyInfDto, List<PRCS_INF_DTO> list) {
        List<PRCS_INF_DTO> prcsInfList = list.stream().filter(s -> s.getF_PLNT().equals(hierarchyInfDto.getF_HIER())).collect(Collectors.toList());
        hierarchyInfDto.setPrcsInfList(prcsInfList);
        if (CollectionUtils.isEmpty(hierarchyInfDto.getChildren())) {
            return;
        }
        hierarchyInfDto.getChildren().forEach(hierarchyInf -> {
            recursionFn(hierarchyInf, list);
        });
    }

    @Override
    public long getTotal(PRCS_INF_VO prcsInfVo) {
        return baseMapper.getTotal(prcsInfVo);
    }


    private List<PRCS_INF_DTO> buildDeptTree(List<PRCS_INF_DTO> list) {
        List<PRCS_INF_DTO> returnList = new ArrayList<>();
        List<Long> tempList = list.stream().map(PRCS_INF_DTO::getF_PRCS).collect(Collectors.toList());
        for (PRCS_INF_DTO dept : list) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getF_PARENT_PRCS())) {
                recursionFn(list, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = list;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<PRCS_INF_DTO> list, PRCS_INF_DTO t) {
        // 得到子节点列表
        List<PRCS_INF_DTO> childList = getChildList(list, t);
        t.setChildren(childList);
        for (PRCS_INF_DTO tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<PRCS_INF_DTO> getChildList(List<PRCS_INF_DTO> list, PRCS_INF_DTO t) {
        List<PRCS_INF_DTO> tlist = new ArrayList<>();
        Iterator<PRCS_INF_DTO> it = list.iterator();
        while (it.hasNext()) {
            PRCS_INF_DTO n = it.next();
            if (StringUtils.isNotNull(n.getF_PARENT_PRCS()) && n.getF_PARENT_PRCS().equals(t.getF_PRCS())) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<PRCS_INF_DTO> list, PRCS_INF_DTO t) {
        return getChildList(list, t).size() > 0;
    }

    @Override
    public void checkParam(PRCS_INF_VO prcsInfVo) {
        List<PRCS_INF> prcsInfList = getPrcsInfs(prcsInfVo);
        if (CollectionUtils.isEmpty(prcsInfList)) return;
        if (prcsInfVo.getF_PRCS() != null) {
            if (prcsInfList.size() > 1 || !Objects.equals(prcsInfList.get(0).getF_PRCS(), prcsInfVo.getF_PRCS())) {
                throw new BusinessException(DataManagementExceptionEnum.PROCESS_NAME_DUPLICATION_EXCEPTION);
            }
        } else {
            if (prcsInfList.size() > 0) {
                throw new BusinessException(DataManagementExceptionEnum.PROCESS_NAME_DUPLICATION_EXCEPTION);
            }
        }
    }

    private List<PRCS_INF> getPrcsInfs(PRCS_INF_VO prcsInfVo) {
        LambdaQueryWrapper<PRCS_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PRCS_INF::getF_NAME, prcsInfVo.getF_NAME())
                .eq(PRCS_INF::getF_PLNT, prcsInfVo.getF_PLNT())
                .eq(PRCS_INF::getF_DEL, DelFlagEnum.USE.getType());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void add(PRCS_INF_VO prcsInfVo) {
        PRCS_INF prcsInf = new PRCS_INF();
        BeanUtils.copyPropertiesIgnoreNull(prcsInfVo, prcsInf);
        baseMapper.insert(prcsInf);
        prcsInfVo.setF_PRCS(prcsInf.getF_PRCS());
        if (CollectionUtils.isNotEmpty(prcsInfVo.getTagDatDtoList())) {
            tagLinkService.saveByType(prcsInf.getF_PRCS(), prcsInfVo.getTagDatDtoList(), TAG_LINKTypeEnum.PRCS_DAT.getCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        /*删除用户过程关联数据*/
        baseMapper.deleteEmplPrcs(ids);

        /*删除对应子组数据*/
        sgrpInfService.updateCondition(ids, ParetoAnalyseTypeEnum.PRCS_DAT);

        /*删除对应的公差限*/
        specInfService.delCondition(ids, ParetoAnalyseTypeEnum.PRCS_DAT);

        /*删除对应控制限*/
        ctrlInfService.delCondition(ids, ParetoAnalyseTypeEnum.PRCS_DAT);

        /*删除对应批次*/
        lotInfService.delCondition(ids, ParetoAnalyseTypeEnum.PRCS_DAT);

        /*删除对应过程事件*/
        evntInfService.delCondition(ids, ParetoAnalyseTypeEnum.PRCS_DAT);

        /*根据id列表修改对应的删除状态*/
        LambdaUpdateWrapper<PRCS_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(PRCS_INF::getF_PRCS, ids)
                .set(PRCS_INF::getF_DEL, YesOrNoEnum.YES.getType());
        baseMapper.update(null, updateWrapper);
    }

}




