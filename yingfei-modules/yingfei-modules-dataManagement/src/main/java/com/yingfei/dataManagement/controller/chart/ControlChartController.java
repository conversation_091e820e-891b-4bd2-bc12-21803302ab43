package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.dataManagement.service.chart.ControlChartService;
import com.yingfei.entity.dto.DataSummaryDTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.ControlChartDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 控制图Controller
 */
@Slf4j
@RestController
@Api(tags = "图表:控制图API")
@RequestMapping("/controlChart")
public class ControlChartController {

    @Resource
    private ControlChartService controlChartService;

    @PostMapping("/info")
    @ApiOperation("控制图")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = ControlChartDTO.class)
    })
    public R<ControlChartDTO> controlChart(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        ControlChartDTO controlChartDTO = controlChartService.getInfo(subgroupDataSelectionDTO);
        return R.ok(controlChartDTO);
    }


    @PostMapping("/infoList")
    @ApiOperation("多控制图")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = ControlChartDTO.class)
    })
    public R<List<ControlChartDTO>> controlChartList(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        subgroupDataSelectionDTO.setIsRange(YesOrNoEnum.YES.getType());
        List<ControlChartDTO> list = controlChartService.getInfoList(subgroupDataSelectionDTO);
        return R.ok(list);
    }


    @ApiOperation("样本分布(点击单个数据点计算)")
    @PostMapping("/distribution")
    public R<?> distribution(@RequestBody SubgroupDataDTO subgroupDataDTO) {
        DataSummaryDTO dataSummaryDTO = controlChartService.distribution(subgroupDataDTO);
        return R.ok(dataSummaryDTO);
    }
}
