package com.yingfei.dataManagement.mapper;

import com.yingfei.entity.domain.NOTIFICATION_RULE;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yingfei.entity.dto.NOTIFICATION_RULE_DTO;
import com.yingfei.entity.vo.NOTIFICATION_RULE_VO;

import java.util.List;

/**
* 
* @description 针对表【NOTIFICATION_RULE(报警通知配置表)】的数据库操作Mapper
* @createDate 2024-08-19 15:36:07
* @Entity com.yingfei.entity.domain.NOTIFICATION_RULE
*/
public interface NOTIFICATION_RULEMapper extends BaseMapper<NOTIFICATION_RULE> {

    long getTotal(NOTIFICATION_RULE_VO notificationRuleVo);

    List<NOTIFICATION_RULE_DTO> getList(NOTIFICATION_RULE_VO notificationRuleVo);
}




