package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.JOB_GRPService;
import com.yingfei.entity.domain.JOB_GRP;
import com.yingfei.entity.dto.JOB_GRP_DTO;
import com.yingfei.entity.vo.JOB_GRP_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "工单组信息API")
@RestController
@RequestMapping("/job_grp")
public class JOB_GRPController extends BaseController {
    
    @Resource
    private JOB_GRPService jobGrpService;

    /**
     * 获取工单组信息列表
     */
    @ApiOperation("获取工单组信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody JOB_GRP_VO jobGrpVo) {
        List<JOB_GRP_DTO> list = jobGrpService.getList(jobGrpVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(jobGrpService.getTotal(jobGrpVo));
        return dataTable;
    }

    /**
     * 新增工单组信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:jobGrp:add")
    @Log(title = "工单组管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增工单组信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody JOB_GRP_VO jobGrpVo) {
        jobGrpService.checkParam(jobGrpVo);
        jobGrpService.add(jobGrpVo);
        return R.ok();
    }

    /**
     * 修改工单组信息
     */
    @RequiresPermissions("dataManagement:jobGrp:edit")
    @Log(title = "工单组管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改工单组信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody JOB_GRP_VO jobGrpVo) {
        jobGrpService.checkParam(jobGrpVo);
        JOB_GRP jobGrp = new JOB_GRP();
        BeanUtils.copyPropertiesIgnoreNull(jobGrpVo, jobGrp);
        jobGrpService.updateById(jobGrp);
        return R.ok();
    }

    /**
     * 批量删除工单组信息
     */
    @RequiresPermissions("dataManagement:jobGrp:remove")
    @Log(title = "工单组管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除工单组信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        jobGrpService.del(ids);
        return R.ok();
    }
}
