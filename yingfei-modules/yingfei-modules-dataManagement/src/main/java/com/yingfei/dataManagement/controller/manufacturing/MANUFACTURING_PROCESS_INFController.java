package com.yingfei.dataManagement.controller.manufacturing;

import com.alibaba.fastjson2.JSONArray;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.datascope.annotation.DataScope;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.manufacturing.MANUFACTURING_PROCESS_INFService;
import com.yingfei.entity.domain.MANUFACTURING_PROCESS_INF;
import com.yingfei.entity.dto.MANUFACTURING_PROCESS_INF_DTO;
import com.yingfei.entity.vo.MANUFACTURING_PROCESS_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "流程图结构信息API")
@RestController
@RequestMapping("/manufacturing_process_inf")
public class MANUFACTURING_PROCESS_INFController extends BaseController {
    
    @Resource
    private MANUFACTURING_PROCESS_INFService manufacturingProcessInfService;

    /**
     * 获取流程图结构信息列表
     */
    @DataScope
    @ApiOperation("获取流程图结构信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody MANUFACTURING_PROCESS_INF_VO manufacturingProcessInfVo) {
        List<MANUFACTURING_PROCESS_INF_DTO> list = manufacturingProcessInfService.getList(manufacturingProcessInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(manufacturingProcessInfService.getTotal(manufacturingProcessInfVo));
        return dataTable;
    }

    @ApiOperation("获取流程图结构信息")
    @PostMapping("/getInfo/{id}")
    public R<?> getInfo(@PathVariable Long id) {
        MANUFACTURING_PROCESS_INF manufacturingProcessInf = manufacturingProcessInfService.getById(id);
        return R.ok(manufacturingProcessInf);
    }

    /**
     * 新增流程图结构信息
     */
    @CreateUpdateBy
    @RequiresPermissions("dataManagement:manufacturingProcessInf:add")
    @Log(title = "流程图结构管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增流程图结构信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody MANUFACTURING_PROCESS_INF_VO manufacturingProcessInfVo) {
        manufacturingProcessInfService.checkParam(manufacturingProcessInfVo);
        manufacturingProcessInfService.add(manufacturingProcessInfVo);
        return R.ok();
    }

    /**
     * 修改流程图结构信息
     */
    @RequiresPermissions("dataManagement:manufacturingProcessInf:edit")
    @Log(title = "流程图结构管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改流程图结构信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody MANUFACTURING_PROCESS_INF_VO manufacturingProcessInfVo) {
        manufacturingProcessInfService.checkParam(manufacturingProcessInfVo);
        manufacturingProcessInfService.edit(manufacturingProcessInfVo);
        return R.ok();
    }

    /**
     * 批量删除流程图结构信息
     */
    @RequiresPermissions("dataManagement:manufacturingProcessInf:remove")
    @Log(title = "流程图结构管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除流程图结构信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        manufacturingProcessInfService.del(ids);
        return R.ok();
    }

    /**
     * 获取流程图结构信息(外部调用)
     */
    @ApiOperation("获取流程图结构信息(外部调用)")
    @GetMapping("/getManufacturingInfo")
    public R<?> getManufacturingInfo(){
        JSONArray list = manufacturingProcessInfService.getManufacturingInfo();
        return R.ok(list);
    }
}
