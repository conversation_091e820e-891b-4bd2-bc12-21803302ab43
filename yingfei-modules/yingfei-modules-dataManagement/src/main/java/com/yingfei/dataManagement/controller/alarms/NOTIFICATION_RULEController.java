package com.yingfei.dataManagement.controller.alarms;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.NOTIFICATION_RULEService;
import com.yingfei.entity.dto.NOTIFICATION_RULE_DTO;
import com.yingfei.entity.enums.SpecificationLimitViolation;
import com.yingfei.entity.vo.NOTIFICATION_RULE_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api(tags = "报警通知配置信息API")
@RestController
@RequestMapping("/notification_rule")
public class NOTIFICATION_RULEController extends BaseController {

    @Resource
    private NOTIFICATION_RULEService notificationRuleService;

    /**
     * 获取报警筛选范围查询条件
     */
    @ApiOperation("获取报警筛选范围查询条件")
    @PostMapping("/getFilterCondition")
    public R<?> getFilterCondition(@RequestBody String dataJson) {
        Map<String, List<Long>> map = notificationRuleService.getFilterCondition(dataJson);
        return R.ok(map);
    }

    /**
     * 获取报警通知配置信息列表
     */
    @ApiOperation("获取报警通知配置信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody NOTIFICATION_RULE_VO notificationRuleVo) {
        List<NOTIFICATION_RULE_DTO> list = notificationRuleService.getList(notificationRuleVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(notificationRuleService.getTotal(notificationRuleVo));
        return dataTable;
    }

    /**
     * 新增报警通知配置信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:cpNotice:add")
    @Log(title = "报警通知配置管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增报警通知配置信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody NOTIFICATION_RULE_VO notificationRuleVo) {
        notificationRuleService.checkParam(notificationRuleVo);
        notificationRuleService.add(notificationRuleVo);
        return R.ok();
    }

    /**
     * 修改报警通知配置信息
     */
    @RequiresPermissions("dataManagement:cpNotice:edit")
    @Log(title = "报警通知配置管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改报警通知配置信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody NOTIFICATION_RULE_VO notificationRuleVo) {
        notificationRuleService.checkParam(notificationRuleVo);
        notificationRuleService.edit(notificationRuleVo);
        return R.ok();
    }

    /**
     * 批量删除报警通知配置信息
     */
    @RequiresPermissions("dataManagement:cpNotice:remove")
    @Log(title = "报警通知配置管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除报警通知配置信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        notificationRuleService.del(ids);
        return R.ok();
    }

    /**
     * 获取公差限报警枚举列表
     */
    @ApiOperation("获取公差限报警枚举列表")
    @GetMapping("/getSpecList")
    public R<?> getSpecList() {
        Map<Integer, String> map = SpecificationLimitViolation.getTypeList();
        return R.ok(map);
    }
}
