package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.PART_INF;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.PART_INF_DTO;
import com.yingfei.entity.vo.PART_INF_VO;
import com.yingfei.entity.vo.PART_REV_VO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【PART_INF(产品信息表)】的数据库操作Service
 * @createDate 2024-05-08 16:28:01
 */
public interface PART_INFService extends IService<PART_INF>, BaseService<PART_INF_VO, PART_INF_DTO> {

    long getUnfoldTotal(PART_INF_VO partInfVo);

    void del(PART_INF_VO partInfVo);

    PART_INF_DTO info(Long id, Long revId);

    void upgrade(PART_REV_VO partInfVo);

    void recovery(Long id);

    void cover(Long id);

    Map<String, String> importPart(List<PART_INF_VO> partInfVoList);

    PART_INF recycleBin(PART_INF_VO partInfVo);

    List<PART_INF> getSearchCondition(Integer isInclude, List<Long> dataList, List<Long> hierIds);

    void batchEdit(PART_INF_VO partInfVo);

    void edit(PART_INF_VO partInfVo);

    HIERARCHY_INF_DTO getBelongPartList(PART_INF_VO partInfVo);

    PART_INF findByName(String partName, Long fPlnt);

    PART_INF_VO addPartInf(PART_INF_VO partInfVo);

    OperationAssociationDTO getOperationAssociation(PART_INF_VO partInfVo);
}
