package com.yingfei.dataManagement.controller.basic;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.poi.ExcelUtil;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.datascope.annotation.DataScope;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.entity.dto.SPEC_INF_DTO;
import com.yingfei.entity.vo.SPEC_INF_VO;
import com.yingfei.entity.vo.excel.SPEC_INF_EXCEL_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

@Api(tags = "公差限信息API")
@Slf4j
@RestController
@RequestMapping("/spec_inf")
public class SPEC_INFController extends BaseController {

    @Resource
    private SPEC_INFService specInfService;

    /**
     * 获取公差限信息列表
     */
    @DataScope
    @ApiOperation("获取公差限信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody SPEC_INF_VO specInfVo) {
        List<SPEC_INF_DTO> list = specInfService.getList(specInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(specInfService.getTotal(specInfVo));
        return dataTable;
    }

    /**
     * 新增公差限信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:specInf:add")
    @Log(title = "公差限管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增公差限信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody SPEC_INF_VO specInfVo) {
        specInfService.checkParam(specInfVo);
        specInfVo.validateLimits();
        specInfService.add(specInfVo);
        return R.ok();
    }

    /**
     * 修改公差限信息
     */
    @RequiresPermissions("dataManagement:specInf:edit")
    @Log(title = "公差限管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改公差限信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody SPEC_INF_VO specInfVo) {
        specInfService.checkParam(specInfVo);
        specInfVo.validateLimits();
        specInfService.edit(specInfVo);
        return R.ok();
    }

    /**
     * 批量删除公差限信息
     */
    @RequiresPermissions("dataManagement:specInf:remove")
    @Log(title = "公差限管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除公差限信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        specInfService.del(ids);
        return R.ok();
    }

    /**
     * 获取报警限和合理限规则
     */
    @ApiOperation("获取报警限和合理限规则")
    @PostMapping("/getRule")
    public R<?> getRule() {
        return R.ok(specInfService.getRule());
    }

    /**
     * 设置报警限和合理限规则
     */
    @RequiresPermissions("dataManagement:specInf:setRule")
    @Log(title = "公差限管理-规则设置", businessType = BusinessType.UPDATE)
    @ApiOperation("设置报警限和合理限规则")
    @GetMapping("/setRule")
    public R<?> setRule(String defaultAlarm, String defaultRational) {
        specInfService.setRule(defaultAlarm, defaultRational);
        return R.ok();
    }

    /**
     * 根据产品+版本查询记录后批量新增
     */
    @ApiOperation("根据产品+版本查询记录后批量新增")
    @Log(title = "公差限管理-批量新增", businessType = BusinessType.INSERT)
    @RequiresPermissions("dataManagement:specInf:addBatch")
    @PostMapping("/addByProductVersion")
    public R<?> addByProductVersion(@RequestBody List<SPEC_INF_VO> specInfVoList) {
        specInfService.addByProductVersion(specInfVoList);
        return R.ok();
    }

    /**
     * 获取产品过程测试对应公差限
     */
    @ApiOperation("获取产品过程测试对应公差限")
    @PostMapping("/getInfo")
    public R<?> getInfo(@RequestBody SPEC_INF_VO specInfVo) {
        return R.ok(specInfService.getInfo(specInfVo));
    }

    /**
     * 获取产品过程测试对应公差限
     */
    @ApiOperation("批量获取产品过程测试对应公差限")
    @PostMapping("/getInfoMapList")
    public R<?> getInfoMapList(@RequestBody SPEC_INF_VO specInfVo) {
        return R.ok(specInfService.getInfoMapList(specInfVo));
    }


    @ApiOperation("下载公差限导入模板")
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<SPEC_INF_EXCEL_VO> util = new ExcelUtil<>(SPEC_INF_EXCEL_VO.class);
        util.importTemplateExcel(response, "公差限数据");
    }

    /**
     * @param file
     * @param status 1:重复跳过 2:重复覆盖
     * @return
     * @throws Exception
     */
    @NotResubmit
    @ApiOperation("导入公差限信息")
    @Log(title = "公差限管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<?> importData(@RequestPart("file") MultipartFile file, Integer status) throws Exception {
        try (InputStream inputStream = file.getInputStream()) {
            ExcelUtil<SPEC_INF_EXCEL_VO> util = new ExcelUtil<>(SPEC_INF_EXCEL_VO.class);
            List<SPEC_INF_EXCEL_VO> specInfExcelVoList = util.importExcel(inputStream);
            if (status == null) status = 0;
            Map<String, String> map = specInfService.importSpec(specInfExcelVoList, status);
            return R.ok(map);
        } catch (Exception e) {
            log.error("导入公差限信息失败");
            e.printStackTrace();
        }
        return R.fail("导入失败");
    }

    /**
     * 外部调用新增
     */
    @Log(title = "公差限管理", businessType = BusinessType.INSERT)
    @ApiOperation("外部调用新增公差限信息")
    @PostMapping("/externalAdd")
    public R<?> externalAdd(@RequestBody SPEC_INF_VO specInfVo) {
        add(specInfVo);
        return R.ok();
    }

    /**
     * 外部调用修改
     */
    @Log(title = "公差限管理", businessType = BusinessType.INSERT)
    @ApiOperation("外部调用修改公差限信息")
    @PostMapping("/externalEdit")
    public R<?> externalEdit(@RequestBody SPEC_INF_VO specInfVo) {
        edit(specInfVo);
        return R.ok();
    }
}
