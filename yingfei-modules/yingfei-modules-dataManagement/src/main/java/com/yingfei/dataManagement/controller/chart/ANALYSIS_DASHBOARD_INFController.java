package com.yingfei.dataManagement.controller.chart;

import com.alibaba.fastjson2.JSONArray;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.PARAMETER_SET_INFService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_INFService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_INF;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_TEMPLATE_INF;
import com.yingfei.entity.domain.PARAMETER_SET_INF;
import com.yingfei.entity.dto.ANALYSIS_DASHBOARD_INF_DTO;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.vo.ANALYSIS_DASHBOARD_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "分析页面关联API")
@RestController
@RequestMapping("/analysis_dashboard_inf")
public class ANALYSIS_DASHBOARD_INFController extends BaseController {

    @Resource
    private ANALYSIS_DASHBOARD_INFService analysisDashboardInfService;
    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private PARAMETER_SET_INFService parameterSetInfService;

    /**
     * 获取分析页面关联列表
     */
    @ApiOperation("获取分析页面关联列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody ANALYSIS_DASHBOARD_INF_VO analysisDashboardInfVo) {
        List<ANALYSIS_DASHBOARD_INF_DTO> list = analysisDashboardInfService.getList(analysisDashboardInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(analysisDashboardInfService.getTotal(analysisDashboardInfVo));
        return dataTable;
    }

    /**
     * 新增分析页面关联
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:analysisDashboardInf:add")
    @Log(title = "分析页面关联管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增分析页面关联")
    @PostMapping("/add")
    public R<?> add(@RequestBody ANALYSIS_DASHBOARD_INF_VO analysisDashboardInfVo) {
        analysisDashboardInfService.checkParam(analysisDashboardInfVo);
        analysisDashboardInfService.add(analysisDashboardInfVo);
        return R.ok();
    }

    /**
     * 修改分析页面关联
     */
    @RequiresPermissions("dataManagement:analysisDashboardInf:edit")
    @Log(title = "分析页面关联管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改分析页面关联")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody ANALYSIS_DASHBOARD_INF_VO analysisDashboardInfVo) {
        analysisDashboardInfService.checkParam(analysisDashboardInfVo);
        analysisDashboardInfService.edit(analysisDashboardInfVo);
        return R.ok();
    }

    /**
     * 批量删除分析页面关联
     */
    @RequiresPermissions("dataManagement:analysisDashboardInf:remove")
    @Log(title = "分析页面关联管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除分析页面关联")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        analysisDashboardInfService.del(ids);
        return R.ok();
    }

    /**
     * 通过菜单id获取对应关联图表
     */
    @ApiOperation("通过菜单id获取对应关联图表")
    @GetMapping("/findByMenuId/{menuId}")
    public R<?> findByMenuId(@PathVariable Long menuId) {
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = analysisDashboardInfService.findByMenuId(menuId);
        ANALYSIS_DASHBOARD_TEMPLATE_INF analysisDashboardTemplateInf = analysisDashboardTemplateInfService.getById(analysisDashboardInf.getF_ADTI());
        List<AnalysisChartConfigDTO> analysisChartConfigDTOS = JSONArray.parseArray(analysisDashboardTemplateInf.getF_DATA(), AnalysisChartConfigDTO.class);
        return R.ok(analysisChartConfigDTOS);
    }

    /**
     * 通过menuId获取对应信息
     */
    @ApiOperation("通过menuId获取对应信息")
    @GetMapping("/getInfo/{menuId}")
    public R<?> getInfo(@PathVariable Long menuId) {
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = analysisDashboardInfService.findByMenuId(menuId);
        ANALYSIS_DASHBOARD_INF_DTO analysisDashboardInfDto = new ANALYSIS_DASHBOARD_INF_DTO();
        BeanUtils.copyPropertiesIgnoreNull(analysisDashboardInf,analysisDashboardInfDto);
        ANALYSIS_DASHBOARD_TEMPLATE_INF analysisDashboardTemplateInf = analysisDashboardTemplateInfService.getById(analysisDashboardInf.getF_ADTI());
        analysisDashboardInfDto.setTemplateName(analysisDashboardTemplateInf.getF_NAME());
        analysisDashboardInfDto.setTemplateConfig(analysisDashboardTemplateInf.getF_CONFIG());
        PARAMETER_SET_INF parameterSetInf = parameterSetInfService.getById(analysisDashboardInf.getF_PRST());
        analysisDashboardInfDto.setParameterName(parameterSetInf.getF_NAME());
        analysisDashboardInfDto.setMaxNum(parameterSetInf.getF_MAX_ITEM());
        return R.ok(analysisDashboardInfDto);
    }

    /**
     * 获取已绑定菜单
     */
    @ApiOperation("获取已绑定菜单")
    @GetMapping("/findMenu")
    public R<?> findMenu() {
        List<ANALYSIS_DASHBOARD_INF> analysisDashboardInfList = analysisDashboardInfService.list();
        List<Long> list = analysisDashboardInfList.stream().map(ANALYSIS_DASHBOARD_INF::getF_MENU).collect(Collectors.toList());
        return R.ok(list);
    }
}
