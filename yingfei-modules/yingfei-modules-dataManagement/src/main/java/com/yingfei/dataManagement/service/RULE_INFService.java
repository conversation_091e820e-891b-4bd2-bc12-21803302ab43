package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.RULE_INF;
import com.yingfei.entity.dto.RULE_INF_DTO;
import com.yingfei.entity.vo.RULE_INF_VO;

/**
* <AUTHOR>
* @description 针对表【RULE_INF(储存报警规则信息表)】的数据库操作Service
* @createDate 2024-05-08 16:28:32
*/
public interface RULE_INFService extends IService<RULE_INF>, BaseService<RULE_INF_VO, RULE_INF_DTO> {

    Integer getMaxCnt();
}
