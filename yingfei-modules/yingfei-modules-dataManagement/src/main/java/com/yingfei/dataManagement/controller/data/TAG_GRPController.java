package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.TAG_GRPService;
import com.yingfei.entity.domain.TAG_GRP;
import com.yingfei.entity.dto.TAG_GRP_DTO;
import com.yingfei.entity.vo.TAG_GRP_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "标签组信息API")
@RestController
@RequestMapping("/tag_grp")
public class TAG_GRPController extends BaseController {
    
    @Resource
    private TAG_GRPService tagGrpService;

    /**
     * 获取标签组信息列表
     */
    @ApiOperation("获取标签组信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody TAG_GRP_VO tagGrpVo) {
        List<TAG_GRP_DTO> list = tagGrpService.getList(tagGrpVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(tagGrpService.getTotal(tagGrpVo));
        return dataTable;
    }

    /**
     * 新增标签组信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:tagGrp:add")
    @Log(title = "标签组管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增标签组信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody TAG_GRP_VO tagGrpVo) {
        tagGrpService.checkParam(tagGrpVo);
        tagGrpService.add(tagGrpVo);
        return R.ok();
    }

    /**
     * 修改标签组信息
     */
    @RequiresPermissions("dataManagement:tagGrp:edit")
    @Log(title = "标签组管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改标签组信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody TAG_GRP_VO tagGrpVo) {
        tagGrpService.checkParam(tagGrpVo);
        TAG_GRP tagGrp = new TAG_GRP();
        BeanUtils.copyPropertiesIgnoreNull(tagGrpVo, tagGrp);
        tagGrpService.updateById(tagGrp);
        return R.ok();
    }

    /**
     * 批量删除标签组信息
     */
    @RequiresPermissions("dataManagement:tagGrp:remove")
    @Log(title = "标签组管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除标签组信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        tagGrpService.del(ids);
        return R.ok();
    }

    /**
     * 标签组列表对应标签
     */
    @ApiOperation("标签组列表对应标签")
    @PostMapping("/tagGrpTreeList")
    public R<?> tagGrpTreeList(@RequestBody TAG_GRP_VO tagGrpVo){
        List<TAG_GRP_DTO> list = tagGrpService.tagGrpTreeList(tagGrpVo);
        return R.ok(list);
    }
}
