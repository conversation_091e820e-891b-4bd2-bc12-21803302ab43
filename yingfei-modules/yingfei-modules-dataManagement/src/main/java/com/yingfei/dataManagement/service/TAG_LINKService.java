package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.TAG_LINK;
import com.yingfei.entity.dto.TAG_DAT_DTO;
import com.yingfei.entity.dto.TAG_GRP_DTO;
import com.yingfei.entity.dto.TAG_LINK_DTO;
import com.yingfei.entity.vo.TAG_LINK_VO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【TAG_LINK(储存所有基础信息与Tag的关联关系)】的数据库操作Service
 * @createDate 2024-05-08 16:29:00
 */
public interface TAG_LINKService extends IService<TAG_LINK>, BaseService<TAG_LINK_VO, TAG_LINK_DTO> {

    Integer getCount(Long resourceId, int type);

    void saveByType(Long resourceId, List<TAG_DAT_DTO> tagDatDtoList, int code);

    List<TAG_LINK> findByResourceIdAndType(Long resourceId, int code);

    /**
     * 获取动态查询条件
     *
     * @param isInclude 是否包含
     * @param code      类别
     * @param tagList  标签id列表
     * @return
     */
    List<TAG_LINK> getSearchCondition(Integer isInclude, int code, List<Long> tagList);

    List<TAG_GRP_DTO> findByHierList(TAG_LINK_VO tagLinkVo);

    void delByType(List<Long> resourceIds, int code);

    void addByType(List<Long> partIds, List<TAG_DAT_DTO> tagDatDtoList, int code);
}
