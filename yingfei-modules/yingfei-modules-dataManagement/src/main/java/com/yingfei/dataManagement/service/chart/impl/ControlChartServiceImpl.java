package com.yingfei.dataManagement.service.chart.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.config.ThreadPoolConfig;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.mapper.ACTIVED_RULE_TEMPLATE_INFMapper;
import com.yingfei.dataManagement.mapper.PROCESSING_TEMPLATE_INFMapper;
import com.yingfei.dataManagement.mapper.RULE_INFMapper;
import com.yingfei.dataManagement.service.EVNT_INFService;
import com.yingfei.dataManagement.service.SGRP_INFService;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.dataManagement.service.TEST_INFService;
import com.yingfei.dataManagement.service.bpm.BpmProcessInstanceService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.ControlChartService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.ControlChartDTO;
import com.yingfei.entity.enums.*;
import com.yingfei.entity.util.ControlChartCalculateService;
import com.yingfei.entity.util.HistogramUtil;
import com.yingfei.entity.util.WarningRuleService;
import com.yingfei.entity.vo.ControlLimitVO;
import com.yingfei.entity.vo.DataPointVO;
import com.yingfei.entity.vo.EVNT_INF_VO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ControlChartServiceImpl implements ControlChartService {

    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private WarningRuleService warningRuleService;
    @Resource
    private TEST_INFService testInfService;
    @Resource
    private RedisService redisService;
    @Resource
    private PROCESSING_TEMPLATE_INFMapper processingTemplateInfMapper;
    @Resource
    private ACTIVED_RULE_TEMPLATE_INFMapper activedRuleTemplateInfMapper;
    @Resource
    private RULE_INFMapper ruleInfMapper;
    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private ChartCommonService chartCommonService;
    @Resource
    private EVNT_INFService evntInfService;
    @Resource
    private BpmProcessInstanceService bpmProcessInstanceService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    public ControlChartDTO getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.CONTROL_CHART);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }
        ControlChartDTO controlChartDTO = new ControlChartDTO();
        /*获取缓存查询条件*/
        /*当从聚合分析图表点入*/
        Integer isTopOne = analysisChartConfigDTO.getIsTopOne();
        if (isTopOne.equals(YesOrNoEnum.YES.getType())) {
            analysisChartConfigDTO.setIsTopOne(YesOrNoEnum.NO.getType());
        }
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);

        if (subgroupDataSelectionDTO == null)
            return controlChartDTO;


        analysisChartConfigDTO.setIsTopOne(isTopOne);
        /*获取子组测试数量*/
        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) {
            return controlChartDTO;
        }
        return getControlChartDTO(subgroupDataSelectionDTO, subgroupDataDTOList, controlChartDTO, analysisChartConfigDTO);
    }


    private ControlChartDTO getControlChartDTO(SubgroupDataSelectionDTO subgroupDataSelectionDTO, List<SubgroupDataDTO> subgroupDataDTOList, ControlChartDTO controlChartDTO, AnalysisChartConfigDTO analysisChartConfigDTO) {

        subgroupDataDTOList = chartCommonService.getTotalNumSubgroup(subgroupDataDTOList, subgroupDataSelectionDTO.getMaxNum(),2);

        /*通过产品,过程,测试*/
        List<SubgroupDataDTO> reassemblySubgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);
        /*当从聚合分析图表点入重新过滤*/
        if (analysisChartConfigDTO.getIsTopOne().equals(YesOrNoEnum.YES.getType())) {
            SubgroupDataSelectionDTO finalSubgroupDataSelectionDTO = subgroupDataSelectionDTO;
            reassemblySubgroupDataDTOList = reassemblySubgroupDataDTOList.stream().filter(s ->
                    s.getF_PART().equals(finalSubgroupDataSelectionDTO.getF_PART()) &&
                            s.getF_PRCS().equals(finalSubgroupDataSelectionDTO.getF_PRCS()) &&
                            s.getF_TEST().equals(finalSubgroupDataSelectionDTO.getF_TEST())
            ).collect(Collectors.toList());
        }
        /*通过产品,过程,测试获取过程事件*/
        EVNT_INF_VO evntInfVo = new EVNT_INF_VO();
        evntInfVo.setF_PART(subgroupDataSelectionDTO.getF_PART())
                .setF_PRCS(subgroupDataSelectionDTO.getF_PRCS())
                .setF_TEST(subgroupDataSelectionDTO.getF_TEST());
        evntInfVo.setDbType(InitConfig.getDriverType());
        List<EVNT_INF_DTO> evntInfDtoList = evntInfService.getList(evntInfVo);
        Map<Long, List<EVNT_INF_DTO>> evntMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(evntInfDtoList)) {
            evntInfDtoList.forEach(evntInfDto -> {
                LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_EVNT, evntInfDto.getF_EVNT());
                List<BPM_PROCESS_INSTANCE> list = bpmProcessInstanceService.list(queryWrapper);
                if (CollectionUtils.isNotEmpty(list)) {
                    evntInfDto.setBpmProcessInstanceList(list);
                }
            });
            evntMap = evntInfDtoList.stream().collect(Collectors.groupingBy(EVNT_INF_DTO::getF_SGRP));
        }

        controlChartDTO.setSubgroupDataDTO(reassemblySubgroupDataDTOList.get(0));

        /*计算过程能力的时候排除失效子组*/
        List<SubgroupDataDTO> excludeList = reassemblySubgroupDataDTOList.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType()).collect(Collectors.toList());
        DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(excludeList, analysisChartConfigDTO.getType(), 0);
        /*获取目标和超公差数据*/
        DataSummaryDTO.getBasicTwo(dataSummaryDTO);
        /*获取过程潜力指数*/
        DataSummaryDTO.getBasicThree(dataSummaryDTO);
        /*获取过程能力指数*/
        DataSummaryDTO.getBasicFour(dataSummaryDTO);

        /*获取公差限数据*/
        SPEC_INF_DTO specInfDto = specInfService.getSpecLim(dataSummaryDTO, controlChartDTO.getSubgroupDataDTO());
        /*判断测试是否为缺陷或者不良*/
        TEST_INF testInf = testInfService.getById(controlChartDTO.getSubgroupDataDTO().getSgrpValDtoList().get(0).getF_TEST());
        if (testInf.getF_TYPE().equals(TEST_INF_TYPEEnum.VARIABLE.getType())) {
            /*获取Cp*/
            dataSummaryDTO.setCp(HistogramUtil.getCp(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                    dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));
            /*获取Cpk*/
            dataSummaryDTO.setCpk(HistogramUtil.getCpk(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                    dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));
            /*获取Pp*/
            dataSummaryDTO.setPp(HistogramUtil.getPp(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                    dataSummaryDTO.getMean(), dataSummaryDTO.getLongTermStandardDeviation()));
            /*获取Ppk*/
            dataSummaryDTO.setPpk(HistogramUtil.getPpk(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                    dataSummaryDTO.getMean(), dataSummaryDTO.getLongTermStandardDeviation()));
        }

        /*获取报警规则*/
        List<RULE_INF> ruleInfs = ruleInfMapper.selectList(new LambdaQueryWrapper<RULE_INF>().eq(RULE_INF::getF_DEL, DelFlagEnum.USE.getType()));

        /*获取控制限*/
        List<CTRL_INF_DTO> ctrlInfDtoList = redisService.getCacheList(RedisConstant.CTRL_KEY +
                controlChartDTO.getSubgroupDataDTO().getF_PART() + Constants.COMMA +
                controlChartDTO.getSubgroupDataDTO().getF_REV() + Constants.COMMA +
                controlChartDTO.getSubgroupDataDTO().getF_PRCS() + Constants.COMMA +
                controlChartDTO.getSubgroupDataDTO().getSgrpValDtoList().get(0).getF_TEST());

        /*获取控制图类型*/
        ControlChartTypeEnum chartTypeEnum;
        PROCESSING_TEMPLATE_INF processingTemplateInf = null;
        ACTIVED_RULE_TEMPLATE_INF activedRuleTemplateInf = null;
        if (CollectionUtils.isNotEmpty(ctrlInfDtoList)) {
            /*按生效时间排序*/
            ctrlInfDtoList = ctrlInfDtoList.stream()
                    .sorted(Comparator.comparing(CTRL_INF_DTO::getF_EFTM)).collect(Collectors.toList());

            controlChartDTO.setType(ctrlInfDtoList.get(0).getF_CHART_TYPE());
            /*取第一个控制限的图表类型*/
            chartTypeEnum = ControlChartTypeEnum.getEnumByType(ctrlInfDtoList.get(0).getF_CHART_TYPE());
            /*获取数据处理方式*/
            processingTemplateInf = processingTemplateInfMapper.selectById(ctrlInfDtoList.get(0).getF_PSTP());
            /*获取报警方式模板*/
            activedRuleTemplateInf = activedRuleTemplateInfMapper.selectById(ctrlInfDtoList.get(0).getF_ARTP());
        } else {
            controlChartDTO.setType(ControlChartTypeEnum.getType(dataSummaryDTO.getSubGroupSize(),
                    dataSummaryDTO.getSubTestNum(), testInf.getF_TYPE()));
            if (testInf.getF_TYPE().equals(TEST_INF_TYPEEnum.DEFECT.getType()) ||
                    testInf.getF_TYPE().equals(TEST_INF_TYPEEnum.DEFECTIVE.getType())) {
                DataSummaryDTO.recalculateMean(dataSummaryDTO, subgroupDataDTOList);
            }
            chartTypeEnum = ControlChartTypeEnum.getEnumByType(controlChartDTO.getType());
            ctrlInfDtoList = new ArrayList<>();
            /*创建一条控制限缓存记录*/
            CTRL_INF_DTO ctrlInfDto = new CTRL_INF_DTO();

            ctrlInfDto.setLimitType(1)
                    .setF_CHART_TYPE(controlChartDTO.getType())
                    .setF_PART(subgroupDataSelectionDTO.getF_PART())
                    .setF_PRCS(subgroupDataSelectionDTO.getF_PRCS())
                    .setF_TEST(subgroupDataSelectionDTO.getF_TEST())
                    .setF_MEAN(dataSummaryDTO.getMean())
                    .setF_SW(0d)
                    .setF_EFTM(DateUtils.parseDate(Constants.DEFAULT_DATE))
                    .setF_SP(dataSummaryDTO.getShortTermStandardDeviation());
            if (chartTypeEnum != null && (
                    chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_IX_MR_RW) ||
                            chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_IX_MR_SDW) ||
                            chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_X_R_SDW) ||
                            chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_X_R_RW) ||
                            chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_X_SD_RW) ||
                            chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_X_SD_SDW))) {
                List<SGRP_VAL_DTO> sgrpValDtoList = reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).collect(Collectors.toList());
                Integer subTestNum = sgrpValDtoList.stream().map(SGRP_VAL_DTO::getF_SBSZ).reduce(Integer::sum).orElse(1);
                double subTestSize = BigDecimal.valueOf(subTestNum).divide(BigDecimal.valueOf(dataSummaryDTO.getSubGroupNum()), 4, RoundingMode.DOWN).doubleValue();
                ctrlInfDto.setF_SW(HistogramUtil.getWithingShortTermStandardDeviation(subTestSize, sgrpValDtoList, ShortSdTermTypeEnum.AUTO.getType()));
                controlChartDTO.setF_SW(ctrlInfDto.getF_SW());
            }

            if (dataSummaryDTO.getSubTestNum() > 1) {
                //todo 暂未实现
            }
            ctrlInfDtoList.add(ctrlInfDto);
        }
        if (chartTypeEnum == null) {
            log.error("获取控制图类型失败");
            return null;
        }

        int size = ctrlInfDtoList.size();

        Map<Integer, List<ControlLimitVO>> controlLimit = new HashMap<>();

        /*图类型对应的报警列表*/
        Map<Integer, List<String>> chartRule = new HashMap<>();


        //控制限计算（基于数据库中控制限记录）DTO
        ControlLimitDTO controlLimitDto = new ControlLimitDTO();
        controlLimitDto.setN(dataSummaryDTO.getSubGroupSize());
        controlLimitDto.setSpecInfDto(specInfDto);
        /*图表序号*/
        int n = 1;
        for (ControlChartSingleEnum chartSingleEnum : chartTypeEnum.getGroupType()) {
            long startTime = size == 0 ? 0L : ctrlInfDtoList.get(0).getF_EFTM().getTime();
            long endTime = 0L;
            if (size > 1) {
                endTime = ctrlInfDtoList.get(1).getF_EFTM().getTime();
            }
            /*极差件内图和标准差件内图是要用子测试数量来作为样本量*/
            if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_SDW ||
                    chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_RW) {
                controlLimitDto.setN(Double.valueOf(dataSummaryDTO.getSubTestNum()));
            } else if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_P ||
                    chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_U) {
                controlLimitDto.setN(Double.valueOf(reassemblySubgroupDataDTOList.get(0).getF_SGSZ()));
            } else {
                controlLimitDto.setN(dataSummaryDTO.getSubGroupSize());
            }

            /*获取激活的报警规则*/
            List<String> idList = new ArrayList<>();
            if (activedRuleTemplateInf != null) {
                if (n == 1) {
                    if (StringUtils.isNotEmpty(activedRuleTemplateInf.getF_CHART_ONE())) {
                        idList = JSONArray.parseArray(activedRuleTemplateInf.getF_CHART_ONE(), String.class);
                    }
                } else if (n == 2) {
                    if (StringUtils.isNotEmpty(activedRuleTemplateInf.getF_CHART_TWO())) {
                        idList = JSONArray.parseArray(activedRuleTemplateInf.getF_CHART_TWO(), String.class);
                    }
                } else {
                    if (StringUtils.isNotEmpty(activedRuleTemplateInf.getF_CHART_THREE())) {
                        idList = JSONArray.parseArray(activedRuleTemplateInf.getF_CHART_THREE(), String.class);
                    }
                }
            } else {
                idList = new ArrayList<>();
            }
            n++;
            List<RULE_INF> ruleInfList = null;
            if (CollectionUtils.isNotEmpty(idList)) {
                List<String> finalIdList = idList;
                ruleInfList = ruleInfs.stream().filter(s -> finalIdList.contains(s.getF_ALR().toString())).collect(Collectors.toList());
            }
            List<ControlLimitVO> controlLimitVOS = new ArrayList<>();
            ControlChartCalculateService controlChartCalculateService = null;
            try {
                Class<?> aClass = Class.forName(chartSingleEnum.getClassPath());
                controlChartCalculateService = (ControlChartCalculateService) aClass.getDeclaredConstructor().newInstance();

                /*根据reassemblySubgroupDataDTOList 的F_SGTM字段正序排列*/
                /*获取数据点*/
                List<SubgroupDataDTO> list = reassemblySubgroupDataDTOList.stream()
                        .sorted(Comparator.comparing(SubgroupDataDTO::getF_SGTM).thenComparing(SubgroupDataDTO::getF_SGRP))
                        .collect(Collectors.toList());

                List<DataPointVO> doubles = controlChartCalculateService.dataPoint(list, chartTypeEnum);

                /*如果控制限表没有数据则只计算数据点*/
                if (size > 0) {
                    int startNum = 0;
                    for (int i = 0; i < size; i++) {
                        CTRL_INF_DTO ctrlLim = ctrlInfDtoList.get(i);
                        if (i > 0) {
                            startTime = endTime;
                            if (i + 1 < size) {
                                endTime = ctrlInfDtoList.get(i + 1).getF_EFTM().getTime();
                            } else {
                                endTime = 0L;
                            }
                        }
                        controlLimitDto.setF_MEAN(ctrlLim.getF_MEAN());
                        controlLimitDto.setF_SW(ctrlLim.getF_SW());
                        controlLimitDto.setF_SP(ctrlLim.getF_SP());
                        controlLimitDto.setF_SPL(ctrlLim.getF_SPL());
                        Double cl = controlChartCalculateService.controlLimitCL(controlLimitDto);
                        controlLimitDto.setCL(cl);
                        controlLimitDto.setF_SIGMA_COUNT(processingTemplateInf == null ? 3d : processingTemplateInf.getF_SIGMA_COUNT());
                        ControlLimitVO controlLimitVO = new ControlLimitVO();
                        controlLimitVO.setLimitType(ctrlLim.getLimitType());
                        controlLimitVO.setCl(cl);
                        controlLimitVO.setUcl(controlChartCalculateService.controlLimitUCL(controlLimitDto));
                        controlLimitVO.setLcl(controlChartCalculateService.controlLimitLCL(controlLimitDto));

                        /*数据点标准化处理*/
                        if (processingTemplateInf != null) {
                            STANDARDIZE_TYPEEnum.dispose(controlLimitDto, controlLimitVO, STANDARDIZE_TYPEEnum.getType(processingTemplateInf.getF_STANDARDIZE_TYPE()), chartSingleEnum);
                        }

                        Map<Integer, List<DataPointVO>> map = new HashMap<>();
                        final Integer[] type = {0};
                        final Double[] d = {0D};
                        List<DataPointVO> dataPointVOList = new ArrayList<>();
                        /*获取数据限区间的数据点*/
                        if (size > 1) {
                            long finalStartTime = startTime;
                            long finalEndTime = endTime;

                            List<CTRL_INF_DTO> finalCtrlInfDtoList = ctrlInfDtoList;
                            List<SubgroupDataDTO> collect = list.stream().filter(x -> finalEndTime > 0L ?
                                    x.getF_SGTM().getTime() >= finalStartTime && x.getF_SGTM().getTime() < finalEndTime :
                                    x.getF_SGTM().getTime() >= finalStartTime).collect(Collectors.toList());
                            /*当第一条控制限生效时间在子组时间外时 会出现控制图数据点对不上 2025-2-18改为不在控制限范围内的按第一条控制限显示*/
                            if (i == 0) {
                                List<SubgroupDataDTO> dtoList = list.stream().filter(x -> x.getF_SGTM().getTime() <
                                        finalCtrlInfDtoList.get(0).getF_EFTM().getTime()).collect(Collectors.toList());
                                /*如有在第一条控制限前面的子组则加在第一条控制限上*/
                                if (CollectionUtils.isNotEmpty(dtoList)) {
                                    dtoList.addAll(collect);
                                    collect = dtoList;
                                }
                            }
                            List<SGRP_VAL_DTO> sgrpValDtoList = collect.stream().map(SubgroupDataDTO::getSgrpValDtoList)
                                    .flatMap(List::stream).collect(Collectors.toList());
                            int num = sgrpValDtoList.size();
                            dataPointVOList = CollectionUtil.sub(doubles, startNum, num + startNum);
                            startNum = num;
                            controlLimitVO.setDataPointVOList(dataPointVOList);
                        } else {
                            dataPointVOList = doubles;
                            controlLimitVO.setDataPointVOList(doubles);
                        }

                        dataPointVOList.forEach(dataPointVO -> {
                            if (dataPointVO.getSubgroupInfoDTO() == null) {
                                return;
                            }
                            if (d[0] == 0D && dataPointVO.getSubgroupInfoDTO().getSample().getF_SGSZ() != null) {
                                d[0] = Double.valueOf(dataPointVO.getSubgroupInfoDTO().getSample().getF_SGSZ());
                                dataPointVO.setNum(type[0]);
                            } else {
                                if (d[0].equals(Double.valueOf(dataPointVO.getSubgroupInfoDTO().getSample().getF_SGSZ()))) {
                                    dataPointVO.setNum(type[0]);
                                } else {
                                    type[0]++;
                                    d[0] = Double.valueOf(dataPointVO.getSubgroupInfoDTO().getSample().getF_SGSZ());
                                    dataPointVO.setNum(type[0]);
                                }
                            }
                        });
                        map = dataPointVOList.stream().filter(s -> s.getNum() != null)
                                .collect(Collectors.groupingBy(DataPointVO::getNum));
                        /*判断每个子组的样本量是否一致,不一致则重新计算cl,ucl,lcl*/
                        if (map.size() > 1) {
                            for (Integer num : map.keySet()) {
                                List<DataPointVO> dataPointVOS = map.get(num);
                                ControlLimitDTO con = new ControlLimitDTO();
                                BeanUtils.copyPropertiesIgnoreNull(controlLimitDto, con);
                                con.setN(Double.valueOf(dataPointVOS.get(0).getSubgroupInfoDTO().getSample().getF_SGSZ()));
                                cl = controlChartCalculateService.controlLimitCL(con);
                                con.setCL(cl);
                                ControlLimitVO vo = new ControlLimitVO();
                                vo.setCl(cl);
                                vo.setUcl(controlChartCalculateService.controlLimitUCL(con));
                                vo.setLcl(controlChartCalculateService.controlLimitLCL(con));
                                vo.setDataPointVOList(dataPointVOS);
                                controlLimitVOS.add(vo);
                            }
                        } else {
                            controlLimitVOS.add(controlLimitVO);
                        }
                    }
                } else {
                    ControlLimitVO controlLimitVO = new ControlLimitVO();
                    controlLimitVO.setDataPointVOList(doubles);
                    controlLimitVOS.add(controlLimitVO);
                }
                /*判断控制限报警规则*/
                if (CollectionUtils.isNotEmpty(ruleInfList)) {
                    List<String> ruleList = warningRuleService.judgeWarningRule(ruleInfList, controlLimitVOS, dataSummaryDTO, specInfDto);
                    if (CollectionUtils.isNotEmpty(ruleList)) {
                        chartRule.put(chartSingleEnum.getType(), ruleList);
                    }
                }
                controlLimit.put(chartSingleEnum.getType(), controlLimitVOS);
            } catch (Exception e) {
                log.error("获取控制限失败");
                e.printStackTrace();
                return null;
            }
        }

        controlChartDTO.setControlLimit(controlLimit);
        /*清空缓存数据*/
        dataSummaryDTO.setValList(null);
        controlChartDTO.setDataSummary(dataSummaryDTO);
        controlChartDTO.setChartRule(chartRule);
        controlChartDTO.setSpecInfDto(specInfDto);
        controlChartDTO.setEvntMap(evntMap);
        return controlChartDTO;
    }


    @Override
    public List<ControlChartDTO> getInfoList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.CONTROL_CHART);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }

        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);

        if (CollectionUtils.isEmpty(subgroupDataDTOList)) {
            return new ArrayList<>();
        }

        /*多测试拆分*/
        List<SubgroupDataDTO> reassemblySubgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);

        final List<Map<String, Long>> queryMapList = chartCommonService.generateCombinations(
                subgroupDataSelectionDTO.getPartList(),
                subgroupDataSelectionDTO.getPtrvList(),
                subgroupDataSelectionDTO.getPrcsList(),
                subgroupDataSelectionDTO.getTestList());
        List<ControlChartDTO> list = new ArrayList<>();

        List<Future<ControlChartDTO>> futures = new ArrayList<>();

        for (Map<String, Long> map : queryMapList) {
            // 进行空值检查
            if (map == null) {
                continue;
            }
            Long fpart = map.get(Constants.part);
            Long fptrv = map.get(Constants.ptrv);
            Long fprcs = map.get(Constants.prcs);
            Long ftest = map.get(Constants.test);
            // 过滤符合条件的 SubgroupDataDTO 列表
            final List<SubgroupDataDTO> collect = reassemblySubgroupDataDTOList.stream()
                    .filter(x -> fpart != null && fpart.equals(x.getF_PART()) &&
                            fptrv != null && fptrv.equals(x.getF_REV()) &&
                            fprcs != null && fprcs.equals(x.getF_PRCS()) &&
                            ftest != null && ftest.equals(x.getF_TEST()))
                    .collect(Collectors.toList());

            // 当 collect 列表为空时，跳过当前循环
            if (CollectionUtils.isEmpty(collect)) {
                continue;
            }
            subgroupDataSelectionDTO.setF_PART(fpart).setF_REV(fptrv).setF_PRCS(fprcs).setF_TEST(ftest);
            // 提交任务到线程池
            SubgroupDataSelectionDTO finalSubgroupDataSelectionDTO = subgroupDataSelectionDTO;
            futures.add(threadPoolConfig.threadPoolTaskExecutor().submit(() -> getControlChartDTO(finalSubgroupDataSelectionDTO, collect, new ControlChartDTO(), analysisChartConfigDTO)));
        }
        // 获取任务结果
        for (Future<ControlChartDTO> future : futures) {
            try {
                ControlChartDTO dto = future.get();
                if (dto != null) {
                    list.add(dto);
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("多线程处理出错", e);
            }
        }
        return list;
    }


    @Override
    public DataSummaryDTO distribution(SubgroupDataDTO subgroupDataDTO) {
        List<SubgroupDataDTO> subgroupDataDTOList = Collections.singletonList(subgroupDataDTO);
        DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(subgroupDataDTOList, 1, 0);
        /*获取公差限数据*/
        SPEC_INF_DTO specInfDto = specInfService.getSpecLim(dataSummaryDTO, subgroupDataDTO);
        DataSummaryDTO.getBasicThree(dataSummaryDTO);
        DataSummaryDTO.getBasicFour(dataSummaryDTO);
        return dataSummaryDTO;
    }
}
