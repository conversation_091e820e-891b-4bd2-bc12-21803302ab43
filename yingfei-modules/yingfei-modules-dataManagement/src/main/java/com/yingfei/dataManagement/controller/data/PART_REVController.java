package com.yingfei.dataManagement.controller.data;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.service.PART_REVService;
import com.yingfei.entity.domain.PART_REV;
import com.yingfei.entity.dto.globalConfig.SysyemGlobalConfig;
import com.yingfei.entity.vo.PART_REV_VO;
import com.yingfei.system.api.RemoteGlobalConfigInfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
@Slf4j
@Api(tags = "产品版本信息API")
@RestController
@RequestMapping("/part_rev")
public class PART_REVController extends BaseController {

    @Resource
    private RedisService redisService;
    @Resource
    private PART_REVService partRevService;
    @Resource
    private RemoteGlobalConfigInfService remoteGlobalConfigInfService;

    /**
     * 获取产品版本
     */
    @ApiOperation("获取产品版本")
    @PostMapping("/getInfo")
    public R<?> getInfo() {
        SysyemGlobalConfig systemConfig = null;
        try {
            systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
        } catch (Exception e) {
            log.error("获取系统配置信息失败 ex:{}",e.getMessage(), e);
            throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
        }
        final String partRevName = systemConfig.getBasicConfig().getPartRevName();
        return R.ok(partRevName);
    }


//    /**
//     * 修改产品版本信息
//     */
//    @RequiresPermissions("dataManagement:partInf:edit")
//    @Log(title = "产品版本管理", businessType = BusinessType.UPDATE)
//    @ApiOperation("修改产品版本信息")
//    @PostMapping("/edit")
//    public R<?> edit(@RequestBody String partRevName) {
//
//        redisService.set(RedisConstant.PART_REV_NAME, partRevName);
//        return R.ok();
//    }

    /**
     * 获取产品对应版本号列表
     */
    @ApiOperation("获取产品对应版本号列表")
    @PostMapping("/findByPartId")
    public R<?> findByPartId(@RequestBody PART_REV_VO partRevVo) {
        LambdaQueryWrapper<PART_REV> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PART_REV::getF_PART, partRevVo.getF_PART()).eq(PART_REV::getF_DEL, DelFlagEnum.USE.getType())
                .gt(PART_REV::getF_FNTM,partRevVo.getF_FNTM());
        List<PART_REV> list = partRevService.list(queryWrapper);
        return R.ok(list);
    }
}
