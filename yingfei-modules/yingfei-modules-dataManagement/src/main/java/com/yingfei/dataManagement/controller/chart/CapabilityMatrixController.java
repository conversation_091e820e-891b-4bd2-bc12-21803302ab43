package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.service.chart.CapabilityMatrixService;
import com.yingfei.dataManagement.service.chart.DataReportService;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.CapabilityMatrixDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@Api(tags = "图表:能力矩阵API")
@RequestMapping("/capabilityMatrix")
public class CapabilityMatrixController {

    @Resource
    private CapabilityMatrixService capabilityMatrixService;

    /**
     * 获取箱线图详情
     *
     * @return
     */
    @PostMapping("/info")
    @ApiOperation("获取能力矩阵详情")
    public R<?> getInfo(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        List<CapabilityMatrixDTO> list = capabilityMatrixService.getInfo(subgroupDataSelectionDTO);
        return R.ok(list);
    }


}
