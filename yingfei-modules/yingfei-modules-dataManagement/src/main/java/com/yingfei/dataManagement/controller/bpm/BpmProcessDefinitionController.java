package com.yingfei.dataManagement.controller.bpm;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.service.bpm.BpmProcessDefinitionService;
import com.yingfei.entity.vo.BPM_PROCESS_DEFINITION_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@Api(tags = "工作流程 - 流程定义")
@RestController
@RequestMapping("/bpm/process-definition")
@Validated
public class BpmProcessDefinitionController {

    @Resource
    private BpmProcessDefinitionService bpmDefinitionService;

    @PostMapping("/page")
    @ApiOperation("获得流程定义分页")
    public R<?> getProcessDefinitionPage(@RequestBody BPM_PROCESS_DEFINITION_VO pageReqVO) {
        return R.ok(bpmDefinitionService.getProcessDefinitionPage(pageReqVO));
    }

    @PostMapping ("/list")
    @ApiOperation("获得流程定义列表")
    public R<?> getProcessDefinitionList(@RequestBody BPM_PROCESS_DEFINITION_VO listReqVO) {
        return R.ok(bpmDefinitionService.getProcessDefinitionList(listReqVO));
    }

    @GetMapping ("/get-bpmn-xml")
    @ApiOperation("获得流程定义的 BPMN XML")
    @Parameter(name = "id", description = "流程编号", required = true, example = "1024")
    public R<String> getProcessDefinitionBpmnXML(@RequestParam("id") String id) {
        String bpmnXML = bpmDefinitionService.getProcessDefinitionBpmnXML(id);
        return R.ok(bpmnXML);
    }
}
