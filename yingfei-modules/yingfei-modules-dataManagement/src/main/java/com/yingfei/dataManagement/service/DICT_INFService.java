package com.yingfei.dataManagement.service;

import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.DICT_INF;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.DICT_INF_DTO;
import com.yingfei.entity.vo.DICT_INF_VO;

/**
* @description 针对表【DICT_INF(枚举翻译字典表)】的数据库操作Service
* @createDate 2024-09-19 15:35:37
*/
public interface DICT_INFService extends IService<DICT_INF>, BaseService<DICT_INF_VO, DICT_INF_DTO> {

}
