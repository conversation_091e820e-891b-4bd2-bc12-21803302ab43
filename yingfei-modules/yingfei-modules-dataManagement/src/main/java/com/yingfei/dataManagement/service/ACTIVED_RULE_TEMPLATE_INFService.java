package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.ACTIVED_RULE_TEMPLATE_INF;
import com.yingfei.entity.dto.ACTIVED_RULE_TEMPLATE_INF_DTO;
import com.yingfei.entity.vo.ACTIVED_RULE_TEMPLATE_INF_VO;

/**
* <AUTHOR>
* @description 针对表【ACTIVED_RULE_TEMPLATE_INF(储存报警规则模板信息表)】的数据库操作Service
* @createDate 2024-05-08 16:26:42
*/
public interface ACTIVED_RULE_TEMPLATE_INFService extends IService<ACTIVED_RULE_TEMPLATE_INF>, BaseService<ACTIVED_RULE_TEMPLATE_INF_VO, ACTIVED_RULE_TEMPLATE_INF_DTO> {

    ACTIVED_RULE_TEMPLATE_INF_DTO info(Long id);
}
