
package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.INSPECTION_TYPE_DATService;
import com.yingfei.entity.domain.INSPECTION_TYPE_DAT;
import com.yingfei.entity.dto.INSPECTION_TYPE_DAT_DTO;
import com.yingfei.entity.vo.INSPECTION_TYPE_DAT_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "检验类型信息API")
@RestController
@RequestMapping("/inspection_type_dat")
public class INSPECTION_TYPE_DATController extends BaseController {
    
    @Resource
    private INSPECTION_TYPE_DATService inspectionTypeDatService;

    /**
     * 获取检验类型信息列表
     */
    @ApiOperation("获取检验类型信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody INSPECTION_TYPE_DAT_VO inspectionTypeDatVo) {
        List<INSPECTION_TYPE_DAT_DTO> list = inspectionTypeDatService.getList(inspectionTypeDatVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(inspectionTypeDatService.getTotal(inspectionTypeDatVo));
        return dataTable;
    }

    /**
     * 新增检验类型信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:inspectionType:add")
    @Log(title = "检验类型管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增检验类型信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody INSPECTION_TYPE_DAT_VO inspectionTypeDatVo) {
        inspectionTypeDatService.checkParam(inspectionTypeDatVo);
        inspectionTypeDatService.add(inspectionTypeDatVo);
        return R.ok();
    }

    /**
     * 修改检验类型信息
     */
    @RequiresPermissions("dataManagement:inspectionType:edit")
    @Log(title = "检验类型管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改检验类型信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody INSPECTION_TYPE_DAT_VO inspectionTypeDatVo) {
        inspectionTypeDatService.checkParam(inspectionTypeDatVo);
        INSPECTION_TYPE_DAT inspectionTypeDat = new INSPECTION_TYPE_DAT();
        BeanUtils.copyPropertiesIgnoreNull(inspectionTypeDatVo, inspectionTypeDat);
        inspectionTypeDatService.updateById(inspectionTypeDat);
        return R.ok();
    }

    /**
     * 批量删除检验类型信息
     */
    @RequiresPermissions("dataManagement:inspectionType:remove")
    @Log(title = "检验类型管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除检验类型信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        inspectionTypeDatService.del(ids);
        return R.ok();
    }
}
