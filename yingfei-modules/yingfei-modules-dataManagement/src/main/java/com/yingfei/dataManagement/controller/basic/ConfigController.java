package com.yingfei.dataManagement.controller.basic;

import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.IdGeneratorService;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.RULE_INFService;
import com.yingfei.entity.dto.BasicConfigDTO;
import com.yingfei.entity.dto.globalConfig.BasicConfig;
import com.yingfei.entity.dto.globalConfig.DataCacheConfig;
import com.yingfei.entity.dto.globalConfig.SysyemGlobalConfig;
import com.yingfei.system.api.RemoteGlobalConfigInfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Api(tags = "全局配置信息API")
@RestController
@RequestMapping("/config")
public class ConfigController {

    @Resource
    private RedisService redisService;
    @Resource
    private RULE_INFService ruleInfService;
    @Resource
    private IdGeneratorService idGeneratorService;
    @Resource
    private RemoteGlobalConfigInfService remoteGlobalConfigInfService;

    /**
     * 获取基础配置信息
     */
    @ApiOperation("获取基础配置信息")
    @GetMapping("/getConfigInfo")
    public R<?> getConfigInfo() {
        BasicConfigDTO basicConfigDTO = new BasicConfigDTO();

        SysyemGlobalConfig systemConfig = null;
        try {
            systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
        } catch (Exception e) {
            log.error("获取系统配置信息失败 ex:{}",e.getMessage(), e);
            throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
        }

        final BasicConfig basicConfig = systemConfig.getBasicConfig();
        final DataCacheConfig dataCacheConfig = systemConfig.getDataCacheConfig();
        /*获取产品版本*/
        final String partRevName = basicConfig.getPartRevName();
        basicConfigDTO.setPartRev(partRevName);

        /*获取报警限,合理限*/
        Object warning_limit = redisService.get(RedisConstant.WARNING_LIMIT);
        Object reasonable_limit = redisService.get(RedisConstant.REASONABLE_LIMIT);
        basicConfigDTO.setWarningLimit(warning_limit == null ? String.valueOf(Constants.WARNING_LIMIT) : warning_limit.toString());
        basicConfigDTO.setReasonableLimit(reasonable_limit == null ? String.valueOf(Constants.REASONABLE_LIMIT) : reasonable_limit.toString());

        /*获取子组缓存天数*/
        final Integer subGroupCacheMaxDay = dataCacheConfig.getSubGroupCacheMaxDay();
        basicConfigDTO.setSubGroupCacheMaxDay(subGroupCacheMaxDay == null ? String.valueOf(Constants.SUB_GROUP_CACHE_MAX_DAY) : subGroupCacheMaxDay.toString());
        /*获取消息日志保存天数*/
        final Integer msgLogSaveDay = basicConfig.getMsgLogSaveDay();
//        Object msg_log_save_day = redisService.get(RedisConstant.MSG_LOG_SAVE_DAY);
        basicConfigDTO.setMsgLogSaveDay(msgLogSaveDay == null ? String.valueOf(Constants.MSG_LOG_SAVE_DAY) : msgLogSaveDay.toString());

        /*获取数据监控缓存保存天数*/
//        Object monitor_save_day = redisService.get(RedisConstant.MONITOR_SAVE_DAY);
        final Integer monitorSaveDay = dataCacheConfig.getMonitorSaveDay();
        basicConfigDTO.setMonitorSaveDay(monitorSaveDay == null ? String.valueOf(Constants.MONITOR_SAVE_DAY) : monitorSaveDay.toString());

        /*获取数据监控缓存保存条数*/
        final Integer monitorSaveCount = dataCacheConfig.getMonitorSaveCount();
//        Object monitor_save_num = redisService.get(RedisConstant.MONITOR_SAVE_NUM);
        basicConfigDTO.setMonitorSaveCount(monitorSaveCount == null ? String.valueOf(Constants.MONITOR_SAVE_NUM) : monitorSaveCount.toString());

        /*获取实时质量概览统计时间*/
//        String qualityOverviewTime = redisService.getCacheObject(RedisConstant.SET_QUALITY_OVERVIEW_TIME);
        final Integer interval = basicConfig.getInterval();
        final Integer timeType = basicConfig.getTimeType();
        basicConfigDTO.setQualityOverviewTime(interval+"-"+timeType);
        return R.ok(basicConfigDTO);
    }

//    /**
//     * 产品版本号修改
//     */
//    @RequiresPermissions("dataManagement:partInf:edit")
//    @Log(title = "产品版本管理", businessType = BusinessType.UPDATE)
//    @ApiOperation("修改产品版本信息")
//    @GetMapping("/productVersionEdit")
//    public R<?> productVersion(String partRevName) {
//        if (StringUtils.isEmpty(partRevName))
//            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
//        redisService.set(RedisConstant.PART_REV_NAME, partRevName);
//        return R.ok();
//    }

    /**
     * 设置报警限和合理限规则
     *
     * @param defaultAlarm
     * @param defaultRational
     * @return
     */
    @RequiresPermissions("dataManagement:specInf:setRule")
    @Log(title = "公差限管理-规则设置", businessType = BusinessType.UPDATE)
    @ApiOperation("设置报警限和合理限规则")
    @GetMapping("/setRule")
    public R<?> setRule(String defaultAlarm, String defaultRational) {
        if (StringUtils.isNotEmpty(defaultAlarm)) {
            if (Double.parseDouble(defaultAlarm) > 1f) {
                throw new BusinessException(DataManagementExceptionEnum.WARNING_LIMIT_EXCEPTION);
            }
            redisService.set(RedisConstant.WARNING_LIMIT, defaultAlarm);
        }

        if (StringUtils.isNotEmpty(defaultRational)) {
            if (Double.parseDouble(defaultRational) < 1f) {
                throw new BusinessException(DataManagementExceptionEnum.REASONABLE_LIMIT_EXCEPTION);
            }
            redisService.set(RedisConstant.REASONABLE_LIMIT, defaultRational);
        }
        return R.ok();
    }

//    /**
//     * 设置缓存子组最大天数
//     */
//    @ApiOperation("设置缓存子组最大天数")
//    @GetMapping("/setSubGroupCacheMaxDay")
//    public R<?> setSubGroupCacheMaxDay(String subGroupCacheMaxDay) {
//        if (StringUtils.isEmpty(subGroupCacheMaxDay))
//            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
//        redisService.set(RedisConstant.SUB_GROUP_CACHE_MAX_DAY, subGroupCacheMaxDay);
//        return R.ok();
//    }

//    /**
//     * 设置消息日志保存天数
//     */
//    @ApiOperation("设置消息日志保存天数")
//    @GetMapping("/setMsgLogSaveDay")
//    public R<?> setMsgLogSaveDay(String msgLogSaveDay) {
//        if (StringUtils.isEmpty(msgLogSaveDay))
//            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
//        redisService.set(RedisConstant.MSG_LOG_SAVE_DAY, msgLogSaveDay);
//        return R.ok();
//    }

//    /**
//     * 设置数据监控保存天数,条数
//     */
//    @ApiOperation("设置数据监控保存天数,条数")
//    @GetMapping("/setMonitorSaveDayAndCount")
//    public R<?> setMonitorSaveDayAndCount(Integer monitorSaveDay, Integer monitorSaveCount) {
//        if (monitorSaveDay == null || monitorSaveCount == null) {
//            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
//        }
//        redisService.set(RedisConstant.MONITOR_SAVE_DAY, monitorSaveDay);
//
//        Integer count = ruleInfService.getMaxCnt();
//        if (count > monitorSaveCount) {
//            throw new BusinessException(DataManagementExceptionEnum.MONITOR_SAVE_NUM_EXCEPTION);
//        }
//        redisService.set(RedisConstant.MONITOR_SAVE_NUM, monitorSaveCount);
//        return R.ok();
//    }

    /**
     * 获取服务标签的id
     */
    @ApiOperation("获取服务标签的id")
    @GetMapping("/getServerTagId")
    public R<?> getServerTagId() {
        Map<String, String> map = new HashMap<>();
        String sgrpTag = RedisConstant.SGRP_TAG;
        Long sgrpId = redisService.getCacheObject(RedisConstant.ID_GENERATOR + sgrpTag);
        map.put("sgrpTag", sgrpId.toString());
        return R.ok(map);
    }

    /**
     * 设置对应服务标签的id
     */
    @ApiOperation("设置对应服务标签的id")
    @GetMapping("/setServerTagId")
    public R<?> setServerTagId(String serverTag, Long id) {
        idGeneratorService.initId(serverTag, id);
        return R.ok();
    }

//    /**
//     * 设置操作日志保存天数
//     */
//    @ApiOperation("设置操作日志保存天数")
//    @GetMapping("/setOperLogSaveDay")
//    public R<?> setOperLogSaveDay(Integer operLogSaveDay) {
//        if (operLogSaveDay == null || operLogSaveDay <= 0) {
//            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
//        }
//        redisService.set(RedisConstant.MSG_LOG_SAVE_DAY, operLogSaveDay);
//        return R.ok();
//    }
}
