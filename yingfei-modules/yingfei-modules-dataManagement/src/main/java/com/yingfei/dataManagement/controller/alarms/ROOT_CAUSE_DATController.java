package com.yingfei.dataManagement.controller.alarms;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.ROOT_CAUSE_DATService;
import com.yingfei.entity.domain.ROOT_CAUSE_DAT;
import com.yingfei.entity.dto.ROOT_CAUSE_DAT_DTO;
import com.yingfei.entity.vo.ROOT_CAUSE_DAT_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "异常原因信息API")
@RestController
@RequestMapping("/root_cause_dat")
public class ROOT_CAUSE_DATController extends BaseController {
    
    @Resource
    private ROOT_CAUSE_DATService rootCauseDatService;

    /**
     * 获取异常原因信息列表
     */
    @ApiOperation("获取异常原因信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody ROOT_CAUSE_DAT_VO rootCauseDatVo) {
        List<ROOT_CAUSE_DAT_DTO> list = rootCauseDatService.getList(rootCauseDatVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(rootCauseDatService.getTotal(rootCauseDatVo));
        return dataTable;
    }

    /**
     * 新增异常原因信息
     */
    @CreateUpdateBy
    @RequiresPermissions("dataManagement:rootCauseDat:add")
    @Log(title = "异常原因管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增异常原因信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody ROOT_CAUSE_DAT_VO rootCauseDatVo) {
        rootCauseDatService.checkParam(rootCauseDatVo);
        rootCauseDatService.add(rootCauseDatVo);
        return R.ok();
    }

    /**
     * 修改异常原因信息
     */
    @RequiresPermissions("dataManagement:rootCauseDat:edit")
    @Log(title = "异常原因管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改异常原因信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody ROOT_CAUSE_DAT_VO rootCauseDatVo) {
        rootCauseDatService.checkParam(rootCauseDatVo);
        ROOT_CAUSE_DAT rootCauseDat = new ROOT_CAUSE_DAT();
        BeanUtils.copyPropertiesIgnoreNull(rootCauseDatVo, rootCauseDat);
        rootCauseDatService.updateById(rootCauseDat);
        return R.ok();
    }

    /**
     * 批量删除异常原因信息
     */
    @RequiresPermissions("dataManagement:rootCauseDat:remove")
    @Log(title = "异常原因管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除异常原因信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        rootCauseDatService.del(ids);
        return R.ok();
    }
}
