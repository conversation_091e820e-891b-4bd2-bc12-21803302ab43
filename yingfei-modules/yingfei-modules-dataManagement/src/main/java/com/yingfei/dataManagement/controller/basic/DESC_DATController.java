package com.yingfei.dataManagement.controller.basic;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.DESC_DATService;
import com.yingfei.entity.domain.DESC_DAT;
import com.yingfei.entity.dto.DESC_DAT_DTO;
import com.yingfei.entity.dto.JOB_DAT_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.vo.DESC_DAT_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "自定义描述符信息API")
@RestController
@RequestMapping("/desc_dat")
public class DESC_DATController extends BaseController {
    
    @Resource
    private DESC_DATService descDatService;

    /**
     * 获取自定义描述符信息列表
     */
    @ApiOperation("获取自定义描述符信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody DESC_DAT_VO descDatVo) {
        List<DESC_DAT_DTO> list = descDatService.getList(descDatVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(descDatService.getTotal(descDatVo));
        return dataTable;
    }

    /**
     * 新增自定义描述符信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:descDat:add")
    @Log(title = "自定义描述符管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增自定义描述符信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody DESC_DAT_VO descDatVo) {
        descDatService.checkParam(descDatVo);
        DESC_DAT descDat = descDatService.addDescDat(descDatVo);
        return R.ok(descDat);
    }

    /**
     * 修改自定义描述符信息
     */
    @RequiresPermissions("dataManagement:descDat:edit")
    @Log(title = "自定义描述符管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改自定义描述符信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody DESC_DAT_VO descDatVo) {
        descDatService.checkParam(descDatVo);
        DESC_DAT descDat = new DESC_DAT();
        BeanUtils.copyPropertiesIgnoreNull(descDatVo, descDat);
        descDatService.updateById(descDat);
        return R.ok();
    }

    /**
     * 批量删除自定义描述符信息
     */
    @RequiresPermissions("dataManagement:descDat:remove")
    @Log(title = "自定义描述符管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除自定义描述符信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        descDatService.del(ids);
        return R.ok();
    }

    @ApiOperation("获取自定义描述符信息")
    @PostMapping("/getInfo/{id}")
    public R<?> getInfo(@PathVariable Long id) {
        DESC_DAT_DTO descDatDto = descDatService.info(id);
        return R.ok(descDatDto);
    }

    @ApiOperation("获取自定义描述符删除影响的关联信息")
    @PostMapping("/getOperationAssociation/{ids}")
    public R<?> getOperationAssociation(@PathVariable List<Long> ids) {
        OperationAssociationDTO operationAssociationDTO = descDatService.getOperationAssociation(ids);
        return R.ok(operationAssociationDTO);
    }
}
