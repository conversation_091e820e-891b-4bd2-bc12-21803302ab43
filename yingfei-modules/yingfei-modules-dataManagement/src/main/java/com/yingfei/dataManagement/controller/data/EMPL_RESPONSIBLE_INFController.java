package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.EMPL_RESPONSIBLE_INFService;
import com.yingfei.entity.dto.EMPL_RESPONSIBLE_INF_DTO;
import com.yingfei.entity.vo.EMPL_RESPONSIBLE_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 员工责任信息Controller
 * <AUTHOR>
 */
@Api(tags = "员工责任信息API")
@Slf4j
@RestController
@RequestMapping("/emplResponsibleInf")
public class EMPL_RESPONSIBLE_INFController extends BaseController {

    @Resource
    private EMPL_RESPONSIBLE_INFService emplResponsibleInfService;

    /**
     * 获取员工责任信息列表
     */
    @ApiOperation("获取员工责任信息列表")
    @RequiresPermissions("system:emplResponsibleInf:list")
    @PostMapping("/test/list")
    public TableDataInfo<?> list(@RequestBody EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        List<EMPL_RESPONSIBLE_INF_DTO> list = emplResponsibleInfService.getList(emplResponsibleInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(emplResponsibleInfService.getTotal(emplResponsibleInfVo));
        return dataTable;
    }

    /**
     * 根据员工ID或测试ID获取列表
     */
    @ApiOperation("根据员工ID或测试ID获取列表")
    @RequiresPermissions("system:emplResponsibleInf:list")
    @PostMapping("/test/getListByFEmplOrFTest")
    public R<?> getListByFEmplOrFTest(@RequestBody EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        List<EMPL_RESPONSIBLE_INF_DTO> list = emplResponsibleInfService.getListByFEmplOrFTest(emplResponsibleInfVo);
        return R.ok(list);
    }

    /**
     * 根据员工ID获取责任信息
     */
    @ApiOperation("根据员工ID获取责任信息")
    @RequiresPermissions("system:emplResponsibleInf:query")
    @GetMapping("/test/getByFEmpl/{fEmpl}")
    public R<?> getByFEmpl(@PathVariable("fEmpl") Long fEmpl) {
        List<EMPL_RESPONSIBLE_INF_DTO> list = emplResponsibleInfService.getByFEmpl(fEmpl);
        return R.ok(list);
    }

    /**
     * 根据测试ID获取负责员工信息
     */
    @ApiOperation("根据测试ID获取负责员工信息")
    @RequiresPermissions("system:emplResponsibleInf:query")
    @GetMapping("/test/getByFTest/{fTest}")
    public R<?> getByFTest(@PathVariable("fTest") Long fTest) {
        List<EMPL_RESPONSIBLE_INF_DTO> list = emplResponsibleInfService.getByFTest(fTest);
        return R.ok(list);
    }

    /**
     * 新增员工责任信息
     */
    @ApiOperation("新增员工责任信息")
    @RequiresPermissions("system:emplResponsibleInf:add")
    @Log(title = "员工责任信息管理", businessType = BusinessType.INSERT)
    @PostMapping("/test/add")
    public R<?> add(@RequestBody EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        emplResponsibleInfService.add(emplResponsibleInfVo);
        return R.ok();
    }

    /**
     * 编辑员工责任信息
     */
    @ApiOperation("编辑员工责任信息")
    @RequiresPermissions("system:emplResponsibleInf:edit")
    @Log(title = "员工责任信息管理", businessType = BusinessType.UPDATE)
    @PostMapping("/test/edit")
    public R<?> edit(@RequestBody EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        emplResponsibleInfService.edit(emplResponsibleInfVo);
        return R.ok();
    }

    /**
     * 删除员工责任信息
     */
    @ApiOperation("删除员工责任信息")
    @RequiresPermissions("system:emplResponsibleInf:remove")
    @Log(title = "员工责任信息管理", businessType = BusinessType.DELETE)
    @PostMapping("/test/del")
    public R<?> del(@RequestBody List<Long> ids) {
        emplResponsibleInfService.del(ids);
        return R.ok();
    }


}
