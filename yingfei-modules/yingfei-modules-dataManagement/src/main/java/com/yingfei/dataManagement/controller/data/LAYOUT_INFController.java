package com.yingfei.dataManagement.controller.data;

import com.alibaba.fastjson2.JSONArray;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.LAYOUT_INFService;
import com.yingfei.entity.domain.LAYOUT_INF;
import com.yingfei.entity.dto.LAYOUT_INF_DTO;
import com.yingfei.entity.vo.LAYOUT_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "用户菜单列配置信息API")
@RestController
@RequestMapping("/layout_inf")
public class LAYOUT_INFController extends BaseController {

    @Resource
    private LAYOUT_INFService layoutInfService;

    /**
     * 获取用户菜单列配置信息列表
     */
    @ApiOperation("获取用户菜单列配置信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody LAYOUT_INF_VO layoutInfVo) {
        List<LAYOUT_INF_DTO> list = layoutInfService.getList(layoutInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(layoutInfService.getTotal(layoutInfVo));
        return dataTable;
    }

    /**
     * 根据用户ID和菜单id获取菜单列配置
     */
    @ApiOperation("根据用户ID和菜单id获取菜单列配置")
    @PostMapping("/getColumnConfig")
    public R<?> getColumnConfig(@RequestBody LAYOUT_INF_VO layoutInfVo){
        LAYOUT_INF_DTO layoutInfDto = layoutInfService.getColumnConfig(layoutInfVo);
        return R.ok(layoutInfDto);
    }

    /**
     * 新增用户菜单列配置信息
     */
    @CreateUpdateBy
    @NotResubmit
    @Log(title = "用户菜单列配置管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增用户菜单列配置信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody LAYOUT_INF_VO layoutInfVo) {
        layoutInfService.checkParam(layoutInfVo);
        layoutInfService.add(layoutInfVo);
        return R.ok();
    }

    /**
     * 修改用户菜单列配置信息
     */
    @Log(title = "用户菜单列配置管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改用户菜单列配置信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody LAYOUT_INF_VO layoutInfVo) {
        layoutInfService.checkParam(layoutInfVo);
        LAYOUT_INF layoutInf = new LAYOUT_INF();
        BeanUtils.copyPropertiesIgnoreNull(layoutInfVo, layoutInf);
        if (CollectionUtils.isNotEmpty(layoutInfVo.getLayoutInfDataDtoList())) {
            String jsonString = JSONArray.toJSONString(layoutInfVo.getLayoutInfDataDtoList());
            layoutInf.setF_DATA(jsonString);
        }
        layoutInfService.updateById(layoutInf);
        return R.ok();
    }

    /**
     * 批量删除用户菜单列配置信息
     */
    @Log(title = "用户菜单列配置管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除用户菜单列配置信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        layoutInfService.del(ids);
        return R.ok();
    }
}
