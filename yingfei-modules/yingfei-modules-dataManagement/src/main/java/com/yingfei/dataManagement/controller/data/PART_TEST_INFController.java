package com.yingfei.dataManagement.controller.data;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.utils.poi.ExcelUtil;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.datascope.annotation.DataScope;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.PART_TEST_INFService;
import com.yingfei.entity.domain.PART_TEST_INF;
import com.yingfei.entity.dto.PART_TEST_INF_DTO;
import com.yingfei.entity.vo.PART_TEST_INF_VO;
import com.yingfei.entity.vo.excel.PART_TEST_EXCEL_VO;
import com.yingfei.entity.vo.excel.SPEC_INF_EXCEL_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

@Api(tags = "产品测试组合的图片信息API")
@Slf4j
@RestController
@RequestMapping("/part_test_inf")
public class PART_TEST_INFController extends BaseController {

    @Resource
    private PART_TEST_INFService partTestInfService;

    /**
     * 获取产品测试组合的图片信息列表
     */
    @DataScope
    @ApiOperation("获取产品测试组合的图片信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody PART_TEST_INF_VO partTestInfVo) {
        List<PART_TEST_INF_DTO> list = partTestInfService.getList(partTestInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(partTestInfService.getTotal(partTestInfVo));
        return dataTable;
    }

    /**
     * 新增产品测试组合的图片信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:partTestInf:add")
    @Log(title = "产品测试组合的图片管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增产品测试组合的图片信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody PART_TEST_INF_VO partTestInfVo) {
        partTestInfService.checkParam(partTestInfVo);
        partTestInfService.add(partTestInfVo);
        return R.ok();
    }

    /**
     * 修改产品测试组合的图片信息
     */
    @RequiresPermissions("dataManagement:partTestInf:edit")
    @Log(title = "产品测试组合的图片管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改产品测试组合的图片信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody PART_TEST_INF_VO partTestInfVo) {
        partTestInfService.checkParam(partTestInfVo);
        PART_TEST_INF partTestInf = new PART_TEST_INF();
        BeanUtils.copyPropertiesIgnoreNull(partTestInfVo, partTestInf);
        partTestInfService.updateById(partTestInf);
        return R.ok();
    }

    /**
     * 批量删除产品测试组合的图片信息
     */
    @RequiresPermissions("dataManagement:partTestInf:remove")
    @Log(title = "产品测试组合的图片管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除产品测试组合的图片信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        partTestInfService.del(ids);
        return R.ok();
    }

    /**
     * 根据产品 测试 查询数据
     */
    @ApiOperation("根据产品 测试 查询数据")
    @PostMapping("/queryByPartTest")
    public R<?> queryByPartTest(@RequestBody PART_TEST_INF_VO partTestInfVo) {
        JudgeUtils.isTrue(
                StringUtils.isEmpty(partTestInfVo.getF_PART()) || StringUtils.isEmpty(partTestInfVo.getF_TEST()),
                CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        LambdaQueryWrapper<PART_TEST_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PART_TEST_INF::getF_PART, partTestInfVo.getF_PART())
                .eq(PART_TEST_INF::getF_TEST, partTestInfVo.getF_TEST())
                .eq(PART_TEST_INF::getF_DEL, DelFlagEnum.USE.getType());
        PART_TEST_INF partTestInf = partTestInfService.getOne(queryWrapper);
        return R.ok(partTestInf);
    }

    /**
     * 工作看板通过参评,版本查询对应的动态检验计划
     */
    @ApiOperation("工作看板通过产品,版本查询对应的动态检验计划")
    @PostMapping("/getByPlan")
    public R<?> getByPlan(@RequestBody PART_TEST_INF_VO partTestInfVo) {
        JudgeUtils.isTrue(
                StringUtils.isEmpty(partTestInfVo.getF_PART()) || StringUtils.isEmpty(partTestInfVo.getF_PTRV()),
                CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        List<PART_TEST_INF> partTestInfList = partTestInfService.getByPlan(partTestInfVo);
        return R.ok(partTestInfList);
    }

    /**
     * 导入特定excel并解析
     */
    @ApiOperation("导入特定excel并解析")
    @PostMapping("/analyzeExcel")
    public R<?> analyzeExcel(@RequestPart("files") List<MultipartFile> files) {
        partTestInfService.analyzeExcel(files);
        return R.ok();
    }

    @ApiOperation("下载产品测试导入模板")
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<PART_TEST_EXCEL_VO> util = new ExcelUtil<>(PART_TEST_EXCEL_VO.class);
        util.importTemplateExcel(response, "产品测试数据");
    }

    @NotResubmit
    @ApiOperation("导入产品测试信息")
    @Log(title = "产品测试组合的图片管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<?> importData(@RequestPart("file") MultipartFile file) throws Exception {
        try (InputStream inputStream = file.getInputStream()) {
            ExcelUtil<PART_TEST_EXCEL_VO> util = new ExcelUtil<>(PART_TEST_EXCEL_VO.class);
            List<PART_TEST_EXCEL_VO> partTestExcelVoList = util.importExcel(inputStream);
            Map<String, String> map = partTestInfService.importData(partTestExcelVoList);
            return R.ok(map);
        } catch (Exception e) {
            log.error("导入产品测试信息失败");
            e.printStackTrace();
        }
        return R.fail("导入失败");
    }
}
