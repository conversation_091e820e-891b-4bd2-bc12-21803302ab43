package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.SGRP_INF;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.enums.ParetoAnalyseTypeEnum;
import com.yingfei.entity.vo.SGRP_INF_UNFINISHED_VO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.entity.vo.SubgroupFilterVO;

import java.util.List;
import java.util.Set;

/**
 * @description 针对表【SGRP_INF(子组主信息表)】的数据库操作Service
 * @createDate 2024-06-13 15:02:05
 */
public interface SGRP_INFService extends IService<SGRP_INF>, BaseService<SubgroupDataVO, SubgroupDataDTO> {
    List<SubgroupDataDTO> getList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    List<SubgroupDataDTO> getFilterList(SubgroupFilterVO subgroupFilterVO);

    List<SubgroupDataDTO> getSubgroupDataDTOList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    SubgroupDataDTO getTopOne(SubgroupDataSelectionDTO sgrpExt);

    void add(List<SubgroupDataVO> subgroupDataVOList);

    List<SubgroupDataDTO> getViewDataSubgroupDataDTOList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    long getViewDataTotal(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    void updateCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum);

    void edit(SubgroupDataVO subgroupDataVO);

    void tempSave(String planId, List<SubgroupDataVO> subgroupDataVOList);

    /**
     * 获取临时保存子组
     * @param planId
     * @return
     */
    List<SubgroupDataVO> getTempSaveInfo(Long planId);

    Integer getWaitDealInfo(Long planId);

    List<SubgroupDataDTO> getSgrpInfList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    long getSgrpInfTotal(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    void disable(List<Long> sgrpIds);

    List<SubgroupDataDTO> findById(Long id);

    Set<String> getSubgroupIdList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    boolean processingTemp(Long planId, SubgroupDataVO subgroupDataVO);

    void cancelProcessingTemp(Long planId, SubgroupDataVO subgroupDataVO);

    boolean processingPending(Long planId, String childId);

    void cancelProcessingPending(Long planId, String childId);

    List<String> getProcessingPending(Long planId);



    List<SubgroupDataDTO> findBySgrpId(Long sgrpId,Integer flag);

    void delTempSaveInfo(Long planId, SubgroupDataVO subgroupDataVO);

    List<SGRP_INF> getCondition(List<Long> strings, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum);

    void delUnfinished(SGRP_INF_UNFINISHED_VO vo);
}
