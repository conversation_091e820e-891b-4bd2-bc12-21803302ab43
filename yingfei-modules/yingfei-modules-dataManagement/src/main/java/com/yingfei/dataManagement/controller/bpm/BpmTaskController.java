package com.yingfei.dataManagement.controller.bpm;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.service.bpm.BpmTaskService;
import com.yingfei.entity.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "工作流程 - 流程任务实例")
@RestController
@RequestMapping("/bpm/task")
public class BpmTaskController {

    @Resource
    private BpmTaskService bpmTaskService;

    @GetMapping("todo-page")
    @ApiOperation("获取 Todo 待办任务分页")
    public R<?> getTodoTaskPage(@Valid BpmTaskTodoPageReqVO pageVO) {
        return R.ok(bpmTaskService.getTodoTaskPage(SecurityUtils.getUserId(), pageVO));
    }

    @GetMapping("done-page")
    @ApiOperation("获取 Done 已办任务分页")
    public R<?> getDoneTaskPage(@Valid BpmTaskTodoPageReqVO pageVO) {
        return R.ok(bpmTaskService.getDoneTaskPage(SecurityUtils.getUserId(), pageVO));
    }

    /**
     * 获得指定流程实例的待处理和已处理的任务列表  待处理后的节点不会查询出来
     * @param processInstanceId
     * @return
     */
    @GetMapping("/list-by-process-instance-id")
    @ApiOperation("获得指定流程实例的待处理和已处理的任务列表")
    @Parameter(name = "processInstanceId", description = "流程实例的编号", required = true)
    public R<?> getTaskListByProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId) {
        return R.ok(bpmTaskService.getTaskListByProcessInstanceId(processInstanceId));
    }


    @PutMapping("/approve")
    @ApiOperation("通过任务")
    public R<?> approveTask(@Valid @RequestBody BpmTaskApproveReqVO reqVO) {
        bpmTaskService.approveTask(SecurityUtils.getUserId(), reqVO);
        return R.ok(true);
    }

    @PutMapping("/reject")
    @ApiOperation("不通过任务")
    public R<?> rejectTask(@Valid @RequestBody BpmTaskApproveReqVO reqVO) {
        bpmTaskService.rejectTask(SecurityUtils.getUserId(), reqVO);
        return R.ok(true);
    }

    @PutMapping("/returns")
    @ApiOperation("退回到指定节点")
    public R<?> returns(@Valid @RequestBody BpmTaskApproveReqVO reqVO) {
        bpmTaskService.returns(SecurityUtils.getUserId(), reqVO);
        return R.ok(true);
    }

    @PutMapping("/update-assignee")
    @ApiOperation("更新任务的负责人 ,用于【流程详情】的【转派】按钮")
    public R<?> updateTaskAssignee(@Valid @RequestBody BpmTaskApproveReqVO reqVO) {
        bpmTaskService.updateTaskAssignee(SecurityUtils.getUserId(), reqVO);
        return R.ok(true);
    }

    /**
     * 获取报警详情
     */
    @ApiOperation("获取报警详情")
    @PostMapping("/getEvntInf/{processInstanceId}")
    public R<?> getEvntInf(@PathVariable String processInstanceId){
        return R.ok(bpmTaskService.getEvntInf(processInstanceId));
    }
}
