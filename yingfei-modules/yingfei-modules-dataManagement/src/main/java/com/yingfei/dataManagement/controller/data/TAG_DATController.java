package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.TAG_DATService;
import com.yingfei.entity.domain.TAG_DAT;
import com.yingfei.entity.dto.TAG_DAT_DTO;
import com.yingfei.entity.vo.TAG_DAT_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "标签信息API")
@RestController
@RequestMapping("/tag_dat")
public class TAG_DATController extends BaseController {
    
    @Resource
    private TAG_DATService tagDatService;

    /**
     * 获取标签信息列表
     */
    @ApiOperation("获取标签信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody TAG_DAT_VO tagDatVo) {
        List<TAG_DAT_DTO> list = tagDatService.getList(tagDatVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(tagDatService.getTotal(tagDatVo));
        return dataTable;
    }

    /**
     * 新增标签信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:tagDat:add")
    @Log(title = "标签管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增标签信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody TAG_DAT_VO tagDatVo) {
        tagDatService.checkParam(tagDatVo);
        tagDatService.add(tagDatVo);
        return R.ok();
    }

    /**
     * 修改标签信息
     */
    @RequiresPermissions("dataManagement:tagDat:edit")
    @Log(title = "标签管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改标签信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody TAG_DAT_VO tagDatVo) {
        tagDatService.checkParam(tagDatVo);
        TAG_DAT tagDat = new TAG_DAT();
        BeanUtils.copyPropertiesIgnoreNull(tagDatVo, tagDat);
        tagDatService.updateById(tagDat);
        return R.ok();
    }

    /**
     * 批量删除标签信息
     */
    @RequiresPermissions("dataManagement:tagDat:remove")
    @Log(title = "标签管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除标签信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        tagDatService.del(ids);
        return R.ok();
    }
}
