package com.yingfei.dataManagement.controller.basic;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.DICT_INFService;
import com.yingfei.entity.domain.DICT_INF;
import com.yingfei.entity.dto.DICT_INF_DTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.vo.DICT_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "枚举翻译字典信息API")
@RestController
@RequestMapping("/dict_inf")
public class DICT_INFController extends BaseController {

    @Resource
    private DICT_INFService dictInfService;

    /**
     * 获取枚举翻译字典信息列表
     */
    @ApiOperation("获取枚举翻译字典信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody DICT_INF_VO dictInfVo) {
        List<DICT_INF_DTO> list = dictInfService.getList(dictInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(dictInfService.getTotal(dictInfVo));
        return dataTable;
    }

    /**
     * 新增枚举翻译字典信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:dictInf:add")
    @Log(title = "枚举翻译字典管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增枚举翻译字典信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody DICT_INF_VO dictInfVo) {
        dictInfService.checkParam(dictInfVo);
        dictInfService.add(dictInfVo);
        return R.ok();
    }

    /**
     * 修改枚举翻译字典信息
     */
    @RequiresPermissions("dataManagement:dictInf:edit")
    @Log(title = "枚举翻译字典管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改枚举翻译字典信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody DICT_INF_VO dictInfVo) {
        dictInfService.checkParam(dictInfVo);
        DICT_INF dictInf = new DICT_INF();
        BeanUtils.copyPropertiesIgnoreNull(dictInfVo, dictInf);
        dictInfService.updateById(dictInf);
        return R.ok();
    }

    /**
     * 批量删除枚举翻译字典信息
     */
    @RequiresPermissions("dataManagement:dictInf:remove")
    @Log(title = "枚举翻译字典管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除枚举翻译字典信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        dictInfService.del(ids);
        return R.ok();
    }

    @ApiOperation("获取图表类型")
    @GetMapping("getChartType")
    public R<?> getChartType() {
        return R.ok(ControlChartTypeEnum.getMap());
    }
}
