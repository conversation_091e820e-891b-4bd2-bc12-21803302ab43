package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.PRCS_INF;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.PRCS_INF_DTO;
import com.yingfei.entity.vo.PRCS_INF_VO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【PRCS_INF(过程信息表)】的数据库操作Service
 * @createDate 2024-05-08 16:28:11
 */
public interface PRCS_INFService extends IService<PRCS_INF>, BaseService<PRCS_INF_VO, PRCS_INF_DTO> {

    List<PRCS_INF_DTO> getTree(PRCS_INF_VO prcsInfVo);

    void recovery(Long id);

    void cover(Long id);

    Map<String, String> importPrcs(List<PRCS_INF_VO> prcsInfVoList);

    PRCS_INF recycleBin(PRCS_INF_VO prcsInfVo);

    List<PRCS_INF> getSearchCondition(Integer isInclude, List<Long> dataList,List<Long> hierIds);

    HIERARCHY_INF_DTO getBelongPrcsList(PRCS_INF_VO prcsInfVo);

    void batchEdit(PRCS_INF_VO prcsInfVo);

    PRCS_INF_DTO getInfo(Long id);

    OperationAssociationDTO getOperationAssociation(List<Long> ids);
}
