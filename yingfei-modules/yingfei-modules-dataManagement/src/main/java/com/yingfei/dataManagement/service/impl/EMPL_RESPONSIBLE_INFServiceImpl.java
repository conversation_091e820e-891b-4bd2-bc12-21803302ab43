package com.yingfei.dataManagement.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.mapper.EMPL_RESPONSIBLE_INFMapper;
import com.yingfei.dataManagement.service.EMPL_RESPONSIBLE_INFService;
import com.yingfei.dataManagement.service.TEST_INFService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.EMPL_RESPONSIBLE_INF_DTO;
import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.enums.DbLinkEnum;
import com.yingfei.entity.enums.EMPL_INFStatusEnum;
import com.yingfei.entity.vo.EMPL_RESPONSIBLE_INF_VO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 员工责任信息Service实现类
 * <AUTHOR>
 * @description 针对表【EMPL_RESPONSIBLE_INF(员工责任信息表)】的数据库操作Service实现
 */
@Slf4j
@Service
public class EMPL_RESPONSIBLE_INFServiceImpl extends ServiceImpl<EMPL_RESPONSIBLE_INFMapper, EMPL_RESPONSIBLE_INF>
        implements EMPL_RESPONSIBLE_INFService {
    @Resource
    private TEST_INFService testInfService;

    @Override
    public long getTotal(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        MPJLambdaWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = buildQueryWrapper(emplResponsibleInfVo);
        return baseMapper.selectJoinCount(queryWrapper);
    }

    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getList(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        MPJLambdaWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = buildQueryWrapper(emplResponsibleInfVo);
        final List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos = baseMapper.selectJoinList(EMPL_RESPONSIBLE_INF_DTO.class, queryWrapper);
        if (emplResponsibleInfVo.getF_TYPE().equals(0)) {
            buildTest( emplResponsibleInfDtos);
        }
        return emplResponsibleInfDtos;
    }

    private void buildTest(List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos) {
            if (CollectionUtils.isNotEmpty(emplResponsibleInfDtos)) {
                //k:F_RESP  v：F_TEST
//                final Map<Long, Long> fTestMap = getFTestMapValues(emplResponsibleInfDtos);
                Map<Long, Long> fTestMap = emplResponsibleInfDtos.stream()
                        .collect(Collectors.toMap(
                                EMPL_RESPONSIBLE_INF_DTO::getF_RESP,
                                dto -> Long.parseLong(dto.getF_DATA()),
                                (existing, newVal) -> newVal
                        ));
                if (ObjectUtils.isNotEmpty(fTestMap)) {
                    //testId集合
                    final HashSet<Long> testIdSet = new HashSet<>(fTestMap.values());
                    final LambdaQueryWrapper<TEST_INF> testInfLambdaQueryWrapper = new LambdaQueryWrapper<TEST_INF>()
                            .in(TEST_INF::getF_TEST, testIdSet)
                            .eq(TEST_INF::getF_DEL, DelFlagEnum.USE.getType());
                    final List<TEST_INF> testInfList = testInfService.getBaseMapper().selectList(testInfLambdaQueryWrapper);
                    if (CollectionUtils.isNotEmpty(testInfList)) {
                        //k:F_TEST  v：F_NAME
                        final Map<Long, String> testMap = testInfList.stream()
                                .collect(Collectors.toMap(
                                        TEST_INF::getF_TEST,
                                        TEST_INF::getF_NAME,
                                        (existing, replacement) -> existing
                                ));
                        for (EMPL_RESPONSIBLE_INF_DTO emplResponsibleInfDto : emplResponsibleInfDtos) {
                            if (ObjectUtils.isNotEmpty(fTestMap.get(emplResponsibleInfDto.getF_RESP()))) {
                                emplResponsibleInfDto.setTestName(testMap.get(fTestMap.get(emplResponsibleInfDto.getF_RESP())));
                                emplResponsibleInfDto.setF_TEST(fTestMap.get(emplResponsibleInfDto.getF_RESP()));
                            }
                        }
                    }
                }
            }
        }


//    /**
//     * 从 EMPL_RESPONSIBLE_INF_DTO 列表中提取 F_RESP 作为键，对应的 F_TEST 作为值，构建 Map
//     * @param emplResponsibleInfDtos EMPL_RESPONSIBLE_INF_DTO 列表
//     * @return 以 F_RESP 为键，F_TEST 为值的 Map
//     */
//    private Map<Long, Long> getFTestMapValues(List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos) {
//        if (emplResponsibleInfDtos == null || emplResponsibleInfDtos.isEmpty()) {
//            return new HashMap<>();
//        }
//        return emplResponsibleInfDtos.stream()
//                .collect(Collectors.toMap(
//                        EMPL_RESPONSIBLE_INF_DTO::getF_RESP,
//                        dto -> {
//                            String fData = dto.getF_DATA();
//                            if (fData == null) {
//                                return null;
//                            }
//                            try {
//                                JSONObject jsonObject = JSONObject.parseObject(fData);
//                                return jsonObject.getLong("F_TEST");
//                            } catch (Exception e) {
//                                // 解析失败返回 null
//                                return null;
//                            }
//                        },
//                        (existing, replacement) -> existing,
//                        HashMap::new
//                )).entrySet().stream()
//                .filter(entry -> entry.getValue() != null)
//                .collect(Collectors.toMap(
//                        Map.Entry::getKey,
//                        Map.Entry::getValue,
//                        (existing, replacement) -> existing,
//                        HashMap::new
//                ));
//    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        // 参数校验
        checkParam(emplResponsibleInfVo);

        EMPL_RESPONSIBLE_INF emplResponsibleInf = new EMPL_RESPONSIBLE_INF();
        BeanUtils.copyPropertiesIgnoreNull(emplResponsibleInfVo, emplResponsibleInf);

        // 设置默认值
        emplResponsibleInf.setF_RESP(JudgeUtils.defaultIdentifierGenerator.nextId(null));
        emplResponsibleInf.setF_DEL(DelFlagEnum.USE.getType());
        emplResponsibleInf.setF_CRUE(SecurityUtils.getUserId());
        emplResponsibleInf.setF_CRTM(LocalDateTime.now());

        baseMapper.insert(emplResponsibleInf);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        if (ObjectUtils.isEmpty(emplResponsibleInfVo.getF_RESP())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        // 参数校验
        checkParam(emplResponsibleInfVo);

        EMPL_RESPONSIBLE_INF emplResponsibleInf = new EMPL_RESPONSIBLE_INF();
        BeanUtils.copyPropertiesIgnoreNull(emplResponsibleInfVo, emplResponsibleInf);

        // 设置编辑信息
        emplResponsibleInf.setF_EDUE(SecurityUtils.getUserId());
        emplResponsibleInf.setF_EDTM(LocalDateTime.now());

        baseMapper.updateById(emplResponsibleInf);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        LambdaUpdateWrapper<EMPL_RESPONSIBLE_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(EMPL_RESPONSIBLE_INF::getF_RESP, ids);
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_DEL, DelFlagEnum.DELETE.getType());
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_EDUE, SecurityUtils.getUserId());
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_EDTM, LocalDateTime.now());

        baseMapper.update(null, updateWrapper);
    }
@Override
    public void checkParam(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        if (ObjectUtils.isEmpty(emplResponsibleInfVo.getF_TYPE())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        if (ObjectUtils.isEmpty(emplResponsibleInfVo.getF_EMPL())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        if (StringUtils.isBlank(emplResponsibleInfVo.getF_DATA())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        if(emplResponsibleInfVo.getF_TYPE().equals(0)) {
            // 检查唯一性
            MPJLambdaWrapper<EMPL_RESPONSIBLE_INF> uniqueWrapper = new MPJLambdaWrapper<>();
            uniqueWrapper.leftJoin(EMPL_INF.class, EMPL_INF::getF_EMPL, EMPL_RESPONSIBLE_INF::getF_EMPL);
            uniqueWrapper.eq(EMPL_INF::getF_STATUS, EMPL_INFStatusEnum.ACTIVATE.getCode());
            uniqueWrapper.eq(EMPL_RESPONSIBLE_INF::getF_DEL, YesOrNoEnum.NO.getType());
            uniqueWrapper.selectAll(EMPL_RESPONSIBLE_INF.class);
            if(ObjectUtils.isNotEmpty(emplResponsibleInfVo.getF_RESP())){
                uniqueWrapper.ne(EMPL_RESPONSIBLE_INF::getF_RESP, emplResponsibleInfVo.getEmplIds());
            }
            //（一个测试只能有一个负责人）
            uniqueWrapper.eq(EMPL_RESPONSIBLE_INF::getF_DATA, emplResponsibleInfVo.getF_DATA());
            long count = baseMapper.selectJoinCount(uniqueWrapper);
            if (count > 0) {
                throw new BusinessException(CommonExceptionEnum.DATA_ALREADY_EXISTS_EXCEPTION);
            }
        }
    }

    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getListByFEmplOrFTest(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        MPJLambdaWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = buildQueryWrapper(emplResponsibleInfVo);
        return baseMapper.selectJoinList( EMPL_RESPONSIBLE_INF_DTO.class,queryWrapper);
    }

    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getByFEmpl(Long fEmpl) {
        if (ObjectUtils.isEmpty(fEmpl)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        final EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo = new EMPL_RESPONSIBLE_INF_VO();
        emplResponsibleInfVo.setF_EMPL(fEmpl);
        emplResponsibleInfVo.setF_TYPE(0);
        MPJLambdaWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = buildQueryWrapper(emplResponsibleInfVo);
        List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos = baseMapper.selectJoinList(EMPL_RESPONSIBLE_INF_DTO.class,queryWrapper);
        buildTest(emplResponsibleInfDtos);
        return emplResponsibleInfDtos;
    }


    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getByFTest(Long fTest) {
        if (ObjectUtils.isEmpty(fTest)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        final EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo = new EMPL_RESPONSIBLE_INF_VO();
        emplResponsibleInfVo.setTestIds(Collections.singletonList(fTest));
        emplResponsibleInfVo.setF_TYPE(0);
        MPJLambdaWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = buildQueryWrapper(emplResponsibleInfVo);
        List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos = baseMapper.selectJoinList(EMPL_RESPONSIBLE_INF_DTO.class,queryWrapper);
        buildTest(emplResponsibleInfDtos);
        return emplResponsibleInfDtos;
    }



    /**
     * 构建查询条件
     * @param emplResponsibleInfVo 查询条件
     * @return 查询包装器
     */
    private MPJLambdaWrapper<EMPL_RESPONSIBLE_INF> buildQueryWrapper(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        MPJLambdaWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.leftJoin(EMPL_INF.class, EMPL_INF::getF_EMPL, EMPL_RESPONSIBLE_INF::getF_EMPL);
        queryWrapper.eq(EMPL_INF::getF_STATUS, EMPL_INFStatusEnum.ACTIVATE.getCode());
        queryWrapper.selectAll(EMPL_RESPONSIBLE_INF.class)
                .selectAs(EMPL_INF::getF_NAME, EMPL_RESPONSIBLE_INF_DTO::getEmplName);

        // 默认查询未删除的记录
            queryWrapper.eq(EMPL_RESPONSIBLE_INF::getF_DEL, DelFlagEnum.USE.getType());

        // 业务类型查询
        if (ObjectUtils.isNotEmpty(emplResponsibleInfVo.getF_TYPE())) {
            queryWrapper.eq(EMPL_RESPONSIBLE_INF::getF_TYPE, emplResponsibleInfVo.getF_TYPE());
        }

        // 员工ID查询
        if (ObjectUtils.isNotEmpty(emplResponsibleInfVo.getF_EMPL())) {
            queryWrapper.eq(EMPL_RESPONSIBLE_INF::getF_EMPL, emplResponsibleInfVo.getF_EMPL());
        }

        // 员工ID列表查询
        if (CollectionUtils.isNotEmpty(emplResponsibleInfVo.getEmplIds())) {
            queryWrapper.in(EMPL_RESPONSIBLE_INF::getF_EMPL, emplResponsibleInfVo.getEmplIds());
        }

//        // 测试ID列表查询（F_DATA直接存储json字符串 {"F_TEST":"123123123"}）
//        if (CollectionUtils.isNotEmpty(emplResponsibleInfVo.getTestIds())) {
//            final List<Long> testIds = emplResponsibleInfVo.getTestIds();
//            // 生成占位符 (?, ?, ...)
//            String placeholders = String.join(",", Collections.nCopies(testIds.size(), "?"));
//
//            queryWrapper.and(wrapper -> {
//                String sqlTemplate;
//                switch (DbLinkEnum.getType(emplResponsibleInfVo.getDbType())) {
//                    case MYSQL:
//                        // 使用JSON_UNQUOTE去除引号，确保类型匹配
//                        sqlTemplate = "JSON_UNQUOTE(JSON_EXTRACT(F_DATA, '$.F_TEST')) IN (" + placeholders + ")";
//                        break;
//                    case PGSQL:
//                        sqlTemplate = "F_DATA::jsonb ->> 'F_TEST' IN (" + placeholders + ")";
//                        break;
//                    case ORACLE:
//                        sqlTemplate = "JSON_VALUE(F_DATA, '$.F_TEST') IN (" + placeholders + ")";
//                        break;
//                    case SQL_SERVER:
//                        sqlTemplate = "JSON_VALUE(F_DATA, '$.F_TEST') IN (" + placeholders + ")";
//                        break;
//                    default:
//                        throw new BusinessException(CommonExceptionEnum.UNSUPPORTED_DATABASE_TYPE_EXCEPTION);
//                }
//                // 传入SQL模板和参数列表（MyBatis会自动处理参数类型和引号）
//                wrapper.apply(sqlTemplate, testIds.toArray());
//
//            });
//        }

        // 测试ID列表查询（F_DATA直接存储testId）
        if (CollectionUtils.isNotEmpty(emplResponsibleInfVo.getTestIds())) {
            List<String> testIds = emplResponsibleInfVo.getTestIds().stream()
                    .map(Object::toString)
                    .collect(Collectors.toList());

            // 确认testIds不为空
            if (!CollectionUtils.isEmpty(testIds)) {
                // 直接对F_DATA字段进行IN查询，无需JSON解析
                queryWrapper.in(EMPL_RESPONSIBLE_INF::getF_DATA, testIds);
            }
        }

        // 数据内容模糊查询
        if (StringUtils.isNotBlank(emplResponsibleInfVo.getF_DATA())) {
            queryWrapper.like(EMPL_RESPONSIBLE_INF::getF_DATA, emplResponsibleInfVo.getF_DATA());
        }

        // 排序
        queryWrapper.orderByDesc(EMPL_RESPONSIBLE_INF::getF_CRTM);

        return queryWrapper;
    }


}
