package com.yingfei.dataManagement.controller.chart;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.poi.ExcelUtil;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.mapper.DESC_GRPMapper;
import com.yingfei.dataManagement.service.chart.BoxPlotsService;
import com.yingfei.dataManagement.service.chart.DataReportService;
import com.yingfei.entity.domain.DESC_GRP;
import com.yingfei.entity.domain.DICT_INF;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.BoxPlotsConfigDTO;
import com.yingfei.entity.dto.chart.BoxPlotsDataDTO;
import com.yingfei.entity.dto.chart.BoxPlotsParticularsDTO;
import com.yingfei.entity.dto.chart.DataReportDTO;
import com.yingfei.entity.enums.BoxPlotsAnalyseTypeEnum;
import com.yingfei.entity.enums.ChartTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@Api(tags = "图表:数据报告API")
@RequestMapping("/dataReport")
public class DataReportController {

    @Resource
    private DataReportService dataReportService;

    /**
     * 获取数据报告详情
     *
     * @return
     */
    @PostMapping("/info")
    @ApiOperation("获取数据报告详情")
    public R<?> getInfo(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        Map<String, Object> map = dataReportService.getInfo(subgroupDataSelectionDTO);
        return R.ok(map);
    }


}
