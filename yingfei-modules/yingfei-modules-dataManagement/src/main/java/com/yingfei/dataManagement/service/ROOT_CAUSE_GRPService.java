package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.ROOT_CAUSE_GRP;
import com.yingfei.entity.dto.ROOT_CAUSE_GRP_DTO;
import com.yingfei.entity.vo.ROOT_CAUSE_GRP_VO;

/**
* <AUTHOR>
* @description 针对表【ROOT_CAUSE_GRP(储存异常原因组信息表)】的数据库操作Service
* @createDate 2024-05-08 16:28:28
*/
public interface ROOT_CAUSE_GRPService extends IService<ROOT_CAUSE_GRP>, BaseService<ROOT_CAUSE_GRP_VO, ROOT_CAUSE_GRP_DTO> {

}
