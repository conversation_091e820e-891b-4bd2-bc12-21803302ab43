package com.yingfei.dataManagement.controller.alarms;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.EVNT_INFService;
import com.yingfei.entity.domain.EVNT_INF;
import com.yingfei.entity.dto.EVNT_INF_DTO;
import com.yingfei.entity.vo.EVNT_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "报警事件信息API")
@RestController
@RequestMapping("/evnt_inf")
public class EVNT_INFController extends BaseController {

    @Resource
    private EVNT_INFService evntInfService;

    /**
     * 获取报警事件信息列表
     */
    @ApiOperation("获取报警事件信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody EVNT_INF_VO evntInfVo) {
        List<EVNT_INF_DTO> list = evntInfService.getList(evntInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(evntInfService.getTotal(evntInfVo));
        return dataTable;
    }

    /**
     * 新增报警事件信息
     */
    @CreateUpdateBy
    @RequiresPermissions("dataManagement:evntInf:add")
    @Log(title = "报警事件管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增报警事件信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody EVNT_INF_VO evntInfVo) {
        evntInfService.checkParam(evntInfVo);
        evntInfService.add(evntInfVo);
        return R.ok();
    }

    /**
     * 修改报警事件信息
     */
    @RequiresPermissions("dataManagement:evntInf:edit")
    @Log(title = "报警事件管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改报警事件信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody EVNT_INF_VO evntInfVo) {
        evntInfService.checkParam(evntInfVo);
        EVNT_INF evntInf = new EVNT_INF();
        BeanUtils.copyPropertiesIgnoreNull(evntInfVo, evntInf);
        evntInfService.updateById(evntInf);
        return R.ok();
    }

    /**
     * 批量删除报警事件信息
     */
    @RequiresPermissions("dataManagement:evntInf:remove")
    @Log(title = "报警事件管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除报警事件信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        evntInfService.del(ids);
        return R.ok();
    }

    /**
     * 指定异常原因
     */
    @ApiOperation("指定异常原因或改善措施")
    @PostMapping("/exceptionCause")
    public R<?> exceptionCause(@RequestBody EVNT_INF_VO evntInfVo) {
        evntInfService.exceptionCause(evntInfVo);
        return R.ok();
    }

    /**
     * 远程调用: 获取报警事件列表中产品,过程,测试对应的标签id列表
     */
    @ApiOperation("远程调用: 获取报警事件列表中产品,过程,测试对应的标签id列表")
    @PostMapping("/findByTagList")
    public R<?> findByTagList(@RequestBody List<EVNT_INF> evntInfList) {
       List<EVNT_INF_DTO> evntInfDtoList = evntInfService.findByTagList(evntInfList);
       return R.ok(evntInfDtoList);
    }
}
