package com.yingfei.dataManagement.service.report.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.dataManagement.mapper.STREAM_TREND_INFMapper;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.report.MonthTrendService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.AdminUserRespDTO;
import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.dto.report.MonthTrendQueryDTO;
import com.yingfei.entity.dto.report.MonthTrendResultDTO;
import com.yingfei.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MonthTrendServiceImpl implements MonthTrendService {

    /**
     * 负责人信息内部类
     */
    private static class ResponsiblePersonInfo {
        private Long personId;
        private String personName;

        public ResponsiblePersonInfo(Long personId, String personName) {
            this.personId = personId;
            this.personName = personName;
        }

        public Long getPersonId() { return personId; }
        public String getPersonName() { return personName; }
    }
    @Resource
    private STREAM_TREND_INFMapper streamTrendInfMapper;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private PART_INFService  partInfService;
    @Resource
    private PRCS_INFService  prcsInfService;
    @Resource
    private TEST_INFService testInfService;
    @Resource
    private PART_REVService partRevService;
    @Resource
    private EMPL_RESPONSIBLE_INFService emplResponsibleInfService;
    @Resource
    private RemoteUserService remoteUserService;


    @Override
    public TableDataInfo<MonthTrendResultDTO> getMonthTrend(MonthTrendQueryDTO monthTrendQueryDTO) {
        // 设置默认查询时间范围（如果未提供）
        if(ObjectUtils.isEmpty(monthTrendQueryDTO.getStartDate())){
            LocalDate today = LocalDate.now();
            Date startDate = Date.from(today.minusMonths(6).withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(today.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            monthTrendQueryDTO.setStartDate(startDate);
            monthTrendQueryDTO.setEndDate(endDate);
        }

        // 计算查询的月份范围
        LocalDateTime startDateTime = monthTrendQueryDTO.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endDateTime = monthTrendQueryDTO.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        Date queryStartDate = Date.from(startDateTime.withDayOfMonth(1).with(LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
        Date queryEndDate = Date.from(endDateTime.withDayOfMonth(endDateTime.getMonth().length(endDateTime.toLocalDate().isLeapYear())).with(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());

        // 查询STREAM_TREND_INF数据（f_type = 1 按月统计）
        List<STREAM_TREND_INF_DTO> streamTrendData = queryStreamTrendData(monthTrendQueryDTO, queryStartDate, queryEndDate);

        // 按产品、过程、测试、版本分组处理数据
        Map<String, List<STREAM_TREND_INF_DTO>> groupedData = streamTrendData.stream()
                .collect(Collectors.groupingBy(item ->
                    item.getF_PART() + "_" + item.getF_PRCS() + "_" + item.getF_TEST() + "_" + item.getF_PTRV()));

        // 构建返回结果（不包含负责人信息）
        List<MonthTrendResultDTO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<STREAM_TREND_INF_DTO>> entry : groupedData.entrySet()) {
            MonthTrendResultDTO resultDTO = buildMonthTrendResult(entry.getValue());
            if (resultDTO != null) {
                resultList.add(resultDTO);
            }
        }

        // 分页处理
        final Page<MonthTrendResultDTO> page = BaseEntity.convertToPage(monthTrendQueryDTO.getOffset(), monthTrendQueryDTO.getNext());
        int start = (int) ((page.getCurrent() - 1) * page.getSize());
        int end = Math.min(start + (int) page.getSize(), resultList.size());

        List<MonthTrendResultDTO> pagedResult = resultList.subList(start, end);
        page.setRecords(pagedResult);
        page.setTotal(resultList.size());

        // 批量查询当前页面的项目负责人信息
        Set<Long> testIds = pagedResult.stream()
                .map(MonthTrendResultDTO::getTestId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (!testIds.isEmpty()) {
            Map<Long, ResponsiblePersonInfo> responsiblePersonMap = batchQueryResponsiblePersons(testIds);
            // 为当前页面的数据设置负责人信息
            pagedResult.forEach(result -> {
                ResponsiblePersonInfo responsiblePerson = responsiblePersonMap.get(result.getTestId());
                if (responsiblePerson != null) {
                    result.setResponsiblePersonId(responsiblePerson.getPersonId());
                    result.setResponsiblePersonName(responsiblePerson.getPersonName());
                }
            });
        }
        return new TableDataInfo<>(pagedResult, resultList.size());
    }

    /**
     * 查询STREAM_TREND_INF数据
     */
    private List<STREAM_TREND_INF_DTO> queryStreamTrendData(MonthTrendQueryDTO queryDTO, Date startDate, Date endDate) {
        MPJLambdaWrapper<STREAM_TREND_INF> wrapper = new MPJLambdaWrapper<>();
        wrapper.leftJoin(PART_INF.class, PART_INF::getF_PART, STREAM_TREND_INF::getF_PART);
        wrapper.leftJoin(PART_REV.class, PART_REV::getF_PTRV, STREAM_TREND_INF::getF_PTRV);
        wrapper.leftJoin(PRCS_INF.class, PRCS_INF::getF_PRCS, STREAM_TREND_INF::getF_PRCS);
        wrapper.leftJoin(TEST_INF.class, TEST_INF::getF_TEST, STREAM_TREND_INF::getF_TEST);

        wrapper.selectAll(STREAM_TREND_INF.class)
                .selectAs(PART_INF::getF_NAME, STREAM_TREND_INF_DTO::getPartName)
                .selectAs(PART_REV::getF_NAME, STREAM_TREND_INF_DTO::getPtrvName)
                .selectAs(PRCS_INF::getF_NAME, STREAM_TREND_INF_DTO::getPrcsName)
                .selectAs(TEST_INF::getF_NAME, STREAM_TREND_INF_DTO::getTestName);

        // 添加查询条件
        if(ObjectUtils.isNotEmpty(queryDTO.getPartList())){
            wrapper.in(STREAM_TREND_INF::getF_PART, queryDTO.getPartList());
        }
        if(ObjectUtils.isNotEmpty(queryDTO.getPtrvList())){
            wrapper.in(STREAM_TREND_INF::getF_PTRV, queryDTO.getPtrvList());
        }
        if(ObjectUtils.isNotEmpty(queryDTO.getPrcsList())){
            wrapper.in(STREAM_TREND_INF::getF_PRCS, queryDTO.getPrcsList());
        }
        if(ObjectUtils.isNotEmpty(queryDTO.getTestList())){
            wrapper.in(STREAM_TREND_INF::getF_TEST, queryDTO.getTestList());
        }

        // 关键条件：f_type = 1（按月统计）
        wrapper.eq(STREAM_TREND_INF::getF_TYPE, 1);
        wrapper.ge(STREAM_TREND_INF::getF_START, startDate);
        wrapper.le(STREAM_TREND_INF::getF_START, endDate);
        wrapper.orderByAsc(STREAM_TREND_INF::getF_START);

        return streamTrendInfMapper.selectJoinList(STREAM_TREND_INF_DTO.class, wrapper);
    }

    /**
     * 构建月度趋势结果
     */
    private MonthTrendResultDTO buildMonthTrendResult(List<STREAM_TREND_INF_DTO> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return null;
        }

        STREAM_TREND_INF_DTO firstItem = dataList.get(0);
        MonthTrendResultDTO result = new MonthTrendResultDTO();

        // 设置基本信息
        result.setPartId(firstItem.getF_PART());
        result.setPartName(firstItem.getPartName());
        result.setPtrvId(firstItem.getF_PTRV());
        result.setPtrvName(firstItem.getPtrvName());
        result.setPrcsId(firstItem.getF_PRCS());
        result.setPrcsName(firstItem.getPrcsName());
        result.setTestId(firstItem.getF_TEST());
        result.setTestName(firstItem.getTestName());

        // 查询公差限信息
        SPEC_INF specInfo = querySpecInfo(firstItem.getF_PART(), firstItem.getF_PRCS(), firstItem.getF_TEST(), firstItem.getF_PTRV());
        if (specInfo != null) {
            result.setUsl(specInfo.getF_USL());
            result.setTar(specInfo.getF_TAR());
            result.setLsl(specInfo.getF_LSL());
            result.setTargetCpk(specInfo.getF_CPK());
        }

        // 构建月度数据
        Map<String, Integer> monthlySubgroupCount = new HashMap<>();
        Map<String, Double> monthlyCpk = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        for (STREAM_TREND_INF_DTO item : dataList) {
            if (item.getF_START() != null) {
                String monthKey = item.getF_START().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(formatter);
                monthlySubgroupCount.put(monthKey, item.getF_SGRP_COUNT());
                monthlyCpk.put(monthKey, item.getF_CPK());
            }
        }

        result.setMonthlySubgroupCount(monthlySubgroupCount);
        result.setMonthlyCpk(monthlyCpk);

        // 计算最新月份和上月数据
        calculateLatestAndPreviousData(result, monthlyCpk);

        // 计算改善幅度和能力分析
        result.calculateImprovementRate();
        result.analyzeCapability();

        return result;
    }

    /**
     * 查询公差限信息
     */
    private SPEC_INF querySpecInfo(Long partId, Long prcsId, Long testId, Long ptrvId) {
        LambdaQueryWrapper<SPEC_INF> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SPEC_INF::getF_PART, partId)
               .eq(SPEC_INF::getF_PRCS, prcsId)
               .eq(SPEC_INF::getF_TEST, testId)
               .eq(SPEC_INF::getF_PTRV, ptrvId)
               .eq(SPEC_INF::getF_DEL, YesOrNoEnum.NO.getType());

        final List<SPEC_INF> specInfs = specInfService.getBaseMapper().selectList(wrapper);
        if (specInfs.isEmpty()) {
            return null;
        }
        return specInfs.get(0);
    }

    /**
     * 批量查询项目负责人信息
     */
    private Map<Long, ResponsiblePersonInfo> batchQueryResponsiblePersons(Set<Long> testIds) {
        if (testIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 批量查询EMPL_RESPONSIBLE_INF表
            List<String> testIdStrings = testIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());

            LambdaQueryWrapper<EMPL_RESPONSIBLE_INF> responsibleWrapper = new LambdaQueryWrapper<>();
            responsibleWrapper.in(EMPL_RESPONSIBLE_INF::getF_DATA, testIdStrings)
                             .eq(EMPL_RESPONSIBLE_INF::getF_TYPE, 0)
                             .eq(EMPL_RESPONSIBLE_INF::getF_DEL, DelFlagEnum.USE.getType());

            List<EMPL_RESPONSIBLE_INF> responsibleList = emplResponsibleInfService.list(responsibleWrapper);

            if (responsibleList.isEmpty()) {
                return new HashMap<>();
            }

            // 提取员工ID列表
            Set<Long> emplIds = responsibleList.stream()
                    .map(EMPL_RESPONSIBLE_INF::getF_EMPL)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (emplIds.isEmpty()) {
                return new HashMap<>();
            }

            // 批量查询员工信息
            Map<Long, AdminUserRespDTO> userMap = new HashMap<>();
            for (Long emplId : emplIds) {
                try {
                    AdminUserRespDTO adminUser = remoteUserService.getUser(emplId).getData();
                    if (adminUser != null) {
                        userMap.put(emplId, adminUser);
                    }
                } catch (Exception e) {
                    log.warn("远程查询员工信息失败，emplId: {}, error: {}", emplId, e.getMessage());
                }
            }

            // 构建结果Map：testId -> ResponsiblePersonInfo
            return responsibleList.stream()
                    .filter(resp -> resp.getF_EMPL() != null && userMap.containsKey(resp.getF_EMPL()))
                    .collect(Collectors.toMap(
                            resp -> Long.valueOf(resp.getF_DATA()),
                            resp -> {
                                AdminUserRespDTO user = userMap.get(resp.getF_EMPL());
                                return new ResponsiblePersonInfo(user.getId(), user.getNickname());
                            },
                            (existing, replacement) -> existing // 如果有重复，保留第一个
                    ));

        } catch (Exception e) {
            log.error("批量查询项目负责人失败，testIds: {}, error: {}", testIds, e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 计算最新月份和上月数据
     */
    private void calculateLatestAndPreviousData(MonthTrendResultDTO result, Map<String, Double> monthlyCpk) {
        if (monthlyCpk.isEmpty()) {
            return;
        }

        // 按月份排序，获取最新和上一个月的数据
        List<String> sortedMonths = monthlyCpk.keySet().stream()
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());

        if (!sortedMonths.isEmpty()) {
            String latestMonth = sortedMonths.get(0);
            result.setLatestMonth(latestMonth);
            result.setLatestCpk(monthlyCpk.get(latestMonth));

            if (sortedMonths.size() > 1) {
                String previousMonth = sortedMonths.get(1);
                result.setPreviousCpk(monthlyCpk.get(previousMonth));
            }
        }
    }


}
