package com.yingfei.dataManagement.controller.gauge;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.dataManagement.service.gauge.GAUGE_FORMATService;
import com.yingfei.entity.domain.GAUGE_FORMAT;
import com.yingfei.entity.dto.GAUGE_FORMAT_DTO;
import com.yingfei.entity.vo.GAUGE_FORMAT_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "量具解析规则信息API")
@RestController
@RequestMapping("/gauge_format")
public class GAUGE_FORMATController extends BaseController {

    @Resource
    private GAUGE_FORMATService gaugeFormatService;

    /**
     * 获取量具解析规则信息列表
     */
    @ApiOperation("获取量具解析规则信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody GAUGE_FORMAT_VO gaugeFormatVo) {
        List<GAUGE_FORMAT_DTO> list = gaugeFormatService.getList(gaugeFormatVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(gaugeFormatService.getTotal(gaugeFormatVo));
        return dataTable;
    }

    @ApiOperation("获取量具解析规则信息列表")
    @PostMapping("/info/{id}")
    public R<?> info(@PathVariable Long id) {
        GAUGE_FORMAT_DTO gaugeFormatDto = gaugeFormatService.info(id);
        return R.ok(gaugeFormatDto);
    }

    /**
     * 新增量具解析规则信息
     */
    @CreateUpdateBy
    @NotResubmit
    @Log(title = "量具解析规则管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增量具解析规则信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody GAUGE_FORMAT_VO gaugeFormatVo) {
        gaugeFormatService.checkParam(gaugeFormatVo);
        gaugeFormatService.add(gaugeFormatVo);
        return R.ok();
    }

    /**
     * 修改量具解析规则信息
     */
    @Log(title = "量具解析规则管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改量具解析规则信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody GAUGE_FORMAT_VO gaugeFormatVo) {
        gaugeFormatService.checkParam(gaugeFormatVo);
        GAUGE_FORMAT gaugeFormat = new GAUGE_FORMAT();
        BeanUtils.copyPropertiesIgnoreNull(gaugeFormatVo, gaugeFormat);
        if (CollectionUtils.isNotEmpty(gaugeFormatVo.getGaugeFormatConfigDtoList())) {
            gaugeFormat.setF_DATA_CONFIG(JSONArray.toJSONString(gaugeFormatVo.getGaugeFormatConfigDtoList()));
        }
        if (gaugeFormatVo.getGaugeFormatAdvancedDto() != null) {
            gaugeFormat.setF_ADVANCED(JSONObject.toJSONString(gaugeFormatVo.getGaugeFormatAdvancedDto()));
        }
        gaugeFormatService.updateById(gaugeFormat);
        return R.ok();
    }

    /**
     * 批量删除量具解析规则信息
     */
    @Log(title = "量具解析规则管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除量具解析规则信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        gaugeFormatService.del(ids);
        return R.ok();
    }
}
