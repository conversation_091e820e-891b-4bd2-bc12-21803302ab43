package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.PART_TEST_INF;
import com.yingfei.entity.dto.PART_TEST_INF_DTO;
import com.yingfei.entity.vo.PART_TEST_INF_VO;
import com.yingfei.entity.vo.excel.PART_TEST_EXCEL_VO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【PART_TEST_INF】的数据库操作Service
* @createDate 2024-05-08 16:28:06
*/
public interface PART_TEST_INFService extends IService<PART_TEST_INF>, BaseService<PART_TEST_INF_VO, PART_TEST_INF_DTO> {

    List<PART_TEST_INF> getByPlan(PART_TEST_INF_VO partTestInfVo);

    void analyzeExcel(List<MultipartFile> files);

    Map<String, String> importData(List<PART_TEST_EXCEL_VO> partTestExcelVoList);
}
