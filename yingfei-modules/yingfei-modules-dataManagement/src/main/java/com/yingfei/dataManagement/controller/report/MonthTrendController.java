package com.yingfei.dataManagement.controller.report;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.dataManagement.service.report.MonthTrendService;
import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.dto.report.MonthTrendQueryDTO;
import com.yingfei.entity.dto.report.MonthTrendResultDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "图表:月能力趋势API")
@RequestMapping("/monthTrend")
public class MonthTrendController {
    @Resource
    private MonthTrendService monthTrendService;

    @PostMapping("/getMonthTrend")
    @ApiOperation("查询月度能力趋势报表")
    public TableDataInfo<MonthTrendResultDTO> getMonthTrend(@RequestBody MonthTrendQueryDTO monthTrendQueryDTO) {
        return monthTrendService.getMonthTrend(monthTrendQueryDTO);
    }
}
