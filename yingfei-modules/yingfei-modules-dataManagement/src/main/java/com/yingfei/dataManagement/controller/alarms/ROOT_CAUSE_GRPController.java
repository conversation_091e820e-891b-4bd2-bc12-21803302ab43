package com.yingfei.dataManagement.controller.alarms;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.ROOT_CAUSE_GRPService;
import com.yingfei.entity.domain.ROOT_CAUSE_GRP;
import com.yingfei.entity.dto.ROOT_CAUSE_GRP_DTO;
import com.yingfei.entity.vo.ROOT_CAUSE_GRP_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "异常原因组信息API")
@RestController
@RequestMapping("/root_cause_grp")
public class ROOT_CAUSE_GRPController extends BaseController {
    
    @Resource
    private ROOT_CAUSE_GRPService rootCauseGrpService;

    /**
     * 获取异常原因组信息列表
     */
    @ApiOperation("获取异常原因组信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody ROOT_CAUSE_GRP_VO rootCauseGrpVo) {
        List<ROOT_CAUSE_GRP_DTO> list = rootCauseGrpService.getList(rootCauseGrpVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(rootCauseGrpService.getTotal(rootCauseGrpVo));
        return dataTable;
    }

    /**
     * 新增异常原因组信息
     */
    @CreateUpdateBy
    @RequiresPermissions("dataManagement:rootCauseGrp:add")
    @Log(title = "异常原因组管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增异常原因组信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody ROOT_CAUSE_GRP_VO rootCauseGrpVo) {
        rootCauseGrpService.checkParam(rootCauseGrpVo);
        rootCauseGrpService.add(rootCauseGrpVo);
        return R.ok();
    }

    /**
     * 修改异常原因组信息
     */
    @RequiresPermissions("dataManagement:rootCauseGrp:edit")
    @Log(title = "异常原因组管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改异常原因组信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody ROOT_CAUSE_GRP_VO rootCauseGrpVo) {
        rootCauseGrpService.checkParam(rootCauseGrpVo);
        ROOT_CAUSE_GRP rootCauseGrp = new ROOT_CAUSE_GRP();
        BeanUtils.copyPropertiesIgnoreNull(rootCauseGrpVo, rootCauseGrp);
        rootCauseGrpService.updateById(rootCauseGrp);
        return R.ok();
    }

    /**
     * 批量删除异常原因组信息
     */
    @RequiresPermissions("dataManagement:rootCauseGrp:remove")
    @Log(title = "异常原因组管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除异常原因组信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        rootCauseGrpService.del(ids);
        return R.ok();
    }
}
