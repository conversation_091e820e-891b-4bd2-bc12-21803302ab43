package com.yingfei.dataManagement.controller.basic;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.DESC_GRPService;
import com.yingfei.entity.domain.DESC_GRP;
import com.yingfei.entity.dto.DESC_GRP_DTO;
import com.yingfei.entity.vo.DESC_GRP_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "自定义描述符组信息API")
@RestController
@RequestMapping("/desc_grp")
public class DESC_GRPController extends BaseController {
    
    @Resource
    private DESC_GRPService descGrpService;

    /**
     * 获取自定义描述符组信息列表
     */
    @ApiOperation("获取自定义描述符组信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody DESC_GRP_VO descGrpVo) {
        List<DESC_GRP_DTO> list = descGrpService.getList(descGrpVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(descGrpService.getTotal(descGrpVo));
        return dataTable;
    }

    /**
     * 新增自定义描述符组信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:descGrp:add")
    @Log(title = "自定义描述符组管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增自定义描述符组信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody DESC_GRP_VO descGrpVo) {
        descGrpService.checkParam(descGrpVo);
        descGrpService.add(descGrpVo);
        return R.ok();
    }

    /**
     * 修改自定义描述符组信息
     */
    @RequiresPermissions("dataManagement:descGrp:edit")
    @Log(title = "自定义描述符组管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改自定义描述符组信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody DESC_GRP_VO descGrpVo) {
        descGrpService.checkParam(descGrpVo);
        DESC_GRP descGrp = new DESC_GRP();
        BeanUtils.copyPropertiesIgnoreNull(descGrpVo, descGrp);
        descGrpService.updateById(descGrp);
        return R.ok();
    }

    /**
     * 批量删除自定义描述符组信息
     */
    @RequiresPermissions("dataManagement:descGrp:remove")
    @Log(title = "自定义描述符组管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除自定义描述符组信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        descGrpService.del(ids);
        return R.ok();
    }
}
