package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.JOB_DATService;
import com.yingfei.entity.domain.JOB_DAT;
import com.yingfei.entity.dto.JOB_DAT_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.vo.JOB_DAT_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "工单信息API")
@RestController
@RequestMapping("/job_dat")
public class JOB_DATController extends BaseController {
    
    @Resource
    private JOB_DATService jobDatService;

    /**
     * 获取工单信息列表
     */
    @ApiOperation("获取工单信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody JOB_DAT_VO jobDatVo) {
        List<JOB_DAT_DTO> list = jobDatService.getList(jobDatVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(jobDatService.getTotal(jobDatVo));
        return dataTable;
    }

    /**
     * 新增工单信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:jobDat:add")
    @Log(title = "工单管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增工单信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody JOB_DAT_VO jobDatVo) {
        jobDatService.checkParam(jobDatVo);
        JOB_DAT jobDat = jobDatService.addJobDat(jobDatVo);
        return R.ok(jobDat);
    }

    /**
     * 修改工单信息
     */
    @RequiresPermissions("dataManagement:jobDat:edit")
    @Log(title = "工单管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改工单信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody JOB_DAT_VO jobDatVo) {
        jobDatService.checkParam(jobDatVo);
        JOB_DAT jobDat = new JOB_DAT();
        BeanUtils.copyPropertiesIgnoreNull(jobDatVo, jobDat);
        jobDatService.updateById(jobDat);
        return R.ok();
    }

    /**
     * 批量删除工单信息
     */
    @RequiresPermissions("dataManagement:jobDat:remove")
    @Log(title = "工单管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除工单信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        jobDatService.del(ids);
        return R.ok();
    }

    @ApiOperation("获取工单信息")
    @PostMapping("/getInfo/{id}")
    public R<?> getInfo(@PathVariable Long id) {
        JOB_DAT_DTO jobDatDto = jobDatService.info(id);
        return R.ok(jobDatDto);
    }

    @ApiOperation("获取工单删除影响的关联信息")
    @PostMapping("/getOperationAssociation/{ids}")
    public R<?> getOperationAssociation(@PathVariable List<Long> ids) {
        OperationAssociationDTO operationAssociationDTO = jobDatService.getOperationAssociation(ids);
        return R.ok(operationAssociationDTO);
    }
}
