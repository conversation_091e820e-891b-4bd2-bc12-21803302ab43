package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.JOB_GRP;
import com.yingfei.entity.dto.JOB_GRP_DTO;
import com.yingfei.entity.vo.JOB_GRP_VO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【JOB_GRP(储存工单组信息表)】的数据库操作Service
* @createDate 2024-05-08 16:27:37
*/
public interface JOB_GRPService extends IService<JOB_GRP>, BaseService<JOB_GRP_VO, JOB_GRP_DTO> {

    JOB_GRP_DTO getInfo(Long id, List<Long> childIds);
}
