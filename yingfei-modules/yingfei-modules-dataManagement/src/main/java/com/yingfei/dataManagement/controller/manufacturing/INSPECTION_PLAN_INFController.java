package com.yingfei.dataManagement.controller.manufacturing;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.dataManagement.service.manufacturing.INSPECTION_PLAN_INFService;
import com.yingfei.entity.domain.DICT_INF;
import com.yingfei.entity.domain.INSPECTION_PLAN_INF;
import com.yingfei.entity.dto.DataValConfigDTO;
import com.yingfei.entity.dto.INSPECTION_PLAN_INF_DTO;
import com.yingfei.entity.enums.ChildPlanAttributesEnum;
import com.yingfei.entity.enums.DataValTypeEnum;
import com.yingfei.entity.enums.PlanCommonAttributesEnums;
import com.yingfei.entity.vo.INSPECTION_PLAN_INF_VO;
import com.yingfei.entity.vo.SerialDebuggingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.MapUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Api(tags = "检查计划信息API")
@RestController
@RequestMapping("/inspection_plan_inf")
public class INSPECTION_PLAN_INFController extends BaseController {

    @Resource
    private INSPECTION_PLAN_INFService inspectionPlanInfService;
    @Resource
    private RedisService redisService;

    /**
     * 获取检查计划列表
     */
    @ApiOperation("获取检查计划列表")
    @PostMapping("/list")
    public R<?> list(@RequestBody INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        List<INSPECTION_PLAN_INF_DTO> inspectionPlanInfDtoList = inspectionPlanInfService.getList(inspectionPlanInfVo);
        return R.ok(inspectionPlanInfDtoList);
    }

    /**
     * 获取检查计划信息
     */
    @ApiOperation("获取检查计划信息")
    @PostMapping("/getInfo/{id}")
    public R<?> getInfo(@PathVariable Long id) {
        INSPECTION_PLAN_INF_DTO inspectionPlanInfDto = inspectionPlanInfService.getInfo(id);
        return R.ok(inspectionPlanInfDto);
    }

    /**
     * 新增检查计划信息
     */
    @CreateUpdateBy
    @Log(title = "检查计划管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增检查计划信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        Long id = inspectionPlanInfService.add(inspectionPlanInfVo);
        return R.ok(id);
    }

    /**
     * 修改检查计划信息
     */
    @Log(title = "检查计划管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改检查计划信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        inspectionPlanInfService.edit(inspectionPlanInfVo);
        return R.ok();
    }

    /**
     * 批量删除检查计划信息
     */
    @Log(title = "检查计划管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除检查计划信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        inspectionPlanInfService.del(ids);
        return R.ok();
    }

    /**
     * 获取子计划属性列表
     */
    @ApiOperation("获取子计划属性列表")
    @GetMapping("/attributeList")
    public R<?> attributeList(HttpServletRequest request) {
        Map<String, List<DICT_INF>> cacheMap = redisService.getCacheMap(RedisConstant.DICT_CACHE);
        return R.ok(ChildPlanAttributesEnum.getMap(cacheMap));
    }

    /**
     * 获取检查计划通用属性配置
     */
    @ApiOperation("获取检查计划通用属性配置")
    @GetMapping("/attributeConfiguration")
    public R<?> attributeConfiguration() {
        return R.ok(PlanCommonAttributesEnums.getMap());
    }

    /**
     * 获取数据库取值查询条件配置
     */
    @ApiOperation("获取数据库取值查询条件配置")
    @GetMapping("/dataValConfig")
    public R<?> dataValConfig() {
        return R.ok(DataValTypeEnum.getMap());
    }

    /**
     * 量具取值:光标到测试值获取框触发
     */
    @ApiOperation("量具取值:光标到测试值获取框触发")
    @PostMapping("/publish")
    public R<?> publish(@RequestBody SerialDebuggingVO serialDebuggingVO) {
        inspectionPlanInfService.publish(serialDebuggingVO);
        return R.ok();
    }

    /**
     * 数据库取值
     */
    @ApiOperation("数据库取值")
    @PostMapping("/getDatabaseHistoricalVal")
    public R<?> getDatabaseHistoricalVal(@RequestBody DataValConfigDTO dataValConfigDTO) {
        if (MapUtils.isEmpty(dataValConfigDTO.getMap()))
            return R.fail("查询条件未配置");
        Double calculate = inspectionPlanInfService.getDatabaseHistoricalVal(dataValConfigDTO);
        return R.ok(calculate);

    }

    /**
     * 获取动态检验计划
     */
    @ApiOperation("获取动态检验计划")
    @PostMapping("/getDynamicInspectionPlan")
    public R<?> getDynamicInspectionPlan(@RequestBody INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        List<INSPECTION_PLAN_INF> inspectionPlanInfDtoList = inspectionPlanInfService.getDynamicInspectionPlan(inspectionPlanInfVo);
        return R.ok(inspectionPlanInfDtoList);
    }
}
