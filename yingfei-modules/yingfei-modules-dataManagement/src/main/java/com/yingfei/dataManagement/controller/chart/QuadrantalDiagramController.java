package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.service.chart.QuadrantalDiagramService;
import com.yingfei.entity.dto.QuadrantalDiagramDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "图表:能力象限图API")
@RequestMapping("/quadrantalDiagram")
public class QuadrantalDiagramController {

    @Resource
    private QuadrantalDiagramService quadrantalDiagramService;

    /**
     * 获取聚合分析详情
     *
     * @return
     */
    @PostMapping("/info")
    @ApiOperation("获取能力象限图详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = QuadrantalDiagramDTO.class)
    })
    public R<?> getQuadrantalDiagramInfo(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        List<QuadrantalDiagramDTO> quadrantalDiagramDTOList = quadrantalDiagramService.getQuadrantalDiagramInfo(subgroupDataSelectionDTO);
        return R.ok(quadrantalDiagramDTOList);
    }

}
