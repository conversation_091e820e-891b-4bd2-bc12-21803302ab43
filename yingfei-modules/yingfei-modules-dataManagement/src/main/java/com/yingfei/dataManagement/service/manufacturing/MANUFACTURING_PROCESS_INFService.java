package com.yingfei.dataManagement.service.manufacturing;

import com.alibaba.fastjson2.JSONArray;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.MANUFACTURING_PROCESS_INF;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.MANUFACTURING_PROCESS_INF_DTO;
import com.yingfei.entity.vo.MANUFACTURING_PROCESS_INF_VO;

import java.util.List;

/**
* 
* @description 针对表【MANUFACTURING_PROCESS_INF(流程图结构表)】的数据库操作Service
* @createDate 2024-06-13 15:01:23
*/
public interface MANUFACTURING_PROCESS_INFService extends IService<MANUFACTURING_PROCESS_INF>, BaseService<MANUFACTURING_PROCESS_INF_VO, MANUFACTURING_PROCESS_INF_DTO> {

    void edit(MANUFACTURING_PROCESS_INF_VO manufacturingProcessInfVo);

    JSONArray getManufacturingInfo();
}
