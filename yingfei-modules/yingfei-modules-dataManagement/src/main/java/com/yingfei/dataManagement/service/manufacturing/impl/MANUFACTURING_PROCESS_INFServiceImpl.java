package com.yingfei.dataManagement.service.manufacturing.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.mapper.MANUFACTURING_PROCESS_INFMapper;
import com.yingfei.dataManagement.service.TEST_INFService;
import com.yingfei.dataManagement.service.manufacturing.INSPECTION_PLAN_INFService;
import com.yingfei.dataManagement.service.manufacturing.MANUFACTURING_NODE_INFService;
import com.yingfei.dataManagement.service.manufacturing.MANUFACTURING_PROCESS_INFService;
import com.yingfei.entity.domain.INSPECTION_PLAN_INF;
import com.yingfei.entity.domain.MANUFACTURING_NODE_INF;
import com.yingfei.entity.domain.MANUFACTURING_PROCESS_INF;
import com.yingfei.entity.domain.TEST_INF;
import com.yingfei.entity.dto.INSPECTION_PLAN_CHILD_DTO;
import com.yingfei.entity.dto.INSPECTION_PLAN_INF_DTO;
import com.yingfei.entity.dto.MANUFACTURING_NODE_INF_DTO;
import com.yingfei.entity.dto.MANUFACTURING_PROCESS_INF_DTO;
import com.yingfei.entity.vo.INSPECTION_PLAN_INF_VO;
import com.yingfei.entity.vo.MANUFACTURING_NODE_INF_VO;
import com.yingfei.entity.vo.MANUFACTURING_PROCESS_INF_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description 针对表【MANUFACTURING_PROCESS_INF(流程图结构表)】的数据库操作Service实现
 * @createDate 2024-06-13 15:01:23
 */
@Service
public class MANUFACTURING_PROCESS_INFServiceImpl extends ServiceImpl<MANUFACTURING_PROCESS_INFMapper, MANUFACTURING_PROCESS_INF>
        implements MANUFACTURING_PROCESS_INFService {

    @Resource
    private MANUFACTURING_NODE_INFService manufacturingNodeInfService;
    @Resource
    private INSPECTION_PLAN_INFService inspectionPlanInfService;
    @Resource
    private TEST_INFService testInfService;

    @Override
    public long getTotal(MANUFACTURING_PROCESS_INF_VO manufacturingProcessInfVo) {
        return baseMapper.getTotal(manufacturingProcessInfVo);
    }

    @Override
    public List<MANUFACTURING_PROCESS_INF_DTO> getList(MANUFACTURING_PROCESS_INF_VO manufacturingProcessInfVo) {
        return baseMapper.getList(manufacturingProcessInfVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(MANUFACTURING_PROCESS_INF_VO manufacturingProcessInfVo) {
        MANUFACTURING_PROCESS_INF manufacturingProcessInf = new MANUFACTURING_PROCESS_INF();
        BeanUtils.copyPropertiesIgnoreNull(manufacturingProcessInfVo, manufacturingProcessInf);
        baseMapper.insert(manufacturingProcessInf);

        /*新增节点数据*/
        if (CollectionUtils.isNotEmpty(manufacturingProcessInfVo.getManufacturingNodeInfVoList())) {
            for (MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo : manufacturingProcessInfVo.getManufacturingNodeInfVoList()) {
                manufacturingNodeInfVo.setF_CRUE(manufacturingProcessInf.getF_CRUE());
                manufacturingNodeInfVo.setF_EDUE(manufacturingProcessInf.getF_EDUE());
                manufacturingNodeInfVo.setF_MFPS(manufacturingProcessInf.getF_MFPS());
                manufacturingNodeInfService.add(manufacturingNodeInfVo);
            }
        }

        /*新增检查计划数据*/
        if (CollectionUtils.isNotEmpty(manufacturingProcessInfVo.getInspectionPlanInfVoList())) {
            for (INSPECTION_PLAN_INF_VO inspectionPlanInfVo : manufacturingProcessInfVo.getInspectionPlanInfVoList()) {
                inspectionPlanInfVo.setF_CRUE(manufacturingProcessInf.getF_CRUE());
                inspectionPlanInfVo.setF_EDUE(manufacturingProcessInf.getF_EDUE());
                inspectionPlanInfVo.setF_MFPS(manufacturingProcessInf.getF_MFPS());
                inspectionPlanInfService.add(inspectionPlanInfVo);

            }
        }
    }

    @Override
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        /*根据id列表修改对应的删除状态*/
        LambdaUpdateWrapper<MANUFACTURING_PROCESS_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(MANUFACTURING_PROCESS_INF::getF_MFPS, ids).eq(MANUFACTURING_PROCESS_INF::getF_DEL, DelFlagEnum.USE.getType())
                .set(MANUFACTURING_PROCESS_INF::getF_DEL, YesOrNoEnum.YES.getType());
        baseMapper.update(null, updateWrapper);

        /*删除对应节点*/
        LambdaUpdateWrapper<MANUFACTURING_NODE_INF> nodeUpdateWrapper = new LambdaUpdateWrapper<>();
        nodeUpdateWrapper.in(MANUFACTURING_NODE_INF::getF_MFPS, ids).eq(MANUFACTURING_NODE_INF::getF_DEL, DelFlagEnum.USE.getType())
                .set(MANUFACTURING_NODE_INF::getF_DEL, YesOrNoEnum.YES.getType());
        manufacturingNodeInfService.update(null, nodeUpdateWrapper);

        /*删除对应计划*/
        LambdaUpdateWrapper<INSPECTION_PLAN_INF> planUpdateWrapper = new LambdaUpdateWrapper<>();
        planUpdateWrapper.in(INSPECTION_PLAN_INF::getF_MFPS, ids).eq(INSPECTION_PLAN_INF::getF_DEL, DelFlagEnum.USE.getType())
                .set(INSPECTION_PLAN_INF::getF_DEL, YesOrNoEnum.YES.getType());
        inspectionPlanInfService.update(null, planUpdateWrapper);
    }

    @Override
    public void checkParam(MANUFACTURING_PROCESS_INF_VO manufacturingProcessInfVo) {
        if (StringUtils.isEmpty(manufacturingProcessInfVo.getF_NAME())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        LambdaQueryWrapper<MANUFACTURING_PROCESS_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MANUFACTURING_PROCESS_INF::getF_NAME, manufacturingProcessInfVo.getF_NAME())
                .eq(MANUFACTURING_PROCESS_INF::getF_DEL, DelFlagEnum.USE.getType());
        List<MANUFACTURING_PROCESS_INF> manufacturingProcessInfList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(manufacturingProcessInfList)) return;
        if (StringUtils.isNotEmpty(manufacturingProcessInfVo.getF_MFPS())) {
            if (manufacturingProcessInfList.size() > 1 || !Objects.equals(manufacturingProcessInfList.get(0).getF_MFPS(), manufacturingProcessInfVo.getF_MFPS())) {
                throw new BusinessException(DataManagementExceptionEnum.MANUFACTURING_PROCESS_NAME_DUPLICATION_EXCEPTION);
            }
        } else {
            if (manufacturingProcessInfList.size() > 0) {
                throw new BusinessException(DataManagementExceptionEnum.MANUFACTURING_PROCESS_NAME_DUPLICATION_EXCEPTION);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(MANUFACTURING_PROCESS_INF_VO manufacturingProcessInfVo) {
        MANUFACTURING_PROCESS_INF manufacturingProcessInf = new MANUFACTURING_PROCESS_INF();
        BeanUtils.copyPropertiesIgnoreNull(manufacturingProcessInfVo, manufacturingProcessInf);
        baseMapper.updateById(manufacturingProcessInf);

        /*编辑节点数据*/
        if (CollectionUtils.isNotEmpty(manufacturingProcessInfVo.getManufacturingNodeInfVoList())) {
            for (MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo : manufacturingProcessInfVo.getManufacturingNodeInfVoList()) {
                MANUFACTURING_NODE_INF inf = manufacturingNodeInfService.getById(manufacturingNodeInfVo.getF_MFND());
                if (inf == null) {
                    /*未在数据库则新增*/
                    manufacturingNodeInfVo.setF_CRUE(manufacturingProcessInf.getF_CRUE());
                    manufacturingNodeInfVo.setF_EDUE(manufacturingProcessInf.getF_EDUE());
                    manufacturingNodeInfService.add(manufacturingNodeInfVo);
                    continue;
                }
                manufacturingNodeInfVo.setF_EDUE(manufacturingProcessInf.getF_EDUE());
                manufacturingNodeInfService.edit(manufacturingNodeInfVo);
            }
        }

        /*编辑检查计划数据*/
        if (CollectionUtils.isNotEmpty(manufacturingProcessInfVo.getInspectionPlanInfVoList())) {
            for (INSPECTION_PLAN_INF_VO inspectionPlanInfVo : manufacturingProcessInfVo.getInspectionPlanInfVoList()) {
                INSPECTION_PLAN_INF inf = inspectionPlanInfService.getById(inspectionPlanInfVo.getF_PLAN());
                if (inf == null) {
                    /*未在数据库则新增*/
                    inspectionPlanInfVo.setF_CRUE(manufacturingProcessInf.getF_CRUE());
                    inspectionPlanInfVo.setF_EDUE(manufacturingProcessInf.getF_EDUE());
                    inspectionPlanInfService.add(inspectionPlanInfVo);
                    continue;
                }
                inspectionPlanInfVo.setF_CRUE(inf.getF_CRUE());
                inspectionPlanInfVo.setF_EDUE(manufacturingProcessInf.getF_EDUE());
                inspectionPlanInfService.edit(inspectionPlanInfVo);
            }
        }

        /*删除节点数据*/
        if (CollectionUtils.isNotEmpty(manufacturingProcessInfVo.getManufacturingNodeDelList())) {
            manufacturingNodeInfService.del(manufacturingProcessInfVo.getManufacturingNodeDelList());
        }

        /*删除检查计划数据*/
        if (CollectionUtils.isNotEmpty(manufacturingProcessInfVo.getInspectionPlanDelList())) {
            inspectionPlanInfService.del(manufacturingProcessInfVo.getInspectionPlanDelList());
        }
    }

    @Override
    public JSONArray getManufacturingInfo() {
        MANUFACTURING_PROCESS_INF_VO manufacturingProcessInfVo = new MANUFACTURING_PROCESS_INF_VO();
        manufacturingProcessInfVo.setNext(Constants.NEXT);
        List<MANUFACTURING_PROCESS_INF_DTO> list = getList(manufacturingProcessInfVo);
        JSONArray manufacturingInfoList = new JSONArray();
        if (CollectionUtils.isEmpty(list)) return manufacturingInfoList;
        list.forEach(manufacturingProcessInfDto -> {
            JSONObject manufacturingProcessInf = new JSONObject();
            manufacturingProcessInf.put("F_MFPS",manufacturingProcessInfDto.getF_MFPS());
            manufacturingProcessInf.put("MFPSName",manufacturingProcessInfDto.getF_NAME());

            MANUFACTURING_NODE_INF_VO manufacturingNodeInfVo = new MANUFACTURING_NODE_INF_VO();
            manufacturingNodeInfVo.setF_MFPS(manufacturingProcessInfDto.getF_MFPS());
            manufacturingNodeInfVo.setNext(Constants.NEXT);
            manufacturingNodeInfVo.setDbType(InitConfig.getDriverType());
            List<MANUFACTURING_NODE_INF_DTO> nodeInfDtoList = manufacturingNodeInfService.getList(manufacturingNodeInfVo);
            JSONArray nodeList = new JSONArray();
            nodeInfDtoList.forEach(manufacturingNodeInfDto -> {
                JSONObject node = new JSONObject();
                node.put("F_MFND",manufacturingNodeInfDto.getF_MFND());
                node.put("MFNDName",manufacturingNodeInfDto.getF_NAME());

                INSPECTION_PLAN_INF_VO inspectionPlanInfVo = new INSPECTION_PLAN_INF_VO();
                inspectionPlanInfVo.setF_MFND(manufacturingNodeInfDto.getF_MFND());
                inspectionPlanInfVo.setNext(Constants.NEXT);
                List<INSPECTION_PLAN_INF_DTO> inspectionPlanInfDtoList = inspectionPlanInfService.getList(inspectionPlanInfVo);
                JSONArray planList = new JSONArray();
                inspectionPlanInfDtoList.forEach(inspectionPlanInfDto -> {
                    JSONObject plan = new JSONObject();
                    plan.put("F_PLAN",inspectionPlanInfDto.getF_PLAN());
                    plan.put("PLANName",inspectionPlanInfDto.getF_NAME());
//                    INSPECTION_PLAN_INF_VO planInfVo = new INSPECTION_PLAN_INF_VO();
//                    BeanUtils.copyPropertiesIgnoreNull(inspectionPlanInfDto, planInfVo);
//                    if (StringUtils.isNotEmpty(inspectionPlanInfDto.getF_DATA())) {
//                        INSPECTION_PLAN_CONFIGURATION_DTO inspectionPlanConfigurationDto =
//                                JSONObject.parseObject(inspectionPlanInfDto.getF_DATA(), INSPECTION_PLAN_CONFIGURATION_DTO.class);
//                        planInfVo.setInspectionPlanConfigurationDto(inspectionPlanConfigurationDto);
//                    }
                    if (StringUtils.isNotEmpty(inspectionPlanInfDto.getF_CHILD())) {
                        Map<String, List<INSPECTION_PLAN_CHILD_DTO>> map =
                                JSONObject.parseObject(inspectionPlanInfDto.getF_CHILD())
                                        .entrySet().stream()
                                        .collect(Collectors.toMap(Map.Entry::getKey, entry ->
                                                JSONArray.parseArray(String.valueOf(entry.getValue()), INSPECTION_PLAN_CHILD_DTO.class)));
                        JSONArray child = new JSONArray();
                        map.forEach((k,v)->{
                            v.forEach(inspectionPlanChildDto -> {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("childName",k);
                                jsonObject.put("childId",inspectionPlanChildDto.getChildId());
                                jsonObject.put("testId",inspectionPlanChildDto.getTestId());
                                TEST_INF testInf = testInfService.getById(inspectionPlanChildDto.getTestId());
                                jsonObject.put("testName",testInf.getF_NAME());
                                child.add(jsonObject);
                            });
                        });
                        plan.put("child",child);
                    }
                    planList.add(plan);
                });
                node.put("planList",planList);
                nodeList.add(node);
            });
            manufacturingProcessInf.put("nodeList",nodeList);
            manufacturingInfoList.add(manufacturingProcessInf);
        });
        return manufacturingInfoList;
    }

}




