package com.yingfei.dataManagement.service.schedule;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.domain.SCHEDULE_JOB_INF;

import java.util.List;

public interface SCHEDULE_JOB_INFService extends IService<SCHEDULE_JOB_INF> {

    /**
     * 保存定时任务
     *
     */
    void add(SCHEDULE_JOB_INF param);

    /**
     * 更新定时任务
     *
     */
    void delete(SCHEDULE_JOB_INF param);

    /**
     * 更新
     *
     */
    void update(SCHEDULE_JOB_INF param);


    /**
     * 启用定时器
     * @param jobIds
     */
    void run(List<Long> jobIds);


    /**
     * 暂停定时器
     * @param jobIds
     */
    void pause(List<Long> jobIds);

    /**
     * 恢复定时器
     * @param jobIds
     */
    void resume(List<Long> jobIds);


    /**
     * 批量删除定时任务
     */
    void deleteBatch(List<Long> jobIds);


    /**
     * 批量更新定时任务状态
     */
    void updateBatch(List<Long> jobIds, Integer status);

    /**
     * 删除对应标识定时任务
     * @param autoCollectIdentify
     * @param ids
     */
    void deleteIdentify(String autoCollectIdentify, List<Long> ids);
}

