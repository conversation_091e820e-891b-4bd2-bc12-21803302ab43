package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.SHIFT_DAT;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.SHIFT_DAT_DTO;
import com.yingfei.entity.vo.SHIFT_DAT_VO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【SHIFT_DAT(储存班次信息表)】的数据库操作Service
* @createDate 2024-05-08 16:28:36
*/
public interface SHIFT_DATService extends IService<SHIFT_DAT>, BaseService<SHIFT_DAT_VO, SHIFT_DAT_DTO> {

    List<SHIFT_DAT> getSearchCondition(Integer isInclude, List<Long> list);

    SHIFT_DAT_DTO info(Long id);

    OperationAssociationDTO getOperationAssociation(List<Long> ids);
}
