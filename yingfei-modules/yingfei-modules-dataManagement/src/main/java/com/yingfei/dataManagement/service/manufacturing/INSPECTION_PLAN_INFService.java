package com.yingfei.dataManagement.service.manufacturing;

import com.yingfei.entity.domain.INSPECTION_PLAN_INF;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.domain.SGRP_INF_UNFINISHED;
import com.yingfei.entity.dto.DataValConfigDTO;
import com.yingfei.entity.dto.INSPECTION_PLAN_INF_DTO;
import com.yingfei.entity.vo.INSPECTION_PLAN_INF_VO;
import com.yingfei.entity.vo.SerialDebuggingVO;

import java.util.List;

/**
* 
* @description 针对表【INSPECTION_PLAN_INF(检查计划表)】的数据库操作Service
* @createDate 2024-06-13 15:00:00
*/
public interface INSPECTION_PLAN_INFService extends IService<INSPECTION_PLAN_INF> {

    INSPECTION_PLAN_INF_DTO getInfo(Long id);

    Long add(INSPECTION_PLAN_INF_VO inspectionPlanInfVo);

    void del(List<Long> ids);

    void edit(INSPECTION_PLAN_INF_VO inspectionPlanInfVo);

    List<INSPECTION_PLAN_INF_DTO> getList(INSPECTION_PLAN_INF_VO inspectionPlanInfVo);

    long getTotal(INSPECTION_PLAN_INF_VO inspectionPlanInfVo);

    void publish(SerialDebuggingVO serialDebuggingVO);

    Double getDatabaseHistoricalVal(DataValConfigDTO dataValConfigDTO);

    List<INSPECTION_PLAN_INF_DTO> getWorkBoardPlan(INSPECTION_PLAN_INF_VO inspectionPlanInfVo);

    List<SGRP_INF_UNFINISHED> getUnfinishedList(INSPECTION_PLAN_INF_VO inspectionPlanInfVo);

    List<INSPECTION_PLAN_INF> getDynamicInspectionPlan(INSPECTION_PLAN_INF_VO inspectionPlanInfVo);
}
