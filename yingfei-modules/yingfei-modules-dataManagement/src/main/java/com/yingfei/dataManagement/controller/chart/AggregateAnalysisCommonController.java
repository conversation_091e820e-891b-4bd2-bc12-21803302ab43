package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.service.chart.AggregateAnalysisCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 聚合分析通用接口
 */
@Slf4j
@RestController
@Api(tags = "图表:聚合分析通用API")
@RequestMapping("/aggregateAnalysisCommon")
public class AggregateAnalysisCommonController {

    @Resource
    private AggregateAnalysisCommonService aggregateAnalysisCommonService;

    /**
     * 获取菜单对应参数集信息
     */
    @ApiOperation("获取菜单对应参数集信息")
    @GetMapping("/getParameter/{menuId}")
    public R<?> getParameter(@PathVariable Long menuId) {
        Map<String, Object> map = aggregateAnalysisCommonService.getParameter(menuId);
        return  R.ok(map);
    }

    /**
     * 通过参数集获取信息
     */
    @ApiOperation("通过参数集获取信息")
    @GetMapping("/findByParameterId/{id}")
    public R<?> findByParameterId(@PathVariable Long id){
        Map<String, Object> map = aggregateAnalysisCommonService.findByParameterId(id);
        return R.ok(map);
    }

}
