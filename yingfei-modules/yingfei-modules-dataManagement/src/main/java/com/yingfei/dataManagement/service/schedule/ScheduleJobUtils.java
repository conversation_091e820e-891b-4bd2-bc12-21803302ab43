package com.yingfei.dataManagement.service.schedule;


import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisLock;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.mapper.INSPECTION_PLAN_RECORD_INFMapper;
import com.yingfei.dataManagement.mapper.SCHEDULE_JOB_LOG_INFMapper;
import com.yingfei.dataManagement.service.SGRP_INFService;
import com.yingfei.dataManagement.service.home.HomeService;
import com.yingfei.dataManagement.service.manufacturing.INSPECTION_PLAN_INFService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.INSPECTION_PLAN_CONFIGURATION_DTO;
import com.yingfei.entity.enums.TimeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jexl3.JexlContext;
import org.apache.commons.jexl3.JexlEngine;
import org.apache.commons.jexl3.JexlScript;
import org.apache.commons.jexl3.MapContext;
import org.apache.commons.jexl3.internal.Engine;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ScheduleJobUtils extends QuartzJobBean {

    @Resource
    private RedisService redisService;

    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private RedisLock redisLock;
    @Resource
    private INSPECTION_PLAN_RECORD_INFMapper inspectionPlanRecordInfMapper;
    @Resource
    private INSPECTION_PLAN_INFService inspectionPlanInfService;
    @Resource
    private SCHEDULE_JOB_INFService scheduleJobInfService;
    @Resource
    private HomeService homeService;

    private static final ExecutorService cachedThreadPool = Executors.newCachedThreadPool();

    @Resource
    private SCHEDULE_JOB_LOG_INFMapper scheduleJobLogMapper;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {

        SCHEDULE_JOB_INF scheduleJob = (SCHEDULE_JOB_INF) context.getMergedJobDataMap()
                .get(Constants.JOB_PARAM_KEY);

        /*保证同一个任务只执行一次*/
        boolean lock = redisLock.getLock(scheduleJob.getF_SJOB().toString(), null, 5, TimeUnit.SECONDS);
        if (!lock) {
            log.info("任务已在执行中!");
            return;
        }

        EMPL_INF_DTO emplInfDto = redisService.getCacheObject(RedisConstant.ADMIN_USER_ID);
        if (emplInfDto == null){
            log.info("默认超级管理员为空");
            return;
        }

        //数据库保存执行记录
        SCHEDULE_JOB_LOG_INF jobLog = new SCHEDULE_JOB_LOG_INF();
        jobLog.setF_SJOB(scheduleJob.getF_SJOB());
        jobLog.setF_NAME(scheduleJob.getF_NAME());
        jobLog.setF_CRTM(DateUtils.getNowDate());
        jobLog.setF_EDTM(DateUtils.getNowDate());
        jobLog.setF_CRUE(emplInfDto.getF_EMPL());
        jobLog.setF_EDUE(emplInfDto.getF_EMPL());
        try {
            //执行任务
            log.info("任务准备执行，任务ID：" + scheduleJob.getF_SJOB());
            cachedThreadPool.execute(() -> {
                taskExecute(jobLog, scheduleJob);
            });

        } catch (Exception e) {
            log.error("任务执行失败，任务ID：" + scheduleJob.getF_SJOB(), e);
            jobLog.setF_STATUS(1);
            jobLog.setF_TIME(0);
            jobLog.setF_LOG(StringUtils.substring(e.toString(), 0, 2000));
            scheduleJobLogMapper.insert(jobLog);
        }
    }

    /**
     * 缓存
     */
    public static Map<String, String> cacheMap = new HashMap<>();
    Map<String, String> saveMap = new HashMap<>();
    public static final String startVal = "dbCollect_startVal:";
    public static final String startKey = "dbCollect_startKey:";

    public static String saveMapKey = "item_";
    public static String testMapKey = "testItem_";

    /**
     * 任务执行方法
     *
     * @param jobLog
     * @param scheduleJob
     */
    public void taskExecute(SCHEDULE_JOB_LOG_INF jobLog, SCHEDULE_JOB_INF scheduleJob) {
        //任务开始时间
        long startTime = System.currentTimeMillis();

        if (scheduleJob.getF_TASK_IDENTIFY().equals(Constants.autoCollectIdentify)) {

        } else if (scheduleJob.getF_TASK_IDENTIFY().equals(Constants.checkPlanIdentify)) {
            /*临时保存执行记录 手动执行后修改状态*/

            INSPECTION_PLAN_RECORD_INF inspectionPlanRecordInf = new INSPECTION_PLAN_RECORD_INF();
            inspectionPlanRecordInf.setF_PLAN(scheduleJob.getF_BUID());
            inspectionPlanRecordInf.setF_SJOB(scheduleJob.getF_SJOB());
            inspectionPlanRecordInf.setF_CRUE(jobLog.getF_CRUE());
            inspectionPlanRecordInf.setF_EDUE(jobLog.getF_CRUE());
            inspectionPlanRecordInf.setF_CRTM(DateUtils.getNowDate());
            inspectionPlanRecordInf.setF_EDTM(DateUtils.getNowDate());
            inspectionPlanRecordInfMapper.insert(inspectionPlanRecordInf);

            //todo 报警任务待定  1.用定时任务生成  2.redis过期监听
            INSPECTION_PLAN_INF inspectionPlanInf = inspectionPlanInfService.getById(scheduleJob.getF_BUID());
            if (inspectionPlanInf != null) {
                INSPECTION_PLAN_CONFIGURATION_DTO inspectionPlanConfigurationDto =
                        JSONObject.parseObject(inspectionPlanInf.getF_DATA(), INSPECTION_PLAN_CONFIGURATION_DTO.class);
                /*redis过期监听*/
                long time = 0L;
                switch (TimeEnum.getType(inspectionPlanConfigurationDto.getTipTimeType())) {
                    case MINUTE:
                        time = inspectionPlanConfigurationDto.getStartAfterTime() * 60L;
                        break;
                    case HOUR:
                        time = inspectionPlanConfigurationDto.getStartAfterTime() * 60 * 60L;
                        break;
                    case DAY:
                        time = inspectionPlanConfigurationDto.getStartAfterTime() * 24 * 60 * 60L;
                        break;
                }
                if (time != 0L)
                    redisService.set(RedisConstant.CHECK_PLAN_ALARM_TRIGGER + "|" + inspectionPlanRecordInf.getF_PLRE(),
                            inspectionPlanRecordInf.getF_PLRE(), time);

            }

        } else if (scheduleJob.getF_TASK_IDENTIFY().equals(Constants.REAL_TIME_QUALITY_OVERVIEW)) {
            homeService.qualityOverviewQualityOverview();
        }
//        else if (scheduleJob.getF_TASK_IDENTIFY().equals(Constants.checkPlanAlarmIdentify)) {
//            /*查询临时保存执行记录是否存在*/
//            LambdaQueryWrapper<INSPECTION_PLAN_RECORD_INF> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(INSPECTION_PLAN_RECORD_INF::getF_TYPE, DelFlagEnum.USE.getType())
//                    .eq(INSPECTION_PLAN_RECORD_INF::getF_PLAN, scheduleJob.getF_BUID());
//            List<INSPECTION_PLAN_RECORD_INF> inspectionPlanRecordInfList = inspectionPlanRecordInfMapper.selectList(queryWrapper);
//            if (CollectionUtils.isNotEmpty(inspectionPlanRecordInfList)) {
//                INSPECTION_PLAN_RECORD_INF inspectionPlanRecordInf = new INSPECTION_PLAN_RECORD_INF();
//                inspectionPlanRecordInf.setF_TYPE(3);
//                inspectionPlanRecordInfMapper.update(inspectionPlanRecordInf, queryWrapper);
//            }
//            /*暂停并删除报警任务*/
//            /*发送系统通知*/
//
//        }
        //任务执行总时长
        Integer times = Math.toIntExact((System.currentTimeMillis() - startTime) / 1000);
        jobLog.setF_TIME(times);
        //任务状态    0：成功    1：失败
        jobLog.setF_STATUS(0);
//        saveSubgroup(subgroupDataVOList);
        log.info("任务执行完毕，任务ID：" + scheduleJob.getF_SJOB() + "  总共耗时：" + times + "秒");
        scheduleJobLogMapper.insert(jobLog);
    }



    public static void main(String[] args) {
        JexlEngine engine = new Engine();
        JexlContext context = new MapContext();
        JexlScript script = engine.createScript("1+2");
        Object execute = script.execute(context);
    }
}
