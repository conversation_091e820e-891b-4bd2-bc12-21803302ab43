package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.SN_INFService;
import com.yingfei.entity.domain.SN_INF;
import com.yingfei.entity.dto.SN_INF_DTO;
import com.yingfei.entity.vo.SN_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "产品序列号信息API")
@RestController
@RequestMapping("/sn_inf")
public class SN_INFController extends BaseController {
    
    @Resource
    private SN_INFService snInfService;

    /**
     * 获取产品序列号信息列表
     */
    @ApiOperation("获取产品序列号信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody SN_INF_VO snInfVo) {
        List<SN_INF_DTO> list = snInfService.getList(snInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(snInfService.getTotal(snInfVo));
        return dataTable;
    }

    /**
     * 新增产品序列号信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:snInf:add")
    @Log(title = "产品序列号管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增产品序列号信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody SN_INF_VO snInfVo) {
        snInfService.checkParam(snInfVo);
        snInfService.add(snInfVo);
        return R.ok();
    }

    /**
     * 修改产品序列号信息
     */
    @RequiresPermissions("dataManagement:snInf:edit")
    @Log(title = "产品序列号管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改产品序列号信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody SN_INF_VO snInfVo) {
        snInfService.checkParam(snInfVo);
        SN_INF snInf = new SN_INF();
        BeanUtils.copyPropertiesIgnoreNull(snInfVo, snInf);
        snInfService.updateById(snInf);
        return R.ok();
    }

    /**
     * 批量删除产品序列号信息
     */
    @RequiresPermissions("dataManagement:snInf:remove")
    @Log(title = "产品序列号管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除产品序列号信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        snInfService.del(ids);
        return R.ok();
    }
}
