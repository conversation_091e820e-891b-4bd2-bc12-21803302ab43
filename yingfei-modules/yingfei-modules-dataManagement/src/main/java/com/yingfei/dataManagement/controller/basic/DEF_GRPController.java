package com.yingfei.dataManagement.controller.basic;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.DEF_GRPService;
import com.yingfei.entity.domain.DEF_GRP;
import com.yingfei.entity.dto.DEF_GRP_DTO;
import com.yingfei.entity.vo.DEF_GRP_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "缺陷代码组信息API")
@RestController
@RequestMapping("/def_grp")
public class DEF_GRPController extends BaseController {
    
    @Resource
    private DEF_GRPService defGrpService;

    /**
     * 获取缺陷代码组信息列表
     */
    @ApiOperation("获取缺陷代码组信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody DEF_GRP_VO defGrpVo) {
        List<DEF_GRP_DTO> list = defGrpService.getList(defGrpVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(defGrpService.getTotal(defGrpVo));
        return dataTable;
    }

    /**
     * 新增缺陷代码组信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:defGrp:add")
    @Log(title = "缺陷代码组管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增缺陷代码组信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody DEF_GRP_VO defGrpVo) {
        defGrpService.checkParam(defGrpVo);
        defGrpService.add(defGrpVo);
        return R.ok();
    }

    /**
     * 修改缺陷代码组信息
     */
    @RequiresPermissions("dataManagement:defGrp:edit")
    @Log(title = "缺陷代码组管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改缺陷代码组信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody DEF_GRP_VO defGrpVo) {
        defGrpService.checkParam(defGrpVo);
        DEF_GRP defGrp = new DEF_GRP();
        BeanUtils.copyPropertiesIgnoreNull(defGrpVo, defGrp);
        defGrpService.updateById(defGrp);
        return R.ok();
    }

    /**
     * 批量删除缺陷代码组信息
     */
    @RequiresPermissions("dataManagement:defGrp:remove")
    @Log(title = "缺陷代码组管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除缺陷代码组信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        defGrpService.del(ids);
        return R.ok();
    }
}
