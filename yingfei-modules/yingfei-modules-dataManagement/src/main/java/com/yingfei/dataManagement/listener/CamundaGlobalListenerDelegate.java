package com.yingfei.dataManagement.listener;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.constant.WorkFlowConstants;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.dataManagement.service.bpm.BpmProcessInstanceService;
import com.yingfei.dataManagement.service.bpm.BpmTaskAssignRuleService;
import com.yingfei.dataManagement.service.bpm.BpmTaskService;
import com.yingfei.entity.domain.BPM_TASK;
import com.yingfei.entity.dto.CamundaProcessInstanceDTO;
import com.yingfei.entity.dto.CamundaTaskDTO;
import com.yingfei.entity.dto.NotificationCauseDataDTO;
import com.yingfei.entity.vo.BpmTaskApproveReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.camunda.bpm.engine.delegate.TaskListener;
import org.camunda.bpm.model.bpmn.instance.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 本项目为Apache2.0协议 请保留此协议头 ,否则即为违反Apache2.0协议,可以视为侵权
 *
 * <AUTHOR>
 */
@Slf4j
public class CamundaGlobalListenerDelegate implements ExecutionListener, TaskListener {

    private static final String EXCEPTION_CAUSE = "指定异常原因";
    private static final String RESPONSE_ACTION = "指定改善措施";
    private static final String EXCEPTION_CAUSE_APPROVAL = "异常原因审核";
    private static final String RESPONSE_ACTION_APPROVAL = "改善措施审核";

    private static ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);

    /**
     * 任务监听器(Task Listeners)主要是对任务的各种动作进行监听。
     * 包含动作有：创建(create)、删除(delete)、完成(complete)、更新(update)、设置审核人(Assignment)等 动作时机的监听。
     *
     * @param delegateTask
     */
    @Override
    public void notify(DelegateTask delegateTask) {
        BpmTaskService bpmTaskService = SpringUtil.getBean(BpmTaskService.class);
        Map<String, Object> variables = delegateTask.getVariables();
        String processInstanceName = MapUtil.getStr(variables, WorkFlowConstants.PROCESS_INSTANCE_NAME);
        Long startUserId = MapUtil.getLong(variables, WorkFlowConstants.PROCESS_INSTANCE_STARTER_USER_ID);
        if (TaskListener.EVENTNAME_CREATE.equals(delegateTask.getEventName())) {
            CamundaTaskDTO camundaTaskDTO = new CamundaTaskDTO();
            camundaTaskDTO.setTaskId(delegateTask.getId());
            camundaTaskDTO.setProcessInstanceId(delegateTask.getProcessInstanceId());
            camundaTaskDTO.setAssigneeUserId(delegateTask.getAssignee());
            camundaTaskDTO.setCreateTime(delegateTask.getCreateTime());
            camundaTaskDTO.setProcessDefinitionName(processInstanceName);
            camundaTaskDTO.setProcessDefinitionId(delegateTask.getProcessDefinitionId());
            camundaTaskDTO.setProcessStartUserId(startUserId);
            camundaTaskDTO.setIdentification(MapUtil.getStr(variables, WorkFlowConstants.PROCESS_UNIQUE_IDENTIFICATION));
            camundaTaskDTO.setName(delegateTask.getName());
            if (delegateTask.getName().equals(EXCEPTION_CAUSE)) {
                String str = MapUtil.getStr(variables, WorkFlowConstants.ROOT_CAUSE);
                if (StringUtils.isNotEmpty(str)) {
                    NotificationCauseDataDTO notificationCauseDataDTO = JSONObject.parseObject(str, NotificationCauseDataDTO.class);
                    camundaTaskDTO.setF_IS_UPLOAD(notificationCauseDataDTO.getIsUploadProof());
                }
            } else if (delegateTask.getName().equals(RESPONSE_ACTION)) {
                String str = MapUtil.getStr(variables, WorkFlowConstants.RESPONSE_ACTION);
                if (StringUtils.isNotEmpty(str)) {
                    NotificationCauseDataDTO notificationCauseDataDTO = JSONObject.parseObject(str, NotificationCauseDataDTO.class);
                    camundaTaskDTO.setF_IS_UPLOAD(notificationCauseDataDTO.getIsUploadProof());
                }
            }
            camundaTaskDTO.setF_CRUE(startUserId);
            camundaTaskDTO.setF_EDUE(startUserId);
            bpmTaskService.createTaskExt(camundaTaskDTO);
        } else if (TaskListener.EVENTNAME_ASSIGNMENT.equals(delegateTask.getEventName())) {
            CamundaTaskDTO camundaTaskDTO = new CamundaTaskDTO();
            camundaTaskDTO.setTaskId(delegateTask.getId());
            camundaTaskDTO.setProcessInstanceId(delegateTask.getProcessInstanceId());
            camundaTaskDTO.setAssigneeUserId(delegateTask.getAssignee());
            camundaTaskDTO.setCreateTime(delegateTask.getCreateTime());
            camundaTaskDTO.setName(delegateTask.getName());
            camundaTaskDTO.setProcessStartUserId(startUserId);
            camundaTaskDTO.setProcessDefinitionName(processInstanceName);
            camundaTaskDTO.setProcessDefinitionId(delegateTask.getProcessDefinitionId());
            camundaTaskDTO.setIdentification(MapUtil.getStr(variables, WorkFlowConstants.PROCESS_UNIQUE_IDENTIFICATION));
            bpmTaskService.updateTaskExtAssign(camundaTaskDTO);

            /*自动审核延时5秒异步执行*/
            executor.schedule(() -> {
                if (delegateTask.getName().equals(EXCEPTION_CAUSE_APPROVAL)) {
                    String str = MapUtil.getStr(variables, WorkFlowConstants.ROOT_CAUSE);
                    if (StringUtils.isNotEmpty(str)) {
                        NotificationCauseDataDTO notificationCauseDataDTO = JSONObject.parseObject(str, NotificationCauseDataDTO.class);
                        if (notificationCauseDataDTO.getAutomaticApproval() == 0) {
                            BpmTaskApproveReqVO bpmTaskApproveReqVO = new BpmTaskApproveReqVO();
                            bpmTaskApproveReqVO.setId(camundaTaskDTO.getTaskId());
                            bpmTaskApproveReqVO.setAssigneeUserId(variables.get(WorkFlowConstants.userId).toString());
                            bpmTaskService.approveTask(Long.valueOf(variables.get(WorkFlowConstants.userId).toString()), bpmTaskApproveReqVO);
                        }
                    }
                } else if (delegateTask.getName().equals(RESPONSE_ACTION_APPROVAL)) {
                    String str = MapUtil.getStr(variables, WorkFlowConstants.RESPONSE_ACTION);
                    if (StringUtils.isNotEmpty(str)) {
                        NotificationCauseDataDTO notificationCauseDataDTO = JSONObject.parseObject(str, NotificationCauseDataDTO.class);
                        if (notificationCauseDataDTO.getAutomaticApproval() == 0) {
                            BpmTaskApproveReqVO bpmTaskApproveReqVO = new BpmTaskApproveReqVO();
                            bpmTaskApproveReqVO.setId(camundaTaskDTO.getTaskId());
                            bpmTaskApproveReqVO.setAssigneeUserId(variables.get(WorkFlowConstants.userId).toString());
                            bpmTaskService.approveTask(Long.valueOf(variables.get(WorkFlowConstants.userId).toString()), bpmTaskApproveReqVO);
                        }
                    }
                }
            }, 5, TimeUnit.SECONDS);

        } else if (TaskListener.EVENTNAME_COMPLETE.equals(delegateTask.getEventName())) {
            CamundaTaskDTO camundaTaskDTO = new CamundaTaskDTO();
            camundaTaskDTO.setTaskId(delegateTask.getId());
            camundaTaskDTO.setProcessInstanceId(delegateTask.getProcessInstanceId());
            camundaTaskDTO.setAssigneeUserId(delegateTask.getAssignee());
            camundaTaskDTO.setCreateTime(delegateTask.getCreateTime());
            camundaTaskDTO.setName(delegateTask.getName());
            camundaTaskDTO.setProcessStartUserId(startUserId);
            camundaTaskDTO.setProcessDefinitionName(processInstanceName);
            camundaTaskDTO.setProcessDefinitionId(delegateTask.getProcessDefinitionId());
            camundaTaskDTO.setIdentification(MapUtil.getStr(variables, WorkFlowConstants.PROCESS_UNIQUE_IDENTIFICATION));
            bpmTaskService.updateTaskExtComplete(camundaTaskDTO);
        }
    }


    @Override
    public void notify(DelegateExecution execution) throws Exception {
        BpmProcessInstanceService bpmProcessInstanceService = SpringUtil.getBean(BpmProcessInstanceService.class);
        BpmTaskAssignRuleService bpmTaskAssignRuleService = SpringUtil.getBean(BpmTaskAssignRuleService.class);
        BpmTaskService bpmTaskService = SpringUtil.getBean(BpmTaskService.class);
        FlowElement bpmnModelElementInstance = execution.getBpmnModelElementInstance();
        Map<String, Object> variables = execution.getVariables();
        String processInstanceName = MapUtil.getStr(variables, WorkFlowConstants.PROCESS_INSTANCE_NAME);
        Long startUserId = MapUtil.getLong(variables, WorkFlowConstants.PROCESS_INSTANCE_STARTER_USER_ID);
        if (bpmnModelElementInstance instanceof UserTask) {
            UserTask userTask = (UserTask) bpmnModelElementInstance;
            LoopCharacteristics loopCharacteristics = userTask.getLoopCharacteristics();
            String currentActivityName = execution.getCurrentActivityName();
            if (loopCharacteristics == null) {
                List<String> userList = new ArrayList<>();
                if (currentActivityName.equals(EXCEPTION_CAUSE) || currentActivityName.equals(EXCEPTION_CAUSE_APPROVAL)) {
                    String str = MapUtil.getStr(variables, WorkFlowConstants.ROOT_CAUSE);
                    if (StringUtils.isNotEmpty(str)) {
                        NotificationCauseDataDTO notificationCauseDataDTO = JSONObject.parseObject(str, NotificationCauseDataDTO.class);
                        userList = currentActivityName.equals(EXCEPTION_CAUSE) ?
                                notificationCauseDataDTO.getResponsiblePersons() :
                                notificationCauseDataDTO.getApprovalResponsiblePersons();
                    }
                } else if (currentActivityName.equals(RESPONSE_ACTION) || currentActivityName.equals(RESPONSE_ACTION_APPROVAL)) {
                    String str = MapUtil.getStr(variables, WorkFlowConstants.RESPONSE_ACTION);
                    if (StringUtils.isNotEmpty(str)) {
                        NotificationCauseDataDTO notificationCauseDataDTO = JSONObject.parseObject(str, NotificationCauseDataDTO.class);
                        userList = currentActivityName.equals(RESPONSE_ACTION) ?
                                notificationCauseDataDTO.getResponsiblePersons() :
                                notificationCauseDataDTO.getApprovalResponsiblePersons();
                    }
                }
                if (CollectionUtils.isEmpty(userList)) {
                    Set<Long> users = bpmTaskAssignRuleService.calculateTaskCandidateUsers(execution);
                    userList = users.stream().map(String::valueOf).collect(Collectors.toList());
                }
//                execution.setVariable("assignee", userList.get(0));
                execution.setVariable("assignee",
                        String.join(Constants.COMMA, userList));
            }
        } else if (bpmnModelElementInstance instanceof StartEvent) {
            CamundaProcessInstanceDTO camundaProcessInstanceDTO = new CamundaProcessInstanceDTO();
            camundaProcessInstanceDTO.setProcessInstanceId(execution.getProcessInstanceId());
            camundaProcessInstanceDTO.setProcessDefinitionName(processInstanceName);
            camundaProcessInstanceDTO.setProcessStartUserId(startUserId);
            camundaProcessInstanceDTO.setProcessDefinitionId(execution.getProcessDefinitionId());
            camundaProcessInstanceDTO.setF_CRUE(startUserId);
            camundaProcessInstanceDTO.setF_EDUE(startUserId);
            bpmProcessInstanceService.createProcessInstanceExt(camundaProcessInstanceDTO);

            /*创建开始任务*/
//            String startName = MapUtil.getStr(variables, WorkFlowConstants.ALARMS);
//            if (StringUtils.isNotEmpty(startName)) {
            BPM_TASK task = new BPM_TASK();
            task.setF_TASK_NUM("0");
            task.setF_PROCESS_INSTANCE(execution.getProcessInstanceId());
            task.setF_PROCESS_DEFINITION(execution.getProcessDefinitionId());
            task.setF_ASSIGNEE_USER(startUserId.toString());
            task.setF_CRTM(DateUtils.getNowDate());
            task.setF_NAME(execution.getCurrentActivityName());
            task.setF_RESULT(BpmProcessInstanceResultEnum.PROCESS.getResult());
            task.setF_REASON(MapUtil.getStr(variables, WorkFlowConstants.ALARMS_DESCRIBE));
            task.setF_IDENTIFICATION(MapUtil.getStr(variables, WorkFlowConstants.PROCESS_UNIQUE_IDENTIFICATION));
            task.setF_CRUE(startUserId);
            task.setF_EDUE(startUserId);
//                CamundaTaskDTO camundaTaskDTO = new CamundaTaskDTO();
//                camundaTaskDTO.setTaskId(UUID.randomUUID().toString());
//                camundaTaskDTO.setProcessInstanceId(execution.getProcessInstanceId());
//                camundaTaskDTO.setAssigneeUserId(startUserId);
//                camundaTaskDTO.setCreateTime(DateUtils.getNowDate());
//                camundaTaskDTO.setProcessDefinitionName(processInstanceName);
//                camundaTaskDTO.setProcessDefinitionId(execution.getProcessDefinitionId());
//                camundaTaskDTO.setProcessStartUserId(startUserId);
//                camundaTaskDTO.setName(execution.getCurrentActivityName());
//                camundaTaskDTO.setDescribe(MapUtil.getStr(variables, WorkFlowConstants.ALARMS_DESCRIBE));
            bpmTaskService.createTaskExt(task);
//            }
        } else if (bpmnModelElementInstance instanceof EndEvent) {
            CamundaProcessInstanceDTO camundaProcessInstanceDTO = new CamundaProcessInstanceDTO();
            camundaProcessInstanceDTO.setProcessInstanceId(execution.getProcessInstanceId());
            camundaProcessInstanceDTO.setProcessDefinitionName(processInstanceName);
            camundaProcessInstanceDTO.setProcessStartUserId(startUserId);
            camundaProcessInstanceDTO.setProcessDefinitionId(execution.getProcessDefinitionId());
            bpmProcessInstanceService.updateProcessInstanceExtComplete(camundaProcessInstanceDTO);
        }


    }

}
