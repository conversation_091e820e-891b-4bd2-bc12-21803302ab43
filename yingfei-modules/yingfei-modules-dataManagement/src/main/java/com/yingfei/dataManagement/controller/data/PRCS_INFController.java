package com.yingfei.dataManagement.controller.data;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.utils.poi.ExcelUtil;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.datascope.annotation.DataScope;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.PRCS_INFService;
import com.yingfei.dataManagement.service.TAG_LINKService;
import com.yingfei.entity.domain.PART_INF;
import com.yingfei.entity.domain.PRCS_INF;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.PRCS_INF_DTO;
import com.yingfei.entity.enums.TAG_LINKTypeEnum;
import com.yingfei.entity.vo.PART_INF_VO;
import com.yingfei.entity.vo.PRCS_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

@Api(tags = "过程信息API")
@Slf4j
@RestController
@RequestMapping("/prcs_inf")
public class PRCS_INFController extends BaseController {
    
    @Resource
    private PRCS_INFService prcsInfService;
    @Resource
    private TAG_LINKService tagLinkService;

    /**
     * 获取过程信息列表
     */
    @DataScope
    @ApiOperation("获取过程信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody PRCS_INF_VO prcsInfVo) {
        List<PRCS_INF_DTO> list = prcsInfService.getList(prcsInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(prcsInfService.getTotal(prcsInfVo));
        return dataTable;
    }

    /**
     * 获取过程信息列表(树形)
     */
    @ApiOperation("获取过程信息列表(树形)")
    @RequiresPermissions("dataManagement:prcsInf:list")
    @PostMapping("/getTree")
    public R<?> getTree(@RequestBody PRCS_INF_VO prcsInfVo) {
        List<PRCS_INF_DTO> list = prcsInfService.getTree(prcsInfVo);
        return R.ok(list);
    }

    /**
     * 新增过程信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:prcsInf:add")
    @Log(title = "过程管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增过程信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody PRCS_INF_VO prcsInfVo) {
        prcsInfService.checkParam(prcsInfVo);
        prcsInfService.add(prcsInfVo);
        return R.ok();
    }

    /**
     * 修改过程信息
     */
    @RequiresPermissions("dataManagement:prcsInf:edit")
    @Log(title = "过程管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改过程信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody PRCS_INF_VO prcsInfVo) {
        prcsInfService.checkParam(prcsInfVo);
        PRCS_INF prcsInf = new PRCS_INF();
        BeanUtils.copyPropertiesIgnoreNull(prcsInfVo, prcsInf);
        prcsInfService.updateById(prcsInf);

        tagLinkService.saveByType(prcsInfVo.getF_PRCS(), prcsInfVo.getTagDatDtoList(), TAG_LINKTypeEnum.PRCS_DAT.getCode());
        return R.ok();
    }

    /**
     * 批量删除过程信息
     */
    @RequiresPermissions("dataManagement:prcsInf:remove")
    @Log(title = "过程管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除过程信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        prcsInfService.del(ids);
        return R.ok();
    }



    /**
     * 查询已删除的名称数据
     */
    @ApiOperation("查询已删除的名称数据")
    @PostMapping("/recycleBin")
    public R<?> recycleBin(@RequestBody PRCS_INF_VO prcsInfVo) {
        PRCS_INF prcsInf = prcsInfService.recycleBin(prcsInfVo);
        return R.ok(prcsInf);
    }

    /**
     * 恢复已删除的数据
     */
    @RequiresPermissions("dataManagement:prcsInf:edit")
    @Log(title = "过程管理-恢复", businessType = BusinessType.UPDATE)
    @ApiOperation("恢复已删除的数据")
    @PostMapping("/recovery/{id}")
    public R<?> recovery(@PathVariable Long id) {
        prcsInfService.recovery(id);
        return R.ok();
    }

    /**
     * 覆盖已删除的数据
     */
    @RequiresPermissions("dataManagement:prcsInf:edit")
    @Log(title = "过程管理-覆盖", businessType = BusinessType.UPDATE)
    @ApiOperation("覆盖已删除的数据")
    @PostMapping("/cover/{id}")
    public R<?> cover(@PathVariable Long id) {
        prcsInfService.cover(id);
        return R.ok();
    }

    @ApiOperation("下载过程导入模板")
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<PRCS_INF_VO> util = new ExcelUtil<>(PRCS_INF_VO.class);
        util.importTemplateExcel(response, "过程数据");
    }

    @ApiOperation("导入过程信息")
    @Log(title = "过程管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("dataManagement:prcsInf:import")
    @PostMapping("/importData")
    public R<?> importData(@RequestPart("file") MultipartFile file) throws Exception {
        try (InputStream inputStream = file.getInputStream()) {
            ExcelUtil<PRCS_INF_VO> util = new ExcelUtil<>(PRCS_INF_VO.class);
            List<PRCS_INF_VO> prcsInfVoList = util.importExcel(inputStream);
            Map<String, String> map = prcsInfService.importPrcs(prcsInfVoList);
            return R.ok(map);
        } catch (Exception e) {
            log.error("导入产品信息失败");
            e.printStackTrace();
        }
        return R.fail("导入失败");
    }

    /**
     * 获取所属过程列表
     */
    @ApiOperation("获取所属过程列表")
    @PostMapping("/getBelongPrcsList")
    public R<?> getBelongPrcsList(@RequestBody PRCS_INF_VO prcsInfVo) {
        HIERARCHY_INF_DTO hierarchyInfDto = prcsInfService.getBelongPrcsList(prcsInfVo);
        return R.ok(hierarchyInfDto);
    }

    @ApiOperation("批量修改接口")
    @Log(title = "过程管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions("dataManagement:prcsInf:edit")
    @PostMapping("/batchEdit")
    public R<?> batchEdit(@RequestBody PRCS_INF_VO prcsInfVo) {
        prcsInfService.batchEdit(prcsInfVo);
        return R.ok();
    }

    @ApiOperation("获取过程信息")
    @PostMapping("/getInfo/{id}")
    public R<?> getInfo(@PathVariable Long id) {
        PRCS_INF_DTO prcsInfDto = prcsInfService.getInfo(id);
        return R.ok(prcsInfDto);
    }

    @ApiOperation("获取过程删除影响的关联信息")
    @PostMapping("/getOperationAssociation/{ids}")
    public R<?> getOperationAssociation(@PathVariable List<Long> ids) {
        OperationAssociationDTO operationAssociationDTO = prcsInfService.getOperationAssociation(ids);
        return R.ok(operationAssociationDTO);
    }
}
