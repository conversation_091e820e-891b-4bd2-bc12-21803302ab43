package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.poi.ExcelUtil;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.datascope.annotation.DataScope;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.PART_INFService;
import com.yingfei.dataManagement.service.TAG_LINKService;
import com.yingfei.entity.domain.PART_INF;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.dto.PART_INF_DTO;
import com.yingfei.entity.vo.PART_INF_VO;
import com.yingfei.entity.vo.PART_REV_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

@Api(tags = "产品信息API")
@Slf4j
@RestController
@RequestMapping("/part_inf")
public class PART_INFController extends BaseController {

    @Resource
    private PART_INFService partInfService;
    @Resource
    private TAG_LINKService tagLinkService;

    /**
     * 获取产品信息列表
     */
    @ApiOperation("获取产品信息列表")
    @DataScope
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody PART_INF_VO partInfVo) {
        List<PART_INF_DTO> list = partInfService.getList(partInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        partInfVo.setPartRevIds(null);
        partInfVo.setPartIds(null);
        dataTable.setTotal(partInfVo.getUnfold() == 1 ?
                partInfService.getTotal(partInfVo) : partInfService.getUnfoldTotal(partInfVo));
        return dataTable;
    }

    /**
     * 获取产品信息
     */
    @ApiOperation("获取产品信息")
    @PostMapping("/info/{id}/{revId}")
    public R<?> info(@PathVariable("id") Long id, @PathVariable("revId") Long revId) {
        PART_INF_DTO info = partInfService.info(id, revId);
        info.setPartRevId(revId);
        return R.ok(info);
    }

    /**
     * 新增产品信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:partInf:add")
    @Log(title = "产品管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增产品信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody PART_INF_VO partInfVo) {
        partInfService.checkParam(partInfVo);
        partInfService.add(partInfVo);
        return R.ok();
    }

    /**
     * 修改产品信息
     */
    @RequiresPermissions("dataManagement:partInf:edit")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("修改产品信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody PART_INF_VO partInfVo) {
        partInfService.checkParam(partInfVo);
        partInfService.edit(partInfVo);
        return R.ok();
    }

    /**
     * 批量删除产品信息
     */
    @RequiresPermissions("dataManagement:partInf:remove")
    @Log(title = "产品管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除产品信息")
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @DeleteMapping("/remove")
    public R<?> remove(@RequestBody PART_INF_VO partInfVo) {
        partInfService.del(partInfVo);
        return R.ok();
    }

    /**
     * 产品升版
     */
    @CreateUpdateBy
    @ApiOperation("产品升版")
    @RequiresPermissions("dataManagement:partInf:upgrade")
    @Log(title = "产品管理-升版", businessType = BusinessType.INSERT)
    @PostMapping("/upgrade")
    public R<?> upgrade(@RequestBody PART_REV_VO partRevVo) {
        partInfService.upgrade(partRevVo);
        return R.ok();
    }

    /**
     * 查询已删除的名称数据
     */
    @ApiOperation("查询已删除的名称数据")
    @PostMapping("/recycleBin")
    public R<?> recycleBin(@RequestBody PART_INF_VO partInfVo) {
        PART_INF partInf = partInfService.recycleBin(partInfVo);
        return R.ok(partInf);
    }

    /**
     * 恢复已删除的数据
     */
    @RequiresPermissions("dataManagement:partInf:edit")
    @Log(title = "产品管理-恢复", businessType = BusinessType.UPDATE)
    @ApiOperation("恢复已删除的数据")
    @PostMapping("/recovery/{id}")
    public R<?> recovery(@PathVariable Long id) {
        partInfService.recovery(id);
        return R.ok();
    }

    /**
     * 覆盖已删除的数据
     */
    @RequiresPermissions("dataManagement:partInf:edit")
    @Log(title = "产品管理-覆盖", businessType = BusinessType.UPDATE)
    @ApiOperation("覆盖已删除的数据")
    @PostMapping("/cover/{id}")
    public R<?> cover(@PathVariable Long id) {
        partInfService.cover(id);
        return R.ok();
    }

    @ApiOperation("下载产品导入模板")
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<PART_INF_VO> util = new ExcelUtil<>(PART_INF_VO.class);
        util.importTemplateExcel(response, "产品数据");
    }

    @ApiOperation("导入产品信息")
    @Log(title = "产品管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("dataManagement:partInf:import")
    @PostMapping("/importData")
    public R<?> importData(@RequestPart("file") MultipartFile file) throws Exception {
        try (InputStream inputStream = file.getInputStream()) {
            ExcelUtil<PART_INF_VO> util = new ExcelUtil<>(PART_INF_VO.class);
            List<PART_INF_VO> partInfVoList = util.importExcel(inputStream);
            Map<String, String> map = partInfService.importPart(partInfVoList);
            return R.ok(map);
        } catch (Exception e) {
            log.error("导入产品信息失败");
            e.printStackTrace();
        }
        return R.fail("导入失败");
    }

    @ApiOperation("批量修改接口")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions("dataManagement:partInf:edit")
    @PostMapping("/batchEdit")
    public R<?> batchEdit(@RequestBody PART_INF_VO partInfVo) {
        partInfService.batchEdit(partInfVo);
        return R.ok();
    }

    @ApiOperation("获取层级对应产品")
    @PostMapping("/getBelongPartList")
    public R<?> getBelongPartList(@RequestBody PART_INF_VO partInfVo) {
        HIERARCHY_INF_DTO hierarchyInfDto = partInfService.getBelongPartList(partInfVo);
        return R.ok(hierarchyInfDto);
    }

    @ApiOperation("获取产品删除影响的关联信息")
    @PostMapping("/getOperationAssociation")
    public R<?> getOperationAssociation(@RequestBody PART_INF_VO partInfVo) {
        OperationAssociationDTO operationAssociationDTO = partInfService.getOperationAssociation(partInfVo);
        return R.ok(operationAssociationDTO);
    }

    /**
     * 新增返回对应产品实体
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:partInf:add")
    @Log(title = "产品管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增产品信息")
    @PostMapping("/addPart")
    public R<?> addPart(@RequestBody PART_INF_VO partInfVo) {
        partInfService.checkParam(partInfVo);
        PART_INF_VO partInf = partInfService.addPartInf(partInfVo);
        return R.ok(partInf);
    }
}
