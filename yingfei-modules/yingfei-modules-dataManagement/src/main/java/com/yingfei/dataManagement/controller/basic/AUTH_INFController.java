package com.yingfei.dataManagement.controller.basic;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.utils.*;
import com.yingfei.entity.domain.AUTH_INF;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Calendar;
import java.util.Date;

@Slf4j
@Api(tags = "机器授权管理API")
@RestController
@RequestMapping("/auth")
public class AUTH_INFController {

    /**
     * 生成机器授权
     */
//    @ApiOperation("生成机器授权，用于其他服务器设备")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "hardwareId", value = "机器唯一编码", dataType = "string"),
//            @ApiImplicitParam(name = "concurrentCount", value = "同时在线人数上限，为空默认100 或 采集服务启动数量", dataType = "integer"),
//            @ApiImplicitParam(name = "licenseType", value = "是否长期(0:短期授权,1:长期授权)", dataType = "integer"),
//            @ApiImplicitParam(name = "startDate", value = "授权开始时间", dataType = "String"),
//            @ApiImplicitParam(name = "endDate", value = "授权结束时间", dataType = "String"),
//            @ApiImplicitParam(name = "productType", value = "产品授权类型(1:数据服务 2:采集服务)", dataType = "integer")
//    })
//    @PostMapping("/generateAuth")
//    public R<?> generateAuth(@RequestParam String hardwareId,
//                             @RequestParam(defaultValue = "100", required = false) Integer concurrentCount,
//                             @RequestParam(defaultValue = "0") Integer licenseType,
//                             @RequestParam String startDate,
//                             @RequestParam String endDate,
//                             @RequestParam String productType) {
//        if (StringUtils.isEmpty(hardwareId)) {
//            return R.fail("机器唯一编码不能为空");
//        }
//
//        AUTH_INF sysAuth = new AUTH_INF();
//        sysAuth.setF_AUTH(String.valueOf(JudgeUtils.defaultIdentifierGenerator.nextId(null)));
//        Calendar instance = Calendar.getInstance();
//        instance.setTime(DateUtils.getNowDate());
//        if (licenseType == 0) {
//            if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
//                instance.add(Calendar.MONTH, 1);
//            }
//        } else {
//            instance.add(Calendar.YEAR, 99);
//        }
//        concurrentCount = concurrentCount == null ? 10 : concurrentCount;
//        String code = hardwareId + "@" + concurrentCount + "@" + licenseType + "@" +
//                DateUtils.dateTimeTwo(DateUtils.parseDate(startDate)) + "@" +
//                DateUtils.dateTimeTwo(DateUtils.parseDate(endDate)) + "@" +
//                productType;
//        String encryptStr = AESUtil.encryptStr(code, AESUtil.defaultAesKey);
//
//        String sb = "DELETE FROM [dbo].[AUTH_INF];" +
//                "INSERT INTO [dbo].[AUTH_INF]  VALUES ('" +
//                sysAuth.getF_AUTH() +
//                "', '" +
//                encryptStr + "');";
//
//        return R.ok(sb);
//    }

}
