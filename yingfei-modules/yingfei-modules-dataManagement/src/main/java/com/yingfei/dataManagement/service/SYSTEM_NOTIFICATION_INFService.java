package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.SYSTEM_NOTIFICATION_INF;
import com.yingfei.entity.dto.SYSTEM_NOTIFICATION_INF_DTO;
import com.yingfei.entity.vo.SYSTEM_NOTIFICATION_INF_VO;

import java.util.List;

public interface SYSTEM_NOTIFICATION_INFService extends IService<SYSTEM_NOTIFICATION_INF>, BaseService<SYSTEM_NOTIFICATION_INF_VO, SYSTEM_NOTIFICATION_INF_DTO> {
    void read(List<Long> ids);

    SYSTEM_NOTIFICATION_INF_DTO info(Long id);
}
