package com.yingfei.dataManagement.controller.alarms;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.RULE_INFService;
import com.yingfei.entity.domain.RULE_INF;
import com.yingfei.entity.dto.RULE_INF_DTO;
import com.yingfei.entity.enums.StatisticalViolationTypeEnum;
import com.yingfei.entity.vo.RULE_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api(tags = "报警规则信息API")
@RestController
@RequestMapping("/rule_inf")
public class RULE_INFController extends BaseController {

    @Resource
    private RULE_INFService ruleInfService;

    /**
     * 获取报警规则信息列表
     */
    @ApiOperation("获取报警规则信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody RULE_INF_VO ruleInfVo) {
        List<RULE_INF_DTO> list = ruleInfService.getList(ruleInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(ruleInfService.getTotal(ruleInfVo));
        return dataTable;
    }

    /**
     * 新增报警规则信息
     */
    @CreateUpdateBy
    @RequiresPermissions("dataManagement:ruleInf:add")
    @Log(title = "报警规则管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增报警规则信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody RULE_INF_VO ruleInfVo) {
        ruleInfService.checkParam(ruleInfVo);
        ruleInfService.add(ruleInfVo);
        return R.ok();
    }

    /**
     * 修改报警规则信息
     */
    @RequiresPermissions("dataManagement:ruleInf:edit")
    @Log(title = "报警规则管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改报警规则信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody RULE_INF_VO ruleInfVo) {
        ruleInfService.checkParam(ruleInfVo);
        RULE_INF ruleInf = new RULE_INF();
        BeanUtils.copyPropertiesIgnoreNull(ruleInfVo, ruleInf);
        ruleInfService.updateById(ruleInf);
        return R.ok();
    }

    /**
     * 批量删除报警规则信息
     */
    @RequiresPermissions("dataManagement:ruleInf:remove")
    @Log(title = "报警规则管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除报警规则信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        ruleInfService.del(ids);
        return R.ok();
    }

    /**
     * 获取报警类型
     */
    @ApiOperation("获取报警类型")
    @GetMapping("/getAlarmType")
    public R<?> getAlarmType() {
        Map<Integer, String> map = StatisticalViolationTypeEnum.getMap();
        return R.ok(map);
    }

    /**
     * 获取用于进行报警判断的子组数
     */
    @ApiOperation("获取用于进行报警判断的子组数")
    @GetMapping("/getMaxCnt")
    public R<?> getMaxCnt() {
        return R.ok(ruleInfService.getMaxCnt());
    }
}
