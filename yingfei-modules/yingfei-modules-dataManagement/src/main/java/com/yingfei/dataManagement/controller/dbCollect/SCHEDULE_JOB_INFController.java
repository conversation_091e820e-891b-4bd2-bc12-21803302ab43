package com.yingfei.dataManagement.controller.dbCollect;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.dataManagement.service.schedule.SCHEDULE_JOB_INFService;
import com.yingfei.dataManagement.service.schedule.ScheduleUtils;
import com.yingfei.entity.domain.SCHEDULE_JOB_INF;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.quartz.CronTrigger;
import org.quartz.Scheduler;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/scheduleJob")
@Api(tags = "任务调度API")
public class SCHEDULE_JOB_INFController {

    @Resource
    private SCHEDULE_JOB_INFService scheduleJobService;
    @Resource
    private Scheduler scheduler;
    /**
     * 项目启动时，初始化定时器
     */
    @PostConstruct
    public void init() {
        List<SCHEDULE_JOB_INF> scheduleJobList = scheduleJobService.list(Wrappers.<SCHEDULE_JOB_INF>lambdaQuery()
                .ne(SCHEDULE_JOB_INF::getF_STATUS, Constants.DEL).ne(SCHEDULE_JOB_INF::getF_TASK_IDENTIFY,Constants.autoCollectIdentify));
        for (SCHEDULE_JOB_INF scheduleJob : scheduleJobList) {
            CronTrigger cronTrigger = ScheduleUtils.getCronTrigger(scheduler, scheduleJob.getF_SJOB());
            //如果不存在，则创建
            if (cronTrigger == null) {
                ScheduleUtils.createScheduleJob(scheduler, scheduleJob);
            } else {
                ScheduleUtils.updateScheduleJob(scheduler, scheduleJob);
            }
        }
    }

    /**
     * 任务列表
     */
    @GetMapping("/list")
    @ApiOperation("任务列表")
    public R<?> list() {
        LambdaQueryWrapper<SCHEDULE_JOB_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(SCHEDULE_JOB_INF::getF_CRTM);
        return R.ok(scheduleJobService.list(queryWrapper));
    }

    /**
     * 添加任务
     */
    @CreateUpdateBy
    @PostMapping("/add")
    @ApiOperation("添加任务")
    public R<?> add(@RequestBody SCHEDULE_JOB_INF scheduleJob) {
        scheduleJobService.add(scheduleJob);
        return R.ok();
    }

    /**
     * 更新任务
     */
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @PostMapping("/update")
    @ApiOperation("更新任务")
    public R<?> update(@RequestBody SCHEDULE_JOB_INF scheduleJob) {
        scheduleJobService.update(scheduleJob);
        return R.ok();
    }

    /**
     * 删除任务
     */
    @PostMapping("/delete")
    @ApiOperation("删除任务")
    public R<?> delete(@RequestBody SCHEDULE_JOB_INF scheduleJob) {
        scheduleJobService.delete(scheduleJob);
        return R.ok();
    }

    /**
     * 暂停任务
     */
    @PostMapping("/pause")
    @ApiOperation("暂停任务")
    public R<?> pause(@RequestBody List<Long> jobIds) {
        scheduleJobService.pause(jobIds);
        return R.ok();
    }

    /**
     * 恢复任务
     */
    @PostMapping("/resume")
    @ApiOperation("恢复任务")
    public R<?> resume(@RequestBody List<Long> jobIds) {
        scheduleJobService.resume(jobIds);
        return R.ok();
    }
}
