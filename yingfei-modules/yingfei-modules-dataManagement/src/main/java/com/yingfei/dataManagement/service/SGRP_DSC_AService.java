package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.domain.SGRP_DSC_A;
import com.yingfei.entity.dto.SGRP_DSC_DTO;

import java.util.Date;
import java.util.List;

/**
* 
* @description 针对表【SGRP_DSC_A(子组与描述符关联缓存表)】的数据库操作Service
* @createDate 2024-06-20 13:45:41
*/
public interface SGRP_DSC_AService extends IService<SGRP_DSC_A> {

    List<SGRP_DSC_DTO> findBySgrp(Long fSgrp);

    void deleteBySgtm(Date startTime, Date endTime);
}
