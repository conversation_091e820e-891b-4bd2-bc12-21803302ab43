package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.dataManagement.service.SGRP_CMTService;
import com.yingfei.entity.dto.SGRP_CMT_DTO;
import com.yingfei.entity.vo.SgrpCmtAddVO;
import com.yingfei.entity.vo.SgrpCmtUpdateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "子组信息备注API")
@RestController
@RequestMapping("/sgrp_cmt")
public class SGRP_CMTController extends BaseController {

    @Resource
    private SGRP_CMTService sgrpCmtService;

    /**
     * 新增子组备注信息
     */
    @Log(title = "子组管理", businessType = BusinessType.INSERT)
//    @NotResubmit
    @CreateUpdateBy
    @ApiOperation("新增子组备注信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody SgrpCmtAddVO vo) {
        if(vo.getFSgrpList().isEmpty() || StringUtils.isBlank(vo.getF_NOTE())){
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        sgrpCmtService.add(vo);
        return R.ok();
    }

    /**
     * 修改子组信息
     */
    @Log(title = "子组备注管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改子组备注信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody SgrpCmtUpdateVO vo) {
        if(vo.getFCmtList().isEmpty() || StringUtils.isBlank(vo.getF_NOTE())){
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        sgrpCmtService.edit(vo);
        return R.ok();
    }

    @Log(title = "子组备注管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除子组备注信息")
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @DeleteMapping("/del")
    public R<?> del(@RequestBody SgrpCmtUpdateVO vo) {
        if(vo.getFCmtList().isEmpty()){
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        sgrpCmtService.del(vo.getFCmtList());
        return R.ok();
    }

    /**
     * 获取子组信息列表
     */
    @ApiOperation("获取子组备注信息列表")
    @GetMapping("/getBySgrp/{fsgrp}")
    public R<?> getBySgrp(@PathVariable("fsgrp") Long fsgrp) {
        if(ObjectUtils.isEmpty(fsgrp)){
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        List<SGRP_CMT_DTO> list = sgrpCmtService.getBySgrp(fsgrp);
        return R.ok(list);
    }

}
