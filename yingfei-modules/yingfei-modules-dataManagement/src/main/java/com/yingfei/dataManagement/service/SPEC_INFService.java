package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.SPEC_INF;
import com.yingfei.entity.dto.DataSummaryDTO;
import com.yingfei.entity.dto.SPEC_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.enums.ParetoAnalyseTypeEnum;
import com.yingfei.entity.vo.SPEC_INF_VO;
import com.yingfei.entity.vo.excel.SPEC_INF_EXCEL_VO;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【SPEC_INF(公差限表)】的数据库操作Service
* @createDate 2024-05-08 16:28:48
*/
public interface SPEC_INFService extends IService<SPEC_INF>, BaseService<SPEC_INF_VO, SPEC_INF_DTO> {

    Object getRule();

    void setRule(String defaultAlarm, String defaultRational);

    void addByProductVersion(List<SPEC_INF_VO> specInfVoList);

    SPEC_INF_DTO getInfo(SPEC_INF_VO specInfVo);

    void edit(SPEC_INF_VO specInfVo);

    SPEC_INF_DTO getSpecLim(DataSummaryDTO dataSummaryDTO, SubgroupDataDTO subgroupDataDTO);

    SPEC_INF_DTO getSpecLim(SubgroupDataDTO subgroupDataDTO);

    void delCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum);

    Map<String, String> importSpec(List<SPEC_INF_EXCEL_VO> specInfExcelVoList, Integer status);

    List<SPEC_INF> getCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum);

    Map<String,SPEC_INF_DTO> getInfoMapList(SPEC_INF_VO specInfVo);
}
