package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.JOB_DAT;
import com.yingfei.entity.dto.JOB_DAT_DTO;
import com.yingfei.entity.dto.OperationAssociationDTO;
import com.yingfei.entity.vo.JOB_DAT_VO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【JOB_DAT(储存工单信息表)】的数据库操作Service
 * @createDate 2024-05-08 16:27:30
 */
public interface JOB_DATService extends IService<JOB_DAT>, BaseService<JOB_DAT_VO, JOB_DAT_DTO> {

    List<JOB_DAT> getSearchCondition(Integer isInclude, List<Long> jobList);

    JOB_DAT_DTO info(Long id);

    JOB_DAT addJobDat(JOB_DAT_VO jobDatVo);

    OperationAssociationDTO getOperationAssociation(List<Long> ids);
}
