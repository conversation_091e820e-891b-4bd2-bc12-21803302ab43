package com.yingfei.dataManagement.listener;//package cn.iocoder.yudao.module.bpm.framework.flowable.core.listener;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.iocoder.yudao.module.bpm.dal.dataobject.task.BpmTaskExtDO;
//import cn.iocoder.yudao.module.bpm.service.task.BpmActivityService;
//import cn.iocoder.yudao.module.bpm.service.task.BpmTaskService;
//import com.google.common.collect.ImmutableSet;
//import lombok.extern.slf4j.Slf4j;
//import org.camunda.bpm.engine.history.HistoricActivityInstance;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Set;
//
///**
// * 监听 {@link org.flowable.task.api.Task} 的开始与完成，创建与更新对应的 {@link BpmTaskExtDO} 记录
// *
// * <AUTHOR>
// */
//@Component
//@Slf4j
//public class BpmTaskEventListener extends AbstractFlowableEngineEventListener {
//
//    @Resource
//    @Lazy // 解决循环依赖
//    private BpmTaskService taskService;
//
//    @Resource
//    @Lazy // 解决循环依赖
//    private BpmActivityService activityService;
//
//    public static final Set<FlowableEngineEventType> TASK_EVENTS = ImmutableSet.<FlowableEngineEventType>builder()
//            .add(FlowableEngineEventType.TASK_CREATED)
//            .add(FlowableEngineEventType.TASK_ASSIGNED)
//            .add(FlowableEngineEventType.TASK_COMPLETED)
//            .add(FlowableEngineEventType.ACTIVITY_CANCELLED)
//            .build();
//
//    public BpmTaskEventListener(){
//        super(TASK_EVENTS);
//    }
//
//    @Override
//    protected void taskCreated(FlowableEngineEntityEvent event) {
//        taskService.createTaskExt((Task) event.getEntity());
//    }
//
//    @Override
//    protected void taskCompleted(FlowableEngineEntityEvent event) {
//        taskService.updateTaskExtComplete((Task)event.getEntity());
//    }
//
//    @Override
//    protected void taskAssigned(FlowableEngineEntityEvent event) {
//        taskService.updateTaskExtAssign((Task)event.getEntity());
//    }
//
//    @Override
//    protected void activityCancelled(FlowableActivityCancelledEvent event) {
//        List<HistoricActivityInstance> activityList = activityService.getHistoricActivityListByExecutionId(event.getExecutionId());
//        if (CollUtil.isEmpty(activityList)) {
//            log.error("[activityCancelled][使用 executionId({}) 查找不到对应的活动实例]", event.getExecutionId());
//            return;
//        }
//        // 遍历处理
//        activityList.forEach(activity -> {
//            if (StrUtil.isEmpty(activity.getTaskId())) {
//                return;
//            }
//            taskService.updateTaskExtCancel(activity.getTaskId());
//        });
//    }
//
//}
