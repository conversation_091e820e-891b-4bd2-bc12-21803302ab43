package com.yingfei.dataManagement.controller.chart;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.ExportImgWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@Slf4j
@RestController
@Api(tags = "图表:通用接口APi")
@RequestMapping("/chartCommon")
public class ChartCommonController {

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.setAutoGrowCollectionLimit(10000);
    }

    @Resource
    private ChartCommonService chartCommonService;

    /**
     * 根据页面获取对应条件的子组
     *
     * @param type           1:单项分析 2:聚合分析
     */
    @ApiOperation("根据页面获取对应条件的子组")
    @PostMapping("/getPageSubgroup/{type}")
    public R<?> getPageSubgroup(@RequestBody SubgroupDataSelectionDTO subgroupDataSelectionDTO, @PathVariable Integer type) {
        Map<String, Object> map = chartCommonService.getPageSubgroup(subgroupDataSelectionDTO, type);
        return R.ok(map);
    }

    /**
     * 导出图片
     */
    @PostMapping("/exportExcel/{menuId}")
    @ApiOperation("测试导出图表")
    public void exportExcel(@PathVariable String menuId, @RequestBody ExportImgWrapper exportImgWrapper, HttpServletResponse response) throws IOException {
        chartCommonService.exportExcel(menuId, exportImgWrapper, response);
    }

}
