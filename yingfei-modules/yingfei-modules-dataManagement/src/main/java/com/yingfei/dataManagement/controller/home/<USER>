package com.yingfei.dataManagement.controller.home;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.home.EMPL_AGENDA_INFService;
import com.yingfei.entity.domain.EMPL_AGENDA_INF;
import com.yingfei.entity.dto.EMPL_AGENDA_INF_DTO;
import com.yingfei.entity.vo.EMPL_AGENDA_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "首页自定义提醒表API")
@RestController
@RequestMapping("/empl_agenda")
public class EMPL_AGENDA_INFController extends BaseController {
    
    @Resource
    private EMPL_AGENDA_INFService emplAgendaInfService;

    /**
     * 获取首页自定义提醒表列表
     */
    @ApiOperation("获取首页自定义提醒表列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody EMPL_AGENDA_INF_VO emplAgendaInfVo) {
        List<EMPL_AGENDA_INF_DTO> list = emplAgendaInfService.getList(emplAgendaInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(emplAgendaInfService.getTotal(emplAgendaInfVo));
        return dataTable;
    }

    /**
     * 新增首页自定义提醒表
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:emplAgendaInf:add")
    @Log(title = "首页自定义提醒表", businessType = BusinessType.INSERT)
    @ApiOperation("新增首页自定义提醒表")
    @PostMapping("/add")
    public R<?> add(@RequestBody EMPL_AGENDA_INF_VO emplAgendaInfVo) {
        emplAgendaInfService.checkParam(emplAgendaInfVo);
        emplAgendaInfService.add(emplAgendaInfVo);
        return R.ok();
    }

    /**
     * 修改首页自定义提醒表
     */
    @RequiresPermissions("dataManagement:emplAgendaInf:edit")
    @Log(title = "首页自定义提醒表", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改首页自定义提醒表")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody EMPL_AGENDA_INF_VO emplAgendaInfVo) {
        emplAgendaInfService.checkParam(emplAgendaInfVo);
        EMPL_AGENDA_INF emplAgendaInf = new EMPL_AGENDA_INF();
        BeanUtils.copyPropertiesIgnoreNull(emplAgendaInfVo, emplAgendaInf);
        emplAgendaInfService.updateById(emplAgendaInf);
        return R.ok();
    }

    /**
     * 批量删除首页自定义提醒表
     */
    @RequiresPermissions("dataManagement:emplAgendaInf:remove")
    @Log(title = "首页自定义提醒表", businessType = BusinessType.DELETE)
    @ApiOperation("删除首页自定义提醒表")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        emplAgendaInfService.del(ids);
        return R.ok();
    }
}
