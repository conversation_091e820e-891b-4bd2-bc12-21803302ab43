package com.yingfei.dataManagement.controller.home;

import com.alibaba.fastjson2.JSONArray;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.EMPL_FAVORITE_INFService;
import com.yingfei.entity.domain.EMPL_FAVORITE_INF;
import com.yingfei.entity.dto.EMPL_FAVORITE_INF_DTO;
import com.yingfei.entity.vo.EMPL_FAVORITE_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "用户菜单收藏信息API")
@RestController
@RequestMapping("/empl_favorite_inf")
public class EMPL_FAVORITE_INFController extends BaseController {

    @Resource
    private EMPL_FAVORITE_INFService emplFavoriteInfService;

    /**
     * 获取用户菜单收藏信息列表
     */
    @ApiOperation("获取用户菜单收藏信息列表")
    @PostMapping("/list")
    public R<?> list(@RequestBody EMPL_FAVORITE_INF_VO emplFavoriteInfVo) {
        List<EMPL_FAVORITE_INF_DTO> list = emplFavoriteInfService.getList(emplFavoriteInfVo);
        return R.ok(list);
    }

    /**
     * 新增用户菜单收藏信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:emplFavoriteInf:add")
    @Log(title = "用户菜单收藏管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增用户菜单收藏信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody EMPL_FAVORITE_INF_VO emplFavoriteInfVo) {
        emplFavoriteInfService.checkParam(emplFavoriteInfVo);
        emplFavoriteInfService.add(emplFavoriteInfVo);
        return R.ok();
    }

    /**
     * 批量删除用户菜单收藏信息(传入menuId列表)
     */
    @RequiresPermissions("dataManagement:emplFavoriteInf:remove")
    @Log(title = "用户菜单收藏管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除用户菜单收藏信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        emplFavoriteInfService.del(ids);
        return R.ok();
    }
}
