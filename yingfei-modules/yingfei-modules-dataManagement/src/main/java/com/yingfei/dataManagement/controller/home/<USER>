package com.yingfei.dataManagement.controller.home;

import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.service.SYSTEM_NOTIFICATION_INFService;
import com.yingfei.entity.dto.SYSTEM_NOTIFICATION_INF_DTO;
import com.yingfei.entity.vo.SYSTEM_NOTIFICATION_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统通知
 */
@Api(tags = "系统通知API")
@RestController
@RequestMapping("/system_notification_inf")
public class SYSTEM_NOTIFICATION_INFController extends BaseController {

    @Resource
    private SYSTEM_NOTIFICATION_INFService systemNotificationInfService;

    /**
     * 获取工单信息列表
     */
    @ApiOperation("获取系统通知列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody SYSTEM_NOTIFICATION_INF_VO systemNotificationInfVo) {
        systemNotificationInfVo.setF_EMPL(SecurityUtils.getUserId());
        List<SYSTEM_NOTIFICATION_INF_DTO> list = systemNotificationInfService.getList(systemNotificationInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(systemNotificationInfService.getTotal(systemNotificationInfVo));
        return dataTable;
    }

    /**
     * 预览
     */
    @ApiOperation("预览")
    @PostMapping("/info/{id}")
    public R<?> info(@PathVariable Long id) {
        SYSTEM_NOTIFICATION_INF_DTO systemNotificationInfDto = systemNotificationInfService.info(id);
        return R.ok(systemNotificationInfDto);
    }



    /**
     * 批量删除系统通知
     */
    @ApiOperation("批量删除系统通知")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        systemNotificationInfService.del(ids);
        return R.ok();
    }

    /**
     * 获取最新未读系统通知
     */
    @ApiOperation("获取最新未读系统通知")
    @GetMapping("/latest_unread")
    public R<?> latestUnread() {
        SYSTEM_NOTIFICATION_INF_VO systemNotificationInfVo = new SYSTEM_NOTIFICATION_INF_VO();
        systemNotificationInfVo.setF_EMPL(SecurityUtils.getUserId());
        systemNotificationInfVo.setF_STATUS(0);
        systemNotificationInfVo.setNext(Constants.NEXT);
        long total = systemNotificationInfService.getTotal(systemNotificationInfVo);
        return R.ok(total);
    }

    /**
     * 批量已读
     */
    @ApiOperation("批量已读")
    @PutMapping("/read/{ids}")
    public R<?> read(@PathVariable List<Long> ids) {
        systemNotificationInfService.read(ids);
        return R.ok();
    }
}
