package com.yingfei.dataManagement.controller.home;

import com.yingfei.common.core.constant.CacheConstants;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.LanguageEnum;
import com.yingfei.common.core.enums.LoginTypeEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.AESUtil;
import com.yingfei.common.core.utils.CodeUtil;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.service.home.HomeService;
import com.yingfei.entity.dto.QualityOverviewDTO;
import com.yingfei.entity.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * 首页
 */
@Api(tags = "首页API")
@RestController
@RequestMapping("/home")
public class HomeController {

    @Resource
    private RedisService redisService;
    @Resource
    private HomeService homeService;

    @GetMapping("/switchLanguage")
    @ApiOperation("切换语言")
    public R<?> switchLanguage(Integer type) {
        if (type == null)
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        LanguageEnum languageEnum = LanguageEnum.valueOfIndex(type);
        if (languageEnum == null)
            throw new BusinessException(DataManagementExceptionEnum.SWITCH_LANGUAGE_NOT_EXISTS);
        redisService.set(RedisConstant.LANGUAGE_TYPE, type);
        return R.ok();
    }

    /**
     * 获取所有登录人员信息
     */
    @ApiOperation("获取所有登录人员信息")
    @PostMapping("/getLoginUserInfo")
    public R<?> getLoginUserInfo() {
        String key = CacheConstants.LOGIN_TOKEN_KEY + "*";
        Collection<String> keys = redisService.keys(key);
        Object o = redisService.get(RedisConstant.DATA_MANAGEMENT_SERVICE);
        int num = 10;
        if (o != null) {
            String decrypt = AESUtil.decryptStr(o.toString(), AESUtil.defaultAesKey);
            num = Integer.parseInt(decrypt == null ? "0" : decrypt.split("@")[1]);
        }
        if (CollectionUtils.isEmpty(keys)) return R.ok(new ArrayList<>());
        List<LoginUser> list = new ArrayList<>();
        keys.forEach(s -> {
            LoginUser user = redisService.getCacheObject(s);
            if (user != null) {
                list.add(user);
            }
        });
        HashMap<String, Object> map = new HashMap<>();
        map.put("loginUserInfo", list);
        map.put("concurrentCount", num);
        /*采集服务启动数量 显示:ip,*/

        return R.ok(map);
    }

    /**
     * 强制账户退出
     */
    @ApiOperation("强制账户退出")
    @PostMapping("/logout")
    public R<?> logout(@RequestBody LoginUser loginUser) {
        String userTokenKeyPrefix = redisService.getUserTokenKeyPrefix(loginUser.getUserid(), LoginTypeEnum.PC);
        String key = userTokenKeyPrefix + loginUser.getToken();
        redisService.deleteObject(key);
        return R.ok();
    }

    /**
     * 实时质量概览
     */
    @ApiOperation("实时质量概览")
    @PostMapping("/qualityOverview")
    public R<?> qualityOverview(@RequestBody Integer type) {
        List<QualityOverviewDTO> qualityOverviewDTOList = homeService.qualityOverview(type);
        return R.ok(qualityOverviewDTOList);
    }

    /**
     * 设置实时质量概览统计时间
     */
    @ApiOperation("设置实时质量概览统计时间")
    @GetMapping("/setQualityOverviewTime")
    public R<?> setQualityOverviewTime(Integer timeType, Integer interval) {
        homeService.setQualityOverviewTime(timeType, interval);
        return R.ok();
    }
}
