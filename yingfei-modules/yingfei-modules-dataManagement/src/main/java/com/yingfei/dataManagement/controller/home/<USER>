package com.yingfei.dataManagement.controller.home;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.bpm.BpmTaskService;
import com.yingfei.dataManagement.service.manufacturing.INSPECTION_PLAN_INFService;
import com.yingfei.entity.domain.SGRP_INF_UNFINISHED;
import com.yingfei.entity.dto.INSPECTION_PLAN_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "工作看板信息API")
@RestController
@RequestMapping("/workBoard")
public class WorkBoardController {

    @Resource
    private INSPECTION_PLAN_INFService inspectionPlanInfService;
    @Resource
    private SGRP_INF_AService sgrpInfAService;
    @Resource
    private BpmTaskService bpmTaskService;

    /**
     * 获取检查计划列表
     */
    @ApiOperation("获取检查计划列表")
    @PostMapping("/getWorkBoardPlan")
    public R<?> getWorkBoardPlan(@RequestBody INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        if (StringUtils.isEmpty(inspectionPlanInfVo.getF_EMPL()) || StringUtils.isEmpty(inspectionPlanInfVo.getF_ROLE())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        List<INSPECTION_PLAN_INF_DTO> inspectionPlanInfDtoList = inspectionPlanInfService.getWorkBoardPlan(inspectionPlanInfVo);
        return R.ok(inspectionPlanInfDtoList);
    }

    /**
     * 获取待完成检查计划列表
     */
    @ApiOperation("获取待完成检查计划列表")
    @PostMapping("/getUnfinishedList")
    public R<?> getUnfinishedList(@RequestBody INSPECTION_PLAN_INF_VO inspectionPlanInfVo) {
        List<SGRP_INF_UNFINISHED> sgrpInfUnfinishedList = inspectionPlanInfService.getUnfinishedList(inspectionPlanInfVo);
        return R.ok(sgrpInfUnfinishedList);
    }

    /**
     * 获取数据采集历史
     */
    @ApiOperation("获取数据采集历史")
    @PostMapping("/getDataHistorical")
    public R<?> getDataHistorical(@RequestBody SubgroupDataVO subgroupDataVO) {
        if (subgroupDataVO.getF_CRUE() == null) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        List<SubgroupDataDTO> subgroupDataDTOList = sgrpInfAService.getDataHistorical(subgroupDataVO);
        return R.ok(subgroupDataDTOList);
    }

    /**
     * 获取数据采集历史详情
     */
    @ApiOperation("获取数据采集历史详情")
    @PostMapping("/getDataHistoricalInfo")
    public R<?> getDataHistoricalInfo(@RequestBody SubgroupDataDTO subgroupDataDTO) {
        if (subgroupDataDTO.getF_CRUE() == null) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        subgroupDataDTO = sgrpInfAService.getDataHistoricalInfo(subgroupDataDTO);
        return R.ok(subgroupDataDTO);
    }

    /**
     * 获取待办任务
     */
    @ApiOperation("获取待办任务")
    @PostMapping("/getPendingTask")
    public TableDataInfo<?> getPendingTask(@RequestBody BpmTaskTodoPageReqVO bpmTaskTodoPageReqVO) {
        TableDataInfo<BpmTaskTodoPageItemRespVO> todoTaskPage =
                bpmTaskService.getTodoTaskPage(SecurityUtils.getUserId(), bpmTaskTodoPageReqVO);
        return todoTaskPage;
    }

    /**
     * 获取已办任务
     */
    @ApiOperation("获取已办任务")
    @PostMapping("/getDoneTask")
    public TableDataInfo<?> getDoneTask(@RequestBody BpmTaskTodoPageReqVO bpmTaskTodoPageReqVO) {
        TableDataInfo<BpmTaskDonePageItemRespVO> doneTaskPage =
                bpmTaskService.getDoneTaskPage(SecurityUtils.getUserId(), bpmTaskTodoPageReqVO);
        return doneTaskPage;
    }
}
