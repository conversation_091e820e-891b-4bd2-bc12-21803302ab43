package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.UNIT_INFService;
import com.yingfei.entity.domain.UNIT_INF;
import com.yingfei.entity.dto.UNIT_INF_DTO;
import com.yingfei.entity.vo.UNIT_INF_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "单位信息API")
@RestController
@RequestMapping("/unit_inf")
public class UNIT_INFController extends BaseController {
    
    @Resource
    private UNIT_INFService unitInfService;

    /**
     * 获取单位信息列表
     */
    @ApiOperation("获取单位信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody UNIT_INF_VO unitInfVo) {
        List<UNIT_INF_DTO> list = unitInfService.getList(unitInfVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(unitInfService.getTotal(unitInfVo));
        return dataTable;
    }

    /**
     * 新增单位信息
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:unitInf:add")
    @Log(title = "单位管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增单位信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody UNIT_INF_VO unitInfVo) {
        unitInfService.checkParam(unitInfVo);
        unitInfService.add(unitInfVo);
        return R.ok();
    }

    /**
     * 修改单位信息
     */
    @RequiresPermissions("dataManagement:unitInf:edit")
    @Log(title = "单位管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改单位信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody UNIT_INF_VO unitInfVo) {
        unitInfService.checkParam(unitInfVo);
        UNIT_INF unitInf = new UNIT_INF();
        BeanUtils.copyPropertiesIgnoreNull(unitInfVo, unitInf);
        unitInfService.updateById(unitInf);
        return R.ok();
    }

    /**
     * 批量删除单位信息
     */
    @RequiresPermissions("dataManagement:unitInf:remove")
    @Log(title = "单位管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除单位信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        unitInfService.del(ids);
        return R.ok();
    }
}
