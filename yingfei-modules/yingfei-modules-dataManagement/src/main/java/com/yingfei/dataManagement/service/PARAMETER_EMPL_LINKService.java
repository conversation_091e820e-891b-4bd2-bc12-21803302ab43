package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.domain.PARAMETER_EMPL_LINK;

import java.util.List;

public interface PARAMETER_EMPL_LINKService extends IService<PARAMETER_EMPL_LINK> {
    void add(List<PARAMETER_EMPL_LINK> parameterEmplLinkList, Long fPrst);

    List<PARAMETER_EMPL_LINK> findByNeEmplAndRole(Long userId, Long fRole);

    List<PARAMETER_EMPL_LINK> findByParameterId(Long id);
}
