package com.yingfei.dataManagement.controller.alarms;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataManagement.service.RESPONSE_ACTION_DATService;
import com.yingfei.entity.domain.RESPONSE_ACTION_DAT;
import com.yingfei.entity.dto.RESPONSE_ACTION_DAT_DTO;
import com.yingfei.entity.vo.RESPONSE_ACTION_DAT_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "改善措施信息API")
@RestController
@RequestMapping("/response_action_dat")
public class RESPONSE_ACTION_DATController extends BaseController {
    
    @Resource
    private RESPONSE_ACTION_DATService responseActionDatService;

    /**
     * 获取改善措施信息列表
     */
    @ApiOperation("获取改善措施信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody RESPONSE_ACTION_DAT_VO responseActionDatVo) {
        List<RESPONSE_ACTION_DAT_DTO> list = responseActionDatService.getList(responseActionDatVo);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(responseActionDatService.getTotal(responseActionDatVo));
        return dataTable;
    }

    /**
     * 新增改善措施信息
     */
    @CreateUpdateBy
    @RequiresPermissions("dataManagement:responseActionDat:add")
    @Log(title = "改善措施管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增改善措施信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody RESPONSE_ACTION_DAT_VO responseActionDatVo) {
        responseActionDatService.checkParam(responseActionDatVo);
        responseActionDatService.add(responseActionDatVo);
        return R.ok();
    }

    /**
     * 修改改善措施信息
     */
    @RequiresPermissions("dataManagement:responseActionDat:edit")
    @Log(title = "改善措施管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改改善措施信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody RESPONSE_ACTION_DAT_VO responseActionDatVo) {
        responseActionDatService.checkParam(responseActionDatVo);
        RESPONSE_ACTION_DAT responseActionDat = new RESPONSE_ACTION_DAT();
        BeanUtils.copyPropertiesIgnoreNull(responseActionDatVo, responseActionDat);
        responseActionDatService.updateById(responseActionDat);
        return R.ok();
    }

    /**
     * 批量删除改善措施信息
     */
    @RequiresPermissions("dataManagement:responseActionDat:remove")
    @Log(title = "改善措施管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除改善措施信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        responseActionDatService.del(ids);
        return R.ok();
    }
}
