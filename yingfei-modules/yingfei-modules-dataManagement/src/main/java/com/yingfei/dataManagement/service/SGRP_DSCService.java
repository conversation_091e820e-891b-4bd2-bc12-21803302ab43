package com.yingfei.dataManagement.service;

import com.yingfei.entity.domain.SGRP_DSC;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.SGRP_DSC_DTO;

import java.util.List;

/**
* 
* @description 针对表【SGRP_DSC(子组与描述符关联表)】的数据库操作Service
* @createDate 2024-06-13 15:01:51
*/
public interface SGRP_DSCService extends IService<SGRP_DSC> {


    List<SGRP_DSC_DTO> findBySgrpList(List<Long> fSgrp);

    List<SGRP_DSC> findByDescIds(List<Long> descList);
}
