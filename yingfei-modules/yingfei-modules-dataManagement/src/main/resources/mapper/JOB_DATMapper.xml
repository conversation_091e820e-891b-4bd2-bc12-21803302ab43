<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.JOB_DATMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.JOB_DAT">
            <id property="f_JOB" column="F_JOB" jdbcType="BIGINT"/>
            <result property="f_JBGP" column="F_JBGP" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_RELEASE_TIME" column="F_RELEASE_TIME" jdbcType="BIGINT"/>
            <result property="f_CLOSE_TIME" column="F_CLOSE_TIME" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        jobdat.F_JOB,jobdat.F_JBGP,jobdat.F_NAME,
        jobdat.F_FACTOR,jobdat.F_DEL,jobdat.F_CRUE,
        jobdat.F_RELEASE_TIME,jobdat.F_CLOSE_TIME,
        jobdat.F_EDUE,jobdat.F_CRTM,jobdat.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from JOB_DAT jobdat
        left join EMPL_INF emplinf on emplinf.F_EMPL = jobdat.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = jobdat.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and jobdat.F_DEL = 0
                </when>
                <otherwise>
                    and jobdat.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_JOB != null and F_JOB != ''">
                and jobdat.F_JOB = #{F_JOB}
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and jobdat.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and jobdat.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_JBGP != null and F_JBGP != ''">
                and jobdat.F_JBGP = #{F_JBGP}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="ids != null and ids.size() != 0">
                and jobdat.F_JOB in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="startTime != null">
                and jobdat.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and jobdat.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and jobdat.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and jobdat.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>
    
    <select id="getTotal" resultType="java.lang.Long">
        select count(jobdat.F_JOB)
        <include refid="baseSql"/>
    </select>
    
    <select id="getList" resultType="com.yingfei.entity.dto.JOB_DAT_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by jobdat.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
