<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.SYSTEM_NOTIFICATION_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.SYSTEM_NOTIFICATION_INF">

    </resultMap>

    <sql id="Base_Column_List">
        sni.F_SYNO,sni.F_DATA,sni.F_STATUS,
        sni.F_EMPL,sni.F_CRUE,sni.F_TYPE,
        sni.F_EDUE,sni.F_CRTM,sni.F_EDTM
    </sql>

    <sql id="baseSql">
        from SYSTEM_NOTIFICATION_INF sni
        <where>
            1=1
            <if test="F_STATUS != null ">
                and sni.F_STATUS = #{F_STATUS}
            </if>
            <if test="F_EMPL != null ">
                and sni.F_EMPL = #{F_EMPL}
            </if>
            <if test="F_DATA != null and F_DATA != ''">
                and sni.F_DATA like concat('%', #{F_DATA}, '%')
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(sni.F_SYNO)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.SYSTEM_NOTIFICATION_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by sni.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>


</mapper>
