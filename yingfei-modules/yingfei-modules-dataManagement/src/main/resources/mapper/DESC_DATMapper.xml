<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.DESC_DATMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.DESC_DAT">
            <id property="f_DESC" column="F_DESC" jdbcType="BIGINT"/>
            <result property="f_DSGP" column="F_DSGP" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        descdat.F_DESC,descdat.F_DSGP,descdat.F_NAME,
        descdat.F_FACTOR,descdat.F_DEL,descdat.F_CRUE,
        descdat.F_EDUE,descdat.F_CRTM,descdat.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from DESC_DAT descdat
        left join EMPL_INF emplinf on emplinf.F_EMPL = descdat.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = descdat.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and descdat.F_DEL = 0
                </when>
                <otherwise>
                    and descdat.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_DESC != null and F_DESC != ''">
                and descdat.F_DESC = #{F_DESC}
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and descdat.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and descdat.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_DSGP != null and F_DSGP != ''">
                and descdat.F_DSGP = #{F_DSGP}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="ids != null and ids.size() != 0">
                and descdat.F_DESC in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="startTime != null">
                and descdat.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and descdat.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and descdat.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and descdat.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(descdat.F_DESC)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.DESC_DAT_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by descdat.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
