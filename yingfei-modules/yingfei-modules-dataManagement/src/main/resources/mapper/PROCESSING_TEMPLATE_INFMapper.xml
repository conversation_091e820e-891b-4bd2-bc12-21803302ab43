<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.PROCESSING_TEMPLATE_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.PROCESSING_TEMPLATE_INF">
            <id property="f_PSTP" column="F_PSTP" jdbcType="BIGINT"/>
            <result property="f_DIV" column="F_DIV" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_TEST_TYPE" column="F_TEST_TYPE" jdbcType="SMALLINT"/>
            <result property="f_STANDARDIZE_TYPE" column="F_STANDARDIZE_TYPE" jdbcType="SMALLINT"/>
            <result property="f_PROCESSING_TYPE" column="F_PROCESSING_TYPE" jdbcType="SMALLINT"/>
            <result property="f_CONFIDENTIAL_INTERVAL" column="F_CONFIDENTIAL_INTERVAL" jdbcType="FLOAT"/>
            <result property="f_SIGMA_COUNT" column="F_SIGMA_COUNT" jdbcType="FLOAT"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        processingtemplateinf.F_PSTP,processingtemplateinf.F_DIV,processingtemplateinf.F_NAME,
        processingtemplateinf.F_TEST_TYPE,processingtemplateinf.F_STANDARDIZE_TYPE,processingtemplateinf.F_PROCESSING_TYPE,
        processingtemplateinf.F_CONFIDENTIAL_INTERVAL,processingtemplateinf.F_SIGMA_COUNT,processingtemplateinf.F_FACTOR,
        processingtemplateinf.F_DEL,processingtemplateinf.F_CRUE,processingtemplateinf.F_EDUE,
        processingtemplateinf.F_CRTM,processingtemplateinf.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from PROCESSING_TEMPLATE_INF processingtemplateinf
        left join EMPL_INF emplinf on emplinf.F_EMPL = processingtemplateinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = processingtemplateinf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and processingtemplateinf.F_DEL = 0
                </when>
                <otherwise>
                    and processingtemplateinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and processingtemplateinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and processingtemplateinf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and processingtemplateinf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and processingtemplateinf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and processingtemplateinf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and processingtemplateinf.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>


    <select id="getTotal" resultType="java.lang.Long">
        select count(processingtemplateinf.F_PSTP)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.PROCESSING_TEMPLATE_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by processingtemplateinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
