<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.SGRP_VAL_AMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.SGRP_VAL_A">
            <result property="f_SGRP" column="F_SGRP" jdbcType="BIGINT"/>
            <result property="f_TEST" column="F_TEST" jdbcType="BIGINT"/>
            <result property="f_DATA" column="F_DATA" jdbcType="VARCHAR"/>
            <result property="f_SGSZ" column="F_SGSZ" jdbcType="INTEGER"/>
            <result property="f_SBSZ" column="F_SBSZ" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_SGRP,F_TEST,F_DATA,
        F_SGSZ,F_SBSZ
    </sql>
</mapper>
