<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.GAUGE_AGENTMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.GAUGE_AGENT">
            <id property="f_GAAG" column="F_GAAG" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_HARDWARE" column="F_HARDWARE" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        gaugeagent.F_GAAG,gaugeagent.F_NAME,gaugeagent.F_HARDWARE,
        gaugeagent.F_CRUE,gaugeagent.F_EDUE,gaugeagent.F_CRTM,
        gaugeagent.F_EDTM
    </sql>

    <sql id="baseSql">
        from GAUGE_AGENT gaugeagent
        left join EMPL_INF emplinf on emplinf.F_EMPL = gaugeagent.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = gaugeagent.F_EDUE
        <where>
            1=1
            <if test="F_CRUE != null and F_CRUE != ''">
                and gaugeagent.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and gaugeagent.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and gaugeagent.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and gaugeagent.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and gaugeagent.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and gaugeagent.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getList" resultType="com.yingfei.entity.dto.GAUGE_AGENT_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by gaugeagent.F_CRTM desc
    </select>
</mapper>
