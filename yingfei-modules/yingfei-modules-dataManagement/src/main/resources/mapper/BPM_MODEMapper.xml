<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.BpmModelMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.BPM_MODE">
            <id property="f_MODE" column="F_MODE" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_KEY" column="F_KEY" jdbcType="VARCHAR"/>
            <result property="f_CATEGORY" column="F_CATEGORY" jdbcType="TINYINT"/>
            <result property="f_VERSION" column="F_VERSION" jdbcType="VARCHAR"/>
            <result property="f_BPMN_XML" column="F_BPMN_XML" jdbcType="VARCHAR"/>
            <result property="f_FROM_INFO" column="F_FROM_INFO" jdbcType="VARCHAR"/>
            <result property="f_DEPLOYMENT" column="F_DEPLOYMENT" jdbcType="VARCHAR"/>
            <result property="f_DESCRIPTION" column="F_DESCRIPTION" jdbcType="VARCHAR"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_MODE,F_NAME,F_KEY,
        F_CATEGORY,F_VERSION,F_BPMN_XML,
        F_FROM_INFO,F_DEPLOYMENT,F_DESCRIPTION,
        F_DEL,F_CRUE,F_EDUE,
        F_CRTM,F_EDTM
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from BPM_MODE
        where  F_MODE = #{f_MODE,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from BPM_MODE
        where  F_MODE = #{f_MODE,jdbcType=BIGINT} 
    </delete>

    <insert id="insertSelective" keyColumn="F_MODE" keyProperty="f_MODE" parameterType="com.yingfei.entity.domain.BPM_MODE" useGeneratedKeys="true">
        insert into BPM_MODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="f_MODE != null">F_MODE,</if>
                <if test="f_NAME != null">F_NAME,</if>
                <if test="f_KEY != null">F_KEY,</if>
                <if test="f_CATEGORY != null">F_CATEGORY,</if>
                <if test="f_VERSION != null">F_VERSION,</if>
                <if test="f_BPMN_XML != null">F_BPMN_XML,</if>
                <if test="f_FROM_INFO != null">F_FROM_INFO,</if>
                <if test="f_DEPLOYMENT != null">F_DEPLOYMENT,</if>
                <if test="f_DESCRIPTION != null">F_DESCRIPTION,</if>
                <if test="f_DEL != null">F_DEL,</if>
                <if test="f_CRUE != null">F_CRUE,</if>
                <if test="f_EDUE != null">F_EDUE,</if>
                <if test="f_CRTM != null">F_CRTM,</if>
                <if test="f_EDTM != null">F_EDTM,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="f_MODE != null">#{f_MODE,jdbcType=BIGINT},</if>
                <if test="f_NAME != null">#{f_NAME,jdbcType=VARCHAR},</if>
                <if test="f_KEY != null">#{f_KEY,jdbcType=VARCHAR},</if>
                <if test="f_CATEGORY != null">#{f_CATEGORY,jdbcType=TINYINT},</if>
                <if test="f_VERSION != null">#{f_VERSION,jdbcType=VARCHAR},</if>
                <if test="f_BPMN_XML != null">#{f_BPMN_XML,jdbcType=VARCHAR},</if>
                <if test="f_FROM_INFO != null">#{f_FROM_INFO,jdbcType=VARCHAR},</if>
                <if test="f_DEPLOYMENT != null">#{f_DEPLOYMENT,jdbcType=VARCHAR},</if>
                <if test="f_DESCRIPTION != null">#{f_DESCRIPTION,jdbcType=VARCHAR},</if>
                <if test="f_DEL != null">#{f_DEL,jdbcType=BIT},</if>
                <if test="f_CRUE != null">#{f_CRUE,jdbcType=BIGINT},</if>
                <if test="f_EDUE != null">#{f_EDUE,jdbcType=BIGINT},</if>
                <if test="f_CRTM != null">#{f_CRTM,jdbcType=TIMESTAMP},</if>
                <if test="f_EDTM != null">#{f_EDTM,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.yingfei.entity.domain.BPM_MODE">
        update BPM_MODE
        <set>
                <if test="f_NAME != null">
                    F_NAME = #{f_NAME,jdbcType=VARCHAR},
                </if>
                <if test="f_KEY != null">
                    F_KEY = #{f_KEY,jdbcType=VARCHAR},
                </if>
                <if test="f_CATEGORY != null">
                    F_CATEGORY = #{f_CATEGORY,jdbcType=TINYINT},
                </if>
                <if test="f_VERSION != null">
                    F_VERSION = #{f_VERSION,jdbcType=VARCHAR},
                </if>
                <if test="f_BPMN_XML != null">
                    F_BPMN_XML = #{f_BPMN_XML,jdbcType=VARCHAR},
                </if>
                <if test="f_FROM_INFO != null">
                    F_FROM_INFO = #{f_FROM_INFO,jdbcType=VARCHAR},
                </if>
                <if test="f_DEPLOYMENT != null">
                    F_DEPLOYMENT = #{f_DEPLOYMENT,jdbcType=VARCHAR},
                </if>
                <if test="f_DESCRIPTION != null">
                    F_DESCRIPTION = #{f_DESCRIPTION,jdbcType=VARCHAR},
                </if>
                <if test="f_DEL != null">
                    F_DEL = #{f_DEL,jdbcType=BIT},
                </if>
                <if test="f_CRUE != null">
                    F_CRUE = #{f_CRUE,jdbcType=BIGINT},
                </if>
                <if test="f_EDUE != null">
                    F_EDUE = #{f_EDUE,jdbcType=BIGINT},
                </if>
                <if test="f_CRTM != null">
                    F_CRTM = #{f_CRTM,jdbcType=TIMESTAMP},
                </if>
                <if test="f_EDTM != null">
                    F_EDTM = #{f_EDTM,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   F_MODE = #{f_MODE,jdbcType=BIGINT} 
    </update>

</mapper>
