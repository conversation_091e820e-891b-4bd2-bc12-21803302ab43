<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.TAG_GRPMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.TAG_GRP">
            <id property="f_TGGP" column="F_TGGP" jdbcType="BIGINT"/>
            <result property="f_DIV" column="F_DIV" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        taggrp.F_TGGP,taggrp.F_DIV,taggrp.F_NAME,
        taggrp.F_FACTOR,taggrp.F_DEL,taggrp.F_CRUE,
        taggrp.F_EDUE,taggrp.F_CRTM,taggrp.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from TAG_GRP taggrp
        left join EMPL_INF emplinf on emplinf.F_EMPL = taggrp.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = taggrp.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and taggrp.F_DEL = 0
                </when>
                <otherwise>
                    and taggrp.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and taggrp.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_DIV != null and F_DIV != ''">
                and taggrp.F_DIV = #{F_DIV}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and taggrp.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
        </where>
    </sql>


    <select id="getTotal" resultType="java.lang.Long">
        select count(taggrp.F_TGGP)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.TAG_GRP_DTO">
        select
            <include refid="Base_Column_List"/>
            <include refid="baseSql"/>
        order by taggrp.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
