<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.INSPECTION_PLAN_RECORD_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.INSPECTION_PLAN_RECORD_INF">

    </resultMap>

    <sql id="Base_Column_List">

    </sql>

    <select id="getLatestRecord" resultType="com.yingfei.entity.domain.INSPECTION_PLAN_RECORD_INF">
        select
        <if test="dbType == 1">
            top 1
        </if>
            *
        from INSPECTION_PLAN_RECORD_INF
        where
            F_PLAN = #{F_PLAN}
        order by F_CRTM DESC
        <choose>
            <when test="dbType == 2">
                FETCH FIRST 1 ROW ONLY;
            </when>
            <when test="dbType == 3">
                LIMIT 1
            </when>
        </choose>
    </select>


</mapper>
