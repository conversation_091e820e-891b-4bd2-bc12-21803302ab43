<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.TAG_DATMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.TAG_DAT">
            <id property="f_TAG" column="F_TAG" jdbcType="BIGINT"/>
            <result property="f_TGGP" column="F_TGGP" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        tagdat.F_TAG,tagdat.F_TGGP,tagdat.F_NAME,
        tagdat.F_FACTOR,tagdat.F_DEL,tagdat.F_CRUE,
        tagdat.F_EDUE,tagdat.F_CRTM,tagdat.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName,
        taggrp.F_NAME as tggpName
    </sql>

    <sql id="baseSql">
        from TAG_DAT tagdat
        left join TAG_GRP taggrp on taggrp.F_TGGP = tagdat.F_TGGP
        left join EMPL_INF emplinf on emplinf.F_EMPL = tagdat.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = tagdat.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and tagdat.F_DEL = 0
                </when>
                <otherwise>
                    and tagdat.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and tagdat.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and tagdat.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_TGGP != null and F_TGGP != ''">
                and tagdat.F_TGGP = #{F_TGGP}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="ids != null and ids.size() != 0">
                and tagdat.F_TAG in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="startTime != null">
                and tagdat.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and tagdat.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and tagdat.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and tagdat.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(tagdat.F_TAG)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.TAG_DAT_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by tagdat.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
