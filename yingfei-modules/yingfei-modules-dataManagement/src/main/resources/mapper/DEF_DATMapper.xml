<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.DEF_DATMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.DEF_DAT">
            <id property="f_DEF" column="F_DEF" jdbcType="BIGINT"/>
            <result property="f_DFGP" column="F_DFGP" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_IMAGE" column="F_IMAGE" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        defdat.F_DEF,defdat.F_DFGP,defdat.F_NAME,
        defdat.F_IMAGE,defdat.F_FACTOR,defdat.F_DEL,
        defdat.F_CRUE,defdat.F_EDUE,defdat.F_CRTM,
        defdat.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from DEF_DAT defdat
        left join EMPL_INF emplinf on emplinf.F_EMPL = defdat.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = defdat.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and defdat.F_DEL = 0
                </when>
                <otherwise>
                    and defdat.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_DEF != null and F_DEF != ''">
                and defdat.F_DEF = #{F_DEF}
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and defdat.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and defdat.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_DFGP != null and F_DFGP != ''">
                and defdat.F_DFGP = #{F_DFGP}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="ids != null and ids.size() != 0">
                and defdat.F_DEF in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="startTime != null">
                and defdat.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and defdat.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and defdat.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and defdat.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(defdat.F_DEF)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.DEF_DAT_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by defdat.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
