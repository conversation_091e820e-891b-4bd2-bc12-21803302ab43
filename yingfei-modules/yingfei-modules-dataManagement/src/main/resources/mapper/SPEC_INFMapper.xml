<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.SPEC_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.SPEC_INF">
            <id property="f_SPEC" column="F_SPEC" jdbcType="BIGINT"/>
            <result property="f_PART" column="F_PART" jdbcType="BIGINT"/>
            <result property="f_TEST" column="F_TEST" jdbcType="BIGINT"/>
            <result property="f_PRCS" column="F_PRCS" jdbcType="BIGINT"/>
            <result property="f_JOB" column="F_JOB" jdbcType="BIGINT"/>
            <result property="f_USL" column="F_USL" jdbcType="FLOAT"/>
            <result property="f_TAR" column="F_TAR" jdbcType="FLOAT"/>
            <result property="f_LSL" column="F_LSL" jdbcType="FLOAT"/>
            <result property="f_URL" column="F_URL" jdbcType="FLOAT"/>
            <result property="f_LRL" column="F_LRL" jdbcType="FLOAT"/>
            <result property="f_UWL" column="F_UWL" jdbcType="FLOAT"/>
            <result property="f_LWL" column="F_LWL" jdbcType="FLOAT"/>
            <result property="f_UWP" column="F_UWP" jdbcType="FLOAT"/>
            <result property="f_LWP" column="F_LWP" jdbcType="FLOAT"/>
            <result property="f_UAL" column="F_UAL" jdbcType="FLOAT"/>
            <result property="f_LAL" column="F_LAL" jdbcType="FLOAT"/>
            <result property="f_CP" column="F_CP" jdbcType="FLOAT"/>
            <result property="f_CPK" column="F_CPK" jdbcType="FLOAT"/>
            <result property="f_PP" column="F_PP" jdbcType="FLOAT"/>
            <result property="f_PPK" column="F_PPK" jdbcType="FLOAT"/>
            <result property="f_AFLAG" column="F_AFLAG" jdbcType="BIGINT"/>
            <result property="f_EFLAG" column="F_EFLAG" jdbcType="BIGINT"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_PTRV" column="F_PTRV" jdbcType="BIGINT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        specinf.F_SPEC,specinf.F_PART,specinf.F_TEST,
        specinf.F_PRCS,specinf.F_JOB,specinf.F_USL,
        specinf.F_TAR,specinf.F_LSL,specinf.F_URL,
        specinf.F_LRL,specinf.F_UWL,specinf.F_LWL,
        specinf.F_UWP,specinf.F_LWP,specinf.F_UAL,
        specinf.F_LAL,specinf.F_CP,specinf.F_CPK,
        specinf.F_PP,specinf.F_PPK,specinf.F_AFLAG,
        specinf.F_EFLAG,specinf.F_FACTOR,specinf.F_DEL,
        specinf.F_CRUE,specinf.F_EDUE,specinf.F_CRTM,
        specinf.F_EDTM,specinf.F_PTRV,
        partinf.F_NAME as partName,
        prcsinf.F_NAME as prcsName,
        testinf.F_NAME as testName,
        partrev.F_NAME as ptrvName,
        jobdat.F_NAME as jobName,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from SPEC_INF specinf
        left join PART_INF partinf on partinf.F_PART = specinf.F_PART
        left join PRCS_INF prcsinf on prcsinf.F_PRCS = specinf.F_PRCS
        left join TEST_INF testinf on testinf.F_TEST = specinf.F_TEST
        left join PART_REV partrev on partrev.F_PTRV = specinf.F_PTRV
        left join JOB_DAT jobdat on jobdat.F_JOB = specinf.F_JOB
        left join EMPL_INF emplinf on emplinf.F_EMPL = specinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = specinf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and specinf.F_DEL = 0
                </when>
                <otherwise>
                    and specinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and specinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_PART != null and F_PART != ''">
                and specinf.F_PART = #{F_PART}
            </if>
            <if test="F_TEST != null and F_TEST != ''">
                and specinf.F_TEST = #{F_TEST}
            </if>
            <if test="F_PTRV != null and F_PTRV != ''">
                and specinf.F_PTRV = #{F_PTRV}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="partIds != null and partIds.size() != 0">
                and specinf.F_PART in
                <foreach collection="partIds" item="part" open="(" close=")" separator=",">
                    #{part}
                </foreach>
            </if>
            <if test="prcsIds != null and prcsIds.size() != 0">
                and specinf.F_PRCS in
                <foreach collection="prcsIds" item="prcs" open="(" close=")" separator=",">
                    #{prcs}
                </foreach>
            </if>
            <if test="testIds != null and testIds.size() != 0">
                and specinf.F_TEST in
                <foreach collection="testIds" item="test" open="(" close=")" separator=",">
                    #{test}
                </foreach>
            </if>
            <if test="startTime != null">
                and specinf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and specinf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and specinf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and specinf.F_EDTM &lt;= #{edEndTime}
            </if>
            <if test="hierarchyInfIds != null and hierarchyInfIds.size() != 0">
                and partinf.F_PLNT in
                <foreach collection="hierarchyInfIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </sql>


    <select id="getTotal" resultType="java.lang.Long">
        select count(specinf.F_SPEC)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.SPEC_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by specinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
