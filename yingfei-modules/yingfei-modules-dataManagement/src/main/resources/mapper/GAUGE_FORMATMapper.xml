<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.GAUGE_FORMATMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.GAUGE_FORMAT">
            <id property="f_GAFO" column="F_GAFO" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_LENGTH" column="F_LENGTH" jdbcType="INTEGER"/>
            <result property="f_START" column="F_START" jdbcType="VARCHAR"/>
            <result property="f_END" column="F_END" jdbcType="VARCHAR"/>
            <result property="f_SPLIT" column="F_SPLIT" jdbcType="VARCHAR"/>
            <result property="f_DATA_CONFIG" column="F_DATA_CONFIG" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        gaugeformat.F_GAFO,gaugeformat.F_NAME,gaugeformat.F_LENGTH,
        gaugeformat.F_START,gaugeformat.F_END,gaugeformat.F_SPLIT,
        gaugeformat.F_DATA_CONFIG,gaugeformat.F_CRUE,gaugeformat.F_EDUE,
        gaugeformat.F_CRTM,gaugeformat.F_EDTM
    </sql>

    <sql id="baseSql">
        from GAUGE_FORMAT gaugeformat
        left join EMPL_INF emplinf on emplinf.F_EMPL = gaugeformat.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = gaugeformat.F_EDUE
        <where>
            1=1
            <if test="F_CRUE != null and F_CRUE != ''">
                and gaugeformat.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and gaugeformat.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and gaugeformat.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and gaugeformat.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and gaugeformat.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and gaugeformat.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getList" resultType="com.yingfei.entity.dto.GAUGE_FORMAT_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by gaugeformat.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
