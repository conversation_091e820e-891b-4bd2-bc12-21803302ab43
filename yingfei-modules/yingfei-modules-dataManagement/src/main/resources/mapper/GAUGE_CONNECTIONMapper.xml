<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.GAUGE_CONNECTIONMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.GAUGE_CONNECTION">
            <id property="f_GICP" column="F_GICP" jdbcType="BIGINT"/>
            <result property="f_GAIN" column="F_GAIN" jdbcType="BIGINT"/>
            <result property="f_GAAG" column="F_GAAG" jdbcType="BIGINT"/>
            <result property="f_CONFIG" column="F_CONFIG" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        gaugeconnection.F_GICP,gaugeconnection.F_GAIN,gaugeconnection.F_GAAG,
        gaugeconnection.F_CONFIG,gaugeconnection.F_CRUE,gaugeconnection.F_EDUE,
        gaugeconnection.F_CRTM,gaugeconnection.F_EDTM,
        gaugeagent.F_NAME as agentName
    </sql>

    <sql id="baseSql">
        from GAUGE_CONNECTION gaugeconnection
        left join GAUGE_AGENT gaugeagent on gaugeagent.F_GAAG = gaugeconnection.F_GAAG
        left join EMPL_INF emplinf on emplinf.F_EMPL = gaugeconnection.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = gaugeconnection.F_EDUE
        <where>
            1=1
            <if test="F_GAAG != null and F_GAAG != ''">
                and gaugeconnection.F_GAAG = #{F_GAAG}
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and gaugeconnection.F_CRUE = #{F_CRUE}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and gaugeconnection.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and gaugeconnection.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and gaugeconnection.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and gaugeconnection.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getList" resultType="com.yingfei.entity.dto.GAUGE_CONNECTION_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by gaugeconnection.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
