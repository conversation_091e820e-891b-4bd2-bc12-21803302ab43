<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.EVNT_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.EVNT_INF">
            <id property="f_EVNT" column="F_EVNT" jdbcType="BIGINT"/>
            <result property="f_PART" column="F_PART" jdbcType="BIGINT"/>
            <result property="f_PRCS" column="F_PRCS" jdbcType="BIGINT"/>
            <result property="f_TEST" column="F_TEST" jdbcType="BIGINT"/>
            <result property="f_EVTM" column="F_EVTM" jdbcType="TIMESTAMP"/>
            <result property="f_SGTM" column="F_SGTM" jdbcType="TIMESTAMP"/>
            <result property="f_SGRP" column="F_SGRP" jdbcType="BIGINT"/>
            <result property="f_RTCS" column="F_RTCS" jdbcType="BIGINT"/>
            <result property="f_RCTM" column="F_RCTM" jdbcType="TIMESTAMP"/>
            <result property="f_RSAT" column="F_RSAT" jdbcType="BIGINT"/>
            <result property="f_RSTM" column="F_RSTM" jdbcType="TIMESTAMP"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_DATA" column="F_DATA" jdbcType="TIMESTAMP"/>
            <result property="f_STATUS" column="F_STATUS" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        evntinf.F_EVNT,
        evntinf.F_PART,evntinf.F_PRCS,evntinf.F_TEST,
        evntinf.F_EVTM,evntinf.F_SGTM,evntinf.F_SGRP,
        evntinf.F_RTCS,evntinf.F_RCTM,evntinf.F_RSAT,
        evntinf.F_RSTM,evntinf.F_DEL,evntinf.F_CRUE,
        evntinf.F_EDUE,evntinf.F_CRTM,evntinf.F_EDTM,
        evntinf.F_DATA,evntinf.F_STATUS,
        pai.F_NAME as partName,
        pri.F_NAME as prcsName,
        ti.F_NAME as testName
    </sql>

    <sql id="baseSql">
        from EVNT_INF evntinf
        left join PART_INF pai on pai.F_PART=evntinf.F_PART
        left join PRCS_INF pri on pri.F_PRCS=evntinf.F_PRCS
        left join TEST_INF ti on ti.F_TEST=evntinf.F_TEST
        left join EMPL_INF emplinf on emplinf.F_EMPL = evntinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = evntinf.F_EDUE
        <if test="isTemp == 1">
            LEFT JOIN TEMP_TABLE_INF tti ON tti.F_ID = si.F_SGRP
        </if>
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and evntinf.F_DEL = 0
                </when>
                <otherwise>
                    and evntinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_PART != null">
                and evntinf.F_PART = #{F_PART}
            </if>
            <if test="F_PRCS != null">
                and evntinf.F_PRCS = #{F_PRCS}
            </if>
            <if test="F_TEST != null">
                and evntinf.F_TEST = #{F_TEST}
            </if>
            <if test="F_STATUS != null">
                and evntinf.F_STATUS = #{F_STATUS}
            </if>
            <if test="partList != null and partList.size() != 0">
                and evntinf.F_PART in
                <foreach collection="partList" item="part" open="(" close=")" separator=",">
                    #{part}
                </foreach>
            </if>
            <if test="prcsList != null and prcsList.size() != 0">
                and evntinf.F_PRCS in
                <foreach collection="prcsList" item="prcs" open="(" close=")" separator=",">
                    #{prcs}
                </foreach>
            </if>
            <if test="testList != null and testList.size() != 0">
                and evntinf.F_TEST in
                <foreach collection="testList" item="test" open="(" close=")" separator=",">
                    #{test}
                </foreach>
            </if>
            <choose>
                <when test="isTemp == 1">
                    and tti.F_IDENTIFY = #{tempIdentify}
                </when>
                <otherwise>
                    <if test="sgrpInfList != null and sgrpInfList.size() != 0">
                        and evntinf.F_SGRP in
                        <foreach collection="sgrpInfList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </if>
                </otherwise>
            </choose>

            <if test="F_CRUE != null and F_CRUE != ''">
                and evntinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and evntinf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and evntinf.F_EVTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and evntinf.F_EVTM &lt;= #{endTime}
            </if>
        </where>
    </sql>
    
    <select id="getTotal" resultType="java.lang.Long">
        select count(evntinf.F_EVNT)
        <include refid="baseSql"/>
    </select>
    
    <select id="getList" resultType="com.yingfei.entity.dto.EVNT_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by evntinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>

    <select id="getRtcsList" resultType="com.yingfei.entity.domain.RESPONSE_ACTION_DAT">
        SELECT
        rad.*
        FROM RESPONSE_ACTION_DAT rad
        WHERE
        1=1
        AND rad.F_RSAT IN (
        SELECT
        evntinf.F_RSAT
        <include refid="baseSql"/>
        GROUP BY evntinf.F_RSAT
        )
    </select>

    <select id="getRsatList" resultType="com.yingfei.entity.domain.ROOT_CAUSE_DAT">
        SELECT
        rcd.*
        FROM ROOT_CAUSE_DAT rcd
        WHERE
        1=1
        AND rcd.F_RTCS IN (
        SELECT
        evntinf.F_RTCS
        <include refid="baseSql"/>
        GROUP BY evntinf.F_RTCS
        )
    </select>

    <select id="getCreateList" resultType="com.yingfei.entity.dto.EMPL_INF_DTO">
        SELECT
        ei.F_EMPL,ei.F_NAME
        FROM EMPL_INF ei
        WHERE
        1=1
        AND ei.F_EMPL IN (
        SELECT
        evntinf.F_CRUE
        <include refid="baseSql"/>
        GROUP BY evntinf.F_CRUE
        )
    </select>

    <select id="getUpdateList" resultType="com.yingfei.entity.dto.EMPL_INF_DTO">
        SELECT
        ei.F_EMPL,ei.F_NAME
        FROM EMPL_INF ei
        WHERE
        1=1
        AND ei.F_EMPL IN (
        SELECT
        evntinf.F_EDUE
        <include refid="baseSql"/>
        GROUP BY evntinf.F_EDUE
        )
    </select>
</mapper>
