<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.GAUGE_DEVICEMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.GAUGE_DEVICE">
            <id property="f_GADE" column="F_GADE" jdbcType="BIGINT"/>
            <result property="f_GICP" column="F_GICP" jdbcType="BIGINT"/>
            <result property="f_GAFO" column="F_GAFO" jdbcType="BIGINT"/>
            <result property="f_CONFIG" column="F_CONFIG" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_NAME" column="F_NAME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        gaugedevice.F_GADE,gaugedevice.F_GICP,gaugedevice.F_GAFO,
        gaugedevice.F_CONFIG,gaugedevice.F_CRUE,gaugedevice.F_EDUE,
        gaugedevice.F_CRTM,gaugedevice.F_EDTM,gaugedevice.F_NAME,
        gaugeformat.F_NAME as gaFoName
    </sql>

    <sql id="baseSql">
        from GAUGE_DEVICE gaugedevice
        left join GAUGE_FORMAT gaugeformat on gaugeformat.F_GAFO = gaugedevice.F_GAFO
        left join EMPL_INF emplinf on emplinf.F_EMPL = gaugedevice.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = gaugedevice.F_EDUE
        <where>
            1=1
            <if test="F_CRUE != null and F_CRUE != ''">
                and gaugedevice.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and gaugedevice.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and gaugedevice.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and gaugedevice.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and gaugedevice.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and gaugedevice.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getList" resultType="com.yingfei.entity.dto.GAUGE_DEVICE_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by gaugedevice.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
