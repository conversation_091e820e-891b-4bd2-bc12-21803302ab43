<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.DICT_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.DICT_INF">
            <id property="f_DICT" column="F_DICT" jdbcType="BIGINT"/>
            <result property="f_GROUP" column="F_GROUP" jdbcType="VARCHAR"/>
            <result property="f_CODE" column="F_CODE" jdbcType="VARCHAR"/>
            <result property="f_ZH_CN" column="F_ZH_CN" jdbcType="VARCHAR"/>
            <result property="f_ZH_TW" column="F_ZH_TW" jdbcType="VARCHAR"/>
            <result property="f_EN" column="F_EN" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        dictinf.F_DICT,dictinf.F_GROUP,dictinf.F_CODE,
        dictinf.F_ZH_CN,dictinf.F_ZH_TW,dictinf.F_EN,
        dictinf.F_CRUE,dictinf.F_EDUE,dictinf.F_CRTM,
        dictinf.F_EDTM
    </sql>

    <sql id="baseSql">
        from DICT_INF dictinf
        left join EMPL_INF emplinf on emplinf.F_EMPL = dictinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = dictinf.F_EDUE
        <where>
            1=1
            <if test="F_NAME != null and F_NAME != ''">
                and dictinf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_GROUP != null and F_GROUP != ''">
                and dictinf.F_GROUP like concat('%', #{F_GROUP}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(dictinf.F_DICT)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.DICT_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by dictinf.F_CRTM,dictinf.F_GROUP desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
