<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.RULE_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.RULE_INF">
            <id property="f_ALR" column="F_ALR" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="SMALLINT"/>
            <result property="f_PRIORITY" column="F_PRIORITY" jdbcType="SMALLINT"/>
            <result property="f_RULE_TYPE" column="F_RULE_TYPE" jdbcType="SMALLINT"/>
            <result property="f_COUNT" column="F_COUNT" jdbcType="SMALLINT"/>
            <result property="f_HITS" column="F_HITS" jdbcType="SMALLINT"/>
            <result property="f_ABBR" column="F_ABBR" jdbcType="VARCHAR"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_FLAG" column="F_FLAG" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ruleinf.F_ALR,ruleinf.F_PRIORITY,ruleinf.F_RULE_TYPE,
        ruleinf.F_COUNT,ruleinf.F_HITS,ruleinf.F_ABBR,
        ruleinf.F_DEL,ruleinf.F_CRUE,ruleinf.F_EDUE,
        ruleinf.F_CRTM,ruleinf.F_EDTM,ruleinf.F_NAME,
        ruleinf.F_FLAG,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from RULE_INF ruleinf
        left join EMPL_INF emplinf on emplinf.F_EMPL = ruleinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = ruleinf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and ruleinf.F_DEL = 0
                </when>
                <otherwise>
                    and ruleinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and ruleinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and ruleinf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and ruleinf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and ruleinf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and ruleinf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and ruleinf.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(ruleinf.F_ALR)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.RULE_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by ruleinf.F_RULE_TYPE asc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
