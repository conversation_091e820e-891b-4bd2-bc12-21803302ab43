<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.EMPL_AGENDA_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.EMPL_AGENDA_INF">
            <id property="f_ID" column="F_ID" jdbcType="BIGINT"/>
            <result property="f_DATE" column="F_DATE" jdbcType="TIMESTAMP"/>
            <result property="f_CONTENT" column="F_CONTENT" jdbcType="VARCHAR"/>
            <result property="f_TYPE" column="F_TYPE" jdbcType="TINYINT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_STATUS" column="F_STATUS" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        eai.F_ID,eai.F_DATE,eai.F_CONTENT,
        eai.F_TYPE,eai.F_CRUE,eai.F_EDUE,
        eai.F_CRTM,eai.F_EDTM,eai.F_STATUS,
        emplinf.F_EMAIL email,emplinf.F_WECHAT weChat,
        emplinf.F_DINGDING dingDing
    </sql>

    <select id="getList" resultType="com.yingfei.entity.dto.EMPL_AGENDA_INF_DTO">
        select
            <include refid="Base_Column_List"/>
        from EMPL_AGENDA_INF eai
        left join EMPL_INF emplinf on emplinf.F_EMPL = eai.F_CRUE
        <where>
            1=1
            <if test="F_DATE != null">
                and eai.F_DATE = #{F_DATE}
            </if>
            <if test="F_STATUS != null">
                and eai.F_STATUS = #{F_STATUS}
            </if>
            <if test="startTime != null">
                and eai.F_DATE &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and eai.F_DATE &lt;= #{endTime}
            </if>
        </where>
    </select>
</mapper>
