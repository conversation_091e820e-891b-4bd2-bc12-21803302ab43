<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.PART_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.dto.PART_INF_DTO">
        <id property="f_PART" column="F_PART" jdbcType="BIGINT"/>
        <result property="f_PLNT" column="F_PLNT" jdbcType="BIGINT"/>
        <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
        <result property="f_LONG_NAME" column="F_LONG_NAME" jdbcType="VARCHAR"/>
        <result property="f_IMAGE" column="F_IMAGE" jdbcType="VARCHAR"/>
        <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
        <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
        <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
        <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
        <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
        <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
        <result property="tagCount" column="tagCount" jdbcType="VARCHAR"/>
        <result property="createName" column="createName" jdbcType="VARCHAR"/>
        <result property="updateName" column="updateName" jdbcType="VARCHAR"/>
        <collection property="partRevList" ofType="com.yingfei.entity.domain.PART_REV">
            <result property="f_PTRV" column="revF_PTRV" jdbcType="BIGINT"/>
            <result property="f_PART" column="revF_PART" jdbcType="BIGINT"/>
            <result property="f_NAME" column="revF_NAME" jdbcType="VARCHAR"/>
            <result property="f_STTM" column="revF_STTM" jdbcType="TIMESTAMP"/>
            <result property="f_FNTM" column="revF_FNTM" jdbcType="TIMESTAMP"/>
            <result property="f_DEL" column="revF_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="revF_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="revF_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="revF_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="revF_EDTM" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        F_PART
        ,F_PLNT,F_NAME,
        F_LONG_NAME,F_IMAGE,
        F_FACTOR,F_DEL,
        F_CRUE,F_EDUE,F_CRTM,
        F_EDTM
    </sql>

    <sql id="Base_Column_List_Dto">
        partinf.F_PART,
        partinf.F_PLNT,
        partinf.F_NAME,
        partinf.F_LONG_NAME,
        partinf.F_IMAGE,
        partinf.F_FACTOR,
        partinf.F_DEL,
        partinf.F_CRUE,
        partinf.F_EDUE,
        partinf.F_CRTM,
        partinf.F_EDTM,

        partrev.F_PTRV as revF_PTRV,
        partrev.F_PART as revF_PART,
        partrev.F_NAME as revF_NAME,
        partrev.F_STTM as revF_STTM,
        partrev.F_FNTM as revF_FNTM,
        partrev.F_DEL as revF_DEL,
        partrev.F_CRUE as revF_CRUE,
        partrev.F_EDUE as revF_EDUE,
        partrev.F_CRTM as revF_CRTM,
        partrev.F_EDTM as revF_EDTM,
        hi.F_NAME as F_PLNT_NAME,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from PART_INF partinf
        left join HIERARCHY_INF hi on hi.F_HIER = partinf.F_PLNT
        left join PART_REV partrev on partrev.F_PART = partinf.F_PART and partrev.F_DEL = 0
        left join EMPL_INF emplinf on emplinf.F_EMPL = partinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = partinf.F_EDUE
        left join TAG_LINK tal on tal.F_RESOURCE = partinf.F_PART and tal.F_TYPE = 1
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and partinf.F_DEL = 0
                </when>
                <otherwise>
                    and partinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_PART != null and F_PART != ''">
                and partinf.F_PART = #{F_PART}
            </if>
            <if test="F_PLNT != null and F_PLNT != ''">
                and partinf.F_PLNT = #{F_PLNT}
            </if>
            <if test="F_PLNT_NAME != null and F_PLNT_NAME != ''">
                and hi.F_NAME = #{F_PLNT_NAME}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and partinf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_LONG_NAME != null and F_LONG_NAME != ''">
                and partinf.F_LONG_NAME like concat('%', #{F_LONG_NAME}, '%')
            </if>
            <if test="F_IMAGE != null and F_IMAGE != ''">
                and partinf.F_IMAGE = #{F_IMAGE}
            </if>
            <if test="F_FACTOR != null and F_FACTOR != ''">
                and partinf.F_FACTOR = #{F_FACTOR}
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and partinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_EDUE != null and F_EDUE != ''">
                and partinf.F_EDUE = #{F_EDUE}
            </if>
            <if test="F_CRTM != null and F_CRTM != ''">
                and partinf.F_CRTM = #{F_CRTM}
            </if>
            <if test="F_EDTM != null and F_EDTM != ''">
                and partinf.F_EDTM = #{F_EDTM}
            </if>
            <if test="partIds != null and partIds.size() != 0">
                and partinf.F_PART in
                <foreach collection="partIds" item="part" open="(" close=")" separator=",">
                    #{part}
                </foreach>
            </if>
            <if test="partRevIds != null and partRevIds.size() != 0">
                and partrev.F_PTRV in
                <foreach collection="partRevIds" item="partRev" open="(" close=")" separator=",">
                    #{partRev}
                </foreach>
            </if>
            <if test="partRevName != null and partRevName != ''">
                and partrev.F_NAME like concat('%', #{partRevName}, '%')
            </if>
            <choose>
                <when test="status == 1">
                    and (partrev.F_STTM &gt;= #{closeTime}
                        or partrev.F_FNTM &lt;= #{closeTime})
                </when>
                <when test="status == 0">
                    and (partrev.F_STTM &lt;= #{closeTime}
                        and partrev.F_FNTM &gt;= #{closeTime})
                </when>
            </choose>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="hierarchyInfIds != null and hierarchyInfIds.size() != 0">
                and partinf.F_PLNT in
                <foreach collection="hierarchyInfIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="parameterHierIds != null and parameterHierIds.size() != 0">
                and partinf.F_PLNT in
                <foreach collection="parameterHierIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="tggpId != null and tggpId != ''">
                and tal.F_TGGP = #{tggpId}
            </if>
            <if test="tagIds != null and tagIds.size() != 0">
                and tal.F_TAG in
                <foreach collection="tagIds" item="tagId" open="(" close=")" separator=",">
                    #{tagId}
                </foreach>
            </if>
            <if test="startTime != null">
                and partinf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and partinf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and partinf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and partinf.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Dto"/>
        <include refid="baseSql"/>
        ORDER BY partinf.F_CRTM DESC
    </select>

    <select id="getPage" resultType="com.yingfei.entity.dto.PART_INF_DTO">
        select DISTINCT partinf.F_PART,partinf.F_CRTM
        <include refid="baseSql"/>
        ORDER BY partinf.F_CRTM DESC
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>

    <select id="getTotal" resultType="java.lang.Long">
        select count(DISTINCT partinf.F_PART)
        <include refid="baseSql"/>
    </select>

    <select id="getUnfoldTotal" resultType="java.lang.Long">
        select count(DISTINCT partrev.F_PTRV)
        <include refid="baseSql"/>
    </select>

    <select id="getUnfoldList" resultType="com.yingfei.entity.dto.PART_INF_DTO">
        select DISTINCT
            partinf.F_PART,
            partinf.F_PLNT,
            partinf.F_NAME,
            partinf.F_LONG_NAME,
            partinf.F_IMAGE,
            partinf.F_FACTOR,
            partinf.F_DEL,
            partinf.F_CRUE,
            partinf.F_EDUE,
            partinf.F_CRTM,
            partinf.F_EDTM,
            partrev.F_NAME as partRevName,
            partrev.F_PTRV as partRevId,
            partrev.F_FNTM as partRevEndTime,
            partrev.F_STTM as partRevStartTime,
            partrev.F_CRTM as partRevCreateTime,
            partrev.F_EDTM as partRevEditTime,
            hi.F_NAME as F_PLNT_NAME,
            emplinf.F_NAME as createName,
            emplinf_u.F_NAME as updateName
        <include refid="baseSql"/>
        ORDER BY partinf.F_CRTM DESC
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>

</mapper>
