<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.UNIT_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.UNIT_INF">
            <id property="f_UNIT" column="F_UNIT" />
            <result property="f_NAME" column="F_NAME" />
            <result property="f_ABBR" column="F_ABBR" />
            <result property="f_FACTOR" column="F_FACTOR" />
            <result property="f_DEL" column="F_DEL" />
            <result property="f_CRUE" column="F_CRUE" />
            <result property="f_EDUE" column="F_EDUE" />
            <result property="f_CRTM" column="F_CRTM" />
            <result property="f_EDTM" column="F_EDTM" />
    </resultMap>

    <sql id="Base_Column_List">
        ui.F_UNIT,ui.F_NAME,ui.F_ABBR,ui.F_FACTOR,ui.F_DEL,ui.F_CRUE,
        ui.F_EDUE,ui.F_CRTM,ui.F_EDTM,
        emplinf.F_NAME as createName,emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from UNIT_INF ui
        left join EMPL_INF emplinf on emplinf.F_EMPL = ui.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = ui.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and ui.F_DEL = 0
                </when>
                <otherwise>
                    and ui.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and ui.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and ui.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and ui.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and ui.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and ui.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and ui.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(ui.F_UNIT)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.UNIT_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by ui.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
