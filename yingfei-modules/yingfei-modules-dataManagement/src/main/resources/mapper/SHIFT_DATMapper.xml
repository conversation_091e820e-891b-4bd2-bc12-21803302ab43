<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.SHIFT_DATMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.SHIFT_DAT">
            <id property="f_SHIFT" column="F_SHIFT" jdbcType="BIGINT"/>
            <result property="f_SHGP" column="F_SHGP" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        shiftdat.F_SHIFT,shiftdat.F_SHGP,shiftdat.F_NAME,
        shiftdat.F_FACTOR,shiftdat.F_DEL,shiftdat.F_CRUE,
        shiftdat.F_EDUE,shiftdat.F_CRTM,shiftdat.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from SHIFT_DAT shiftdat
        left join EMPL_INF emplinf on emplinf.F_EMPL = shiftdat.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = shiftdat.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and shiftdat.F_DEL = 0
                </when>
                <otherwise>
                    and shiftdat.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_SHIFT != null and F_SHIFT != ''">
                and shiftdat.F_SHIFT = #{F_SHIFT}
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and shiftdat.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and shiftdat.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_SHGP != null and F_SHGP != ''">
                and shiftdat.F_SHGP = #{F_SHGP}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="ids != null and ids.size() != 0">
                and shiftdat.F_SHIFT in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="startTime != null">
                and shiftdat.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and shiftdat.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and shiftdat.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and shiftdat.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(shiftdat.F_SHIFT)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.SHIFT_DAT_DTO">
        select
            <include refid="Base_Column_List"/>
            <include refid="baseSql"/>
        order by shiftdat.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
