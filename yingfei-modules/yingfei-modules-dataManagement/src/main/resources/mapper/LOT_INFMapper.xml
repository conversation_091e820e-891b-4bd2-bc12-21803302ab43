<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.LOT_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.LOT_INF">
            <id property="f_LOT" column="F_LOT" jdbcType="BIGINT"/>
            <result property="f_DIV" column="F_DIV" jdbcType="BIGINT"/>
            <result property="f_PART" column="F_PART" jdbcType="BIGINT"/>
            <result property="f_PRCS" column="F_PRCS" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_COUNT" column="F_COUNT" jdbcType="INTEGER"/>
            <result property="f_RELEASE_TIME" column="F_RELEASE_TIME" jdbcType="TIMESTAMP"/>
            <result property="f_CLOSE_TIME" column="F_CLOSE_TIME" jdbcType="TIMESTAMP"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        lotinf.F_LOT,lotinf.F_DIV,lotinf.F_PART,
        lotinf.F_PRCS,lotinf.F_NAME,lotinf.F_COUNT,
        lotinf.F_RELEASE_TIME,lotinf.F_CLOSE_TIME,
        lotinf.F_FACTOR,lotinf.F_DEL,lotinf.F_CRUE,
        lotinf.F_EDUE,lotinf.F_CRTM,lotinf.F_EDTM,
        hi.F_NAME as F_PLNT_NAME,
        partinf.F_NAME as partName,prcsinf.F_NAME as prcsName,
        emplinf.F_NAME as createName,emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from LOT_INF lotinf
        left join PART_INF partinf on lotinf.F_PART = partinf.F_PART
        left join HIERARCHY_INF hi on hi.F_HIER = partinf.F_PLNT
        left join PRCS_INF prcsinf on lotinf.F_PRCS = prcsinf.F_PRCS
        left join EMPL_INF emplinf on lotinf.F_CRUE = emplinf.F_EMPL
        left join EMPL_INF emplinf_u on lotinf.F_EDUE = emplinf_u.F_EMPL
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and lotinf.F_DEL = 0
                </when>
                <otherwise>
                    and lotinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and lotinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_LOT != null and F_LOT != ''">
                and lotinf.F_LOT = #{F_LOT}
            </if>
            <if test="F_DIV != null and F_DIV != ''">
                and lotinf.F_DIV = #{F_DIV}
            </if>
            <if test="F_PART != null and F_PART != ''">
                and lotinf.F_PART = #{F_PART}
            </if>
            <if test="F_PRCS != null and F_PRCS != ''">
                and lotinf.F_PRCS = #{F_PRCS}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and lotinf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_COUNT != null and F_COUNT != ''">
                and lotinf.F_COUNT = #{F_COUNT}
            </if>
            <if test="F_RELEASE_TIME != null">
                and lotinf.F_RELEASE_TIME = #{F_RELEASE_TIME}
            </if>
            <if test="F_CLOSE_TIME != null">
                and lotinf.F_CLOSE_TIME = #{F_CLOSE_TIME}
            </if>
            <if test="F_FACTOR != null and F_FACTOR != ''">
                and lotinf.F_FACTOR = #{F_FACTOR}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="partName != null and partName != ''">
                and partinf.F_NAME like concat('%', #{partName}, '%')
            </if>
            <if test="prcsName != null and prcsName != ''">
                and prcsinf.F_NAME like concat('%', #{prcsName}, '%')
            </if>
            <if test="status != null and status != 0">
                <choose>
                    <when test="status == 1">
                        and lotinf.F_RELEASE_TIME &lt; #{statusTime}
                        and lotinf.F_CLOSE_TIME &gt; #{statusTime}
                    </when>
                    <otherwise>
                        and (lotinf.F_RELEASE_TIME &gt; #{statusTime}
                        or lotinf.F_CLOSE_TIME &lt; #{statusTime})
                    </otherwise>
                </choose>
            </if>
            <if test="startTime != null">
                and lotinf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and lotinf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and lotinf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and lotinf.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(lotinf.F_LOT)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.LOT_INF_DTO">
        select
            <include refid="Base_Column_List"/>
            <include refid="baseSql"/>
        order by lotinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
