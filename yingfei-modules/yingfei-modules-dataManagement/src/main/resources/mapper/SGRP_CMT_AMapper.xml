<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.SGRP_CMT_AMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.SGRP_CMT_A">
            <result property="f_SGRP" column="F_SGRP" jdbcType="BIGINT"/>
            <result property="f_TEST" column="F_TEST" jdbcType="BIGINT"/>
            <result property="f_NOTE" column="F_NOTE" jdbcType="VARCHAR"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_SGRP,F_TEST,F_NOTE,
        F_DEL,F_CRUE,F_EDUE,
        F_CRTM,F_EDTM
    </sql>

    <delete id="deleteBySgtm">
        DELETE
        FROM SGRP_CMT_A
        WHERE F_SGRP IN
              (SELECT sia.F_SGRP
               FROM SGRP_INF_A sia
               WHERE sia.F_SGTM &gt; #{startTime}
                 AND sia.F_SGTM &lt; #{endTime})
    </delete>
</mapper>
