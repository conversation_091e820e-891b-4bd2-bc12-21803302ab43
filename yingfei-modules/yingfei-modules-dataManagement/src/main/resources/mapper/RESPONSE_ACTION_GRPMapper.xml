<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.RESPONSE_ACTION_GRPMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.RESPONSE_ACTION_GRP">
            <id property="f_RAGP" column="F_RAGP" jdbcType="BIGINT"/>
            <result property="f_DIV" column="F_DIV" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        responseactiongrp.F_RAGP,responseactiongrp.F_DIV,responseactiongrp.F_NAME,
        responseactiongrp.F_FACTOR,responseactiongrp.F_DEL,responseactiongrp.F_CRUE,
        responseactiongrp.F_EDUE,responseactiongrp.F_CRTM,responseactiongrp.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from RESPONSE_ACTION_GRP responseactiongrp
        left join EMPL_INF emplinf on emplinf.F_EMPL = responseactiongrp.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = responseactiongrp.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and responseactiongrp.F_DEL = 0
                </when>
                <otherwise>
                    and responseactiongrp.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and responseactiongrp.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and responseactiongrp.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and responseactiongrp.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and responseactiongrp.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and responseactiongrp.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and responseactiongrp.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>


    <select id="getTotal" resultType="java.lang.Long">
        select count(responseactiongrp.F_RAGP)
        <include refid="baseSql"/>
    </select>
    
    <select id="getList" resultType="com.yingfei.entity.dto.RESPONSE_ACTION_GRP_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by responseactiongrp.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
