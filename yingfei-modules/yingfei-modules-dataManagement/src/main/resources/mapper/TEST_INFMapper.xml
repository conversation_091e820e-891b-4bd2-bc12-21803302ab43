<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.TEST_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.TEST_INF">
            <id property="F_TEST" column="F_TEST" jdbcType="BIGINT"/>
            <result property="F_PLNT" column="F_PLNT" jdbcType="BIGINT"/>
            <result property="F_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="F_LONG_NAME" column="F_LONG_NAME" jdbcType="VARCHAR"/>
            <result property="F_IMAGE" column="F_IMAGE" jdbcType="VARCHAR"/>
            <result property="F_TYPE" column="F_TYPE" jdbcType="TINYINT"/>
            <result property="F_DFGP" column="F_DFGP" jdbcType="BIGINT"/>
            <result property="F_UNIT" column="F_UNIT" jdbcType="BIGINT"/>
            <result property="F_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="F_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="F_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="F_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="F_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="F_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        testinf.F_TEST,testinf.F_PLNT,testinf.F_NAME,
        testinf.F_LONG_NAME,testinf.F_IMAGE,testinf.F_TYPE,
        testinf.F_DFGP,testinf.F_UNIT,testinf.F_FACTOR,
        testinf.F_DEL,testinf.F_CRUE,testinf.F_EDUE,
        testinf.F_CRTM,testinf.F_EDTM,hi.F_NAME as planName,
        dg.F_NAME as dfgpName,testinf.F_LEVEL,
        emplinf.F_NAME as createName,emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from TEST_INF testinf
        left join HIERARCHY_INF hi on hi.F_HIER = testinf.F_PLNT
        left join DEF_GRP dg on dg.F_DFGP = testinf.F_DFGP
        left join EMPL_INF emplinf on testinf.F_CRUE = emplinf.F_EMPL
        left join EMPL_INF emplinf_u on testinf.F_EDUE = emplinf_u.F_EMPL
        left join TAG_LINK tal on tal.F_RESOURCE = testinf.F_TEST and tal.F_TYPE = 3
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and testinf.F_DEL = 0
                </when>
                <otherwise>
                    and testinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_TEST != null and F_TEST != ''">
                and testinf.F_TEST = #{F_TEST}
            </if>
            <if test="F_PLNT != null and F_PLNT != ''">
                and testinf.F_PLNT = #{F_PLNT}
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and testinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and testinf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_LONG_NAME != null and F_LONG_NAME != ''">
                and testinf.F_LONG_NAME like concat('%', #{F_LONG_NAME}, '%')
            </if>
            <if test="F_IMAGE != null and F_IMAGE != ''">
                and testinf.F_IMAGE = #{F_IMAGE}
            </if>
            <if test="F_TYPE != null and F_TYPE != ''">
                and testinf.F_TYPE = #{F_TYPE}
            </if>
            <if test="F_LEVEL != null and F_LEVEL != ''">
                and testinf.F_LEVEL = #{F_LEVEL}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="hierarchyInfIds != null and hierarchyInfIds.size() != 0">
                and testinf.F_PLNT in
                <foreach collection="hierarchyInfIds" item="hierarchyInfId" open="(" close=")" separator=",">
                    #{hierarchyInfId}
                </foreach>
            </if>
            <if test="tggpId != null and tggpId != ''">
                and tal.F_TGGP = #{tggpId}
            </if>
            <if test="tagIds != null and tagIds.size() != 0">
                and tal.F_TAG in
                <foreach collection="tagIds" item="tagId" open="(" close=")" separator=",">
                    #{tagId}
                </foreach>
            </if>
            <if test="startTime != null">
                and testinf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and testinf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and testinf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and testinf.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getList" resultType="com.yingfei.entity.dto.TEST_INF_DTO">
        select DISTINCT
            <include refid="Base_Column_List"/>
            <include refid="baseSql"/>
        order by testinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>

    <select id="getTotal" resultType="java.lang.Long">
        select count(DISTINCT testinf.F_TEST)
        <include refid="baseSql"/>
    </select>
</mapper>
