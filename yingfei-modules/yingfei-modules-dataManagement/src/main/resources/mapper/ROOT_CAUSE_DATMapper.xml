<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.ROOT_CAUSE_DATMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.ROOT_CAUSE_DAT">
            <id property="f_RTCS" column="F_RTCS" jdbcType="BIGINT"/>
            <result property="f_RCGP" column="F_RCGP" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_IMAGE" column="F_IMAGE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        rootcausedat.F_RTCS,rootcausedat.F_RCGP,rootcausedat.F_NAME,
        rootcausedat.F_FACTOR,rootcausedat.F_DEL,rootcausedat.F_CRUE,
        rootcausedat.F_EDUE,rootcausedat.F_CRTM,rootcausedat.F_EDTM,
        rootcausedat.F_IMAGE,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from ROOT_CAUSE_DAT rootcausedat
        left join EMPL_INF emplinf on emplinf.F_EMPL = rootcausedat.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = rootcausedat.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and rootcausedat.F_DEL = 0
                </when>
                <otherwise>
                    and rootcausedat.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and rootcausedat.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and rootcausedat.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_RCGP != null and F_RCGP != ''">
                and rootcausedat.F_RCGP = #{F_RCGP}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and rootcausedat.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and rootcausedat.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and rootcausedat.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and rootcausedat.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>
    
    <select id="getTotal" resultType="java.lang.Long">
        select count(rootcausedat.F_RTCS)
        <include refid="baseSql"/>
    </select>
    
    <select id="getList" resultType="com.yingfei.entity.dto.ROOT_CAUSE_DAT_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by rootcausedat.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
