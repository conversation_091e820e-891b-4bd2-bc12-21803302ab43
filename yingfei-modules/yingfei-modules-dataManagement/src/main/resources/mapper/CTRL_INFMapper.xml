<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.CTRL_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.CTRL_INF">
            <id property="f_CTRL" column="F_CTRL" jdbcType="BIGINT"/>
            <result property="f_PART" column="F_PART" jdbcType="BIGINT"/>
            <result property="f_TEST" column="F_TEST" jdbcType="BIGINT"/>
            <result property="f_PRCS" column="F_PRCS" jdbcType="BIGINT"/>
            <result property="f_EFTM" column="F_EFTM" jdbcType="TIMESTAMP"/>
            <result property="f_MEAN" column="F_MEAN" jdbcType="FLOAT"/>
            <result property="f_SP" column="F_SP" jdbcType="FLOAT"/>
            <result property="f_SPL" column="F_SPL" jdbcType="FLOAT"/>
            <result property="f_SW" column="F_SW" jdbcType="FLOAT"/>
            <result property="f_SWL" column="F_SWL" jdbcType="FLOAT"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_PSTP" column="F_PSTP" jdbcType="BIGINT"/>
            <result property="f_ARTP" column="F_ARTP" jdbcType="BIGINT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ctrlinf.F_CTRL,ctrlinf.F_PART,ctrlinf.F_TEST,
        ctrlinf.F_PRCS,ctrlinf.F_EFTM,ctrlinf.F_MEAN,
        ctrlinf.F_SP,ctrlinf.F_SPL,ctrlinf.F_SW,
        ctrlinf.F_SWL,ctrlinf.F_FACTOR,ctrlinf.F_PSTP,
        ctrlinf.F_ARTP,ctrlinf.F_DEL,ctrlinf.F_CRUE,
        ctrlinf.F_EDUE,ctrlinf.F_CRTM,ctrlinf.F_EDTM,
        ctrlinf.F_CHART_TYPE,ctrlinf.F_PTRV,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName,
        pai.F_NAME as partName,
        pri.F_NAME as prcsName,
        ti.F_NAME as testName,
        par.F_NAME as ptrvName
    </sql>

    <sql id="baseSql">
        from CTRL_INF ctrlinf
        left join PART_INF pai on pai.F_PART = ctrlinf.F_PART
        left join PRCS_INF pri on pri.F_PRCS = ctrlinf.F_PRCS
        left join TEST_INF ti on ti.F_TEST = ctrlinf.F_TEST
        left join PART_REV par on par.F_PTRV = ctrlinf.F_PTRV
        left join EMPL_INF emplinf on emplinf.F_EMPL = ctrlinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = ctrlinf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and ctrlinf.F_DEL = 0
                </when>
                <otherwise>
                    and ctrlinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_PART != null and F_PART != ''">
                and ctrlinf.F_PART = #{F_PART}
            </if>
            <if test="F_PRCS != null and F_PRCS != ''">
                and ctrlinf.F_PRCS = #{F_PRCS}
            </if>
            <if test="F_TEST != null and F_TEST != ''">
                and ctrlinf.F_TEST = #{F_TEST}
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and ctrlinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="hierarchyInfIds != null and hierarchyInfIds.size() != 0">
                and pai.F_PLNT in
                <foreach collection="hierarchyInfIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </sql>
    
    <select id="getTotal" resultType="java.lang.Long">
        select count(ctrlinf.F_CTRL)
        <include refid="baseSql"/>
    </select>
    
    <select id="getList" resultType="com.yingfei.entity.dto.CTRL_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by ctrlinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
