<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.BpmTaskAssignRuleMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.BPM_TASK_RULE">
            <id property="f_RULE" column="F_RULE" jdbcType="BIGINT"/>
            <result property="f_MODE" column="F_MODE" jdbcType="VARCHAR"/>
            <result property="f_PROCESS_DEFINITION" column="F_PROCESS_DEFINITION" jdbcType="VARCHAR"/>
            <result property="f_TASK_KEY" column="F_TASK_KEY" jdbcType="VARCHAR"/>
            <result property="f_TYPE" column="F_TYPE" jdbcType="TINYINT"/>
            <result property="f_OPTIONS" column="F_OPTIONS" jdbcType="VARCHAR"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_RULE,F_MODE,F_PROCESS_DEFINITION,
        F_TASK_KEY,F_TYPE,F_OPTIONS,
        F_DEL,F_CRUE,F_EDUE,
        F_CRTM,F_EDTM
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from BPM_TASK_RULE
        where  F_RULE = #{f_RULE,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from BPM_TASK_RULE
        where  F_RULE = #{f_RULE,jdbcType=BIGINT} 
    </delete>

    <insert id="insertSelective" keyColumn="F_RULE" keyProperty="f_RULE" parameterType="com.yingfei.entity.domain.BPM_TASK_RULE" useGeneratedKeys="true">
        insert into BPM_TASK_RULE
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="f_RULE != null">F_RULE,</if>
                <if test="f_MODE != null">F_MODE,</if>
                <if test="f_PROCESS_DEFINITION != null">F_PROCESS_DEFINITION,</if>
                <if test="f_TASK_KEY != null">F_TASK_KEY,</if>
                <if test="f_TYPE != null">F_TYPE,</if>
                <if test="f_OPTIONS != null">F_OPTIONS,</if>
                <if test="f_DEL != null">F_DEL,</if>
                <if test="f_CRUE != null">F_CRUE,</if>
                <if test="f_EDUE != null">F_EDUE,</if>
                <if test="f_CRTM != null">F_CRTM,</if>
                <if test="f_EDTM != null">F_EDTM,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="f_RULE != null">#{f_RULE,jdbcType=BIGINT},</if>
                <if test="f_MODE != null">#{f_MODE,jdbcType=VARCHAR},</if>
                <if test="f_PROCESS_DEFINITION != null">#{f_PROCESS_DEFINITION,jdbcType=VARCHAR},</if>
                <if test="f_TASK_KEY != null">#{f_TASK_KEY,jdbcType=VARCHAR},</if>
                <if test="f_TYPE != null">#{f_TYPE,jdbcType=TINYINT},</if>
                <if test="f_OPTIONS != null">#{f_OPTIONS,jdbcType=VARCHAR},</if>
                <if test="f_DEL != null">#{f_DEL,jdbcType=BIT},</if>
                <if test="f_CRUE != null">#{f_CRUE,jdbcType=BIGINT},</if>
                <if test="f_EDUE != null">#{f_EDUE,jdbcType=BIGINT},</if>
                <if test="f_CRTM != null">#{f_CRTM,jdbcType=TIMESTAMP},</if>
                <if test="f_EDTM != null">#{f_EDTM,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.yingfei.entity.domain.BPM_TASK_RULE">
        update BPM_TASK_RULE
        <set>
                <if test="f_MODE != null">
                    F_MODE = #{f_MODE,jdbcType=VARCHAR},
                </if>
                <if test="f_PROCESS_DEFINITION != null">
                    F_PROCESS_DEFINITION = #{f_PROCESS_DEFINITION,jdbcType=VARCHAR},
                </if>
                <if test="f_TASK_KEY != null">
                    F_TASK_KEY = #{f_TASK_KEY,jdbcType=VARCHAR},
                </if>
                <if test="f_TYPE != null">
                    F_TYPE = #{f_TYPE,jdbcType=TINYINT},
                </if>
                <if test="f_OPTIONS != null">
                    F_OPTIONS = #{f_OPTIONS,jdbcType=VARCHAR},
                </if>
                <if test="f_DEL != null">
                    F_DEL = #{f_DEL,jdbcType=BIT},
                </if>
                <if test="f_CRUE != null">
                    F_CRUE = #{f_CRUE,jdbcType=BIGINT},
                </if>
                <if test="f_EDUE != null">
                    F_EDUE = #{f_EDUE,jdbcType=BIGINT},
                </if>
                <if test="f_CRTM != null">
                    F_CRTM = #{f_CRTM,jdbcType=TIMESTAMP},
                </if>
                <if test="f_EDTM != null">
                    F_EDTM = #{f_EDTM,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   F_RULE = #{f_RULE,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.yingfei.entity.domain.BPM_TASK_RULE">
        update BPM_TASK_RULE
        set 
            F_MODE =  #{f_MODE,jdbcType=VARCHAR},
            F_PROCESS_DEFINITION =  #{f_PROCESS_DEFINITION,jdbcType=VARCHAR},
            F_TASK_KEY =  #{f_TASK_KEY,jdbcType=VARCHAR},
            F_TYPE =  #{f_TYPE,jdbcType=TINYINT},
            F_OPTIONS =  #{f_OPTIONS,jdbcType=VARCHAR},
            F_DEL =  #{f_DEL,jdbcType=BIT},
            F_CRUE =  #{f_CRUE,jdbcType=BIGINT},
            F_EDUE =  #{f_EDUE,jdbcType=BIGINT},
            F_CRTM =  #{f_CRTM,jdbcType=TIMESTAMP},
            F_EDTM =  #{f_EDTM,jdbcType=TIMESTAMP}
        where   F_RULE = #{f_RULE,jdbcType=BIGINT} 
    </update>
</mapper>
