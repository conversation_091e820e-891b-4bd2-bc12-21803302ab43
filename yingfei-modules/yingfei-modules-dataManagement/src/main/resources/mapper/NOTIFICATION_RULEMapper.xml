<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.NOTIFICATION_RULEMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.NOTIFICATION_RULE">
            <id property="f_NOTI" column="F_NOTI" jdbcType="BIGINT"/>
            <result property="f_DATA" column="F_DATA" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_ALR_TYPE" column="F_ALR_TYPE" jdbcType="TINYINT"/>
            <result property="f_ALR_DETAIL" column="F_ALR_DETAIL" jdbcType="VARCHAR"/>
            <result property="f_EMPL" column="F_EMPL" jdbcType="VARCHAR"/>
            <result property="f_NOTI_TYPE" column="F_NOTI_TYPE" jdbcType="TINYINT"/>
            <result property="f_STATUS" column="F_STATUS" jdbcType="TINYINT"/>
            <result property="f_PROCESS_DEFINITION" column="F_PROCESS_DEFINITION" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        nr.F_NOTI,nr.F_DATA,nr.F_CRUE,nr.F_NAME,
        nr.F_EDUE,nr.F_CRTM,nr.F_EDTM,
        nr.F_ALR_TYPE,nr.F_ALR_DETAIL,nr.F_EMPL,
        nr.F_NOTI_TYPE,nr.F_STATUS,nr.F_PROCESS_DEFINITION,
        nr.F_CAUSE_DATA,nr.F_ACTION_DATA,nr.F_EMPL_TYPE,
        nr.F_DESC,nr.F_PLNT
    </sql>

    <sql id="baseSql">
        from NOTIFICATION_RULE nr
        left join EMPL_INF emplinf on emplinf.F_EMPL = nr.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = nr.F_EDUE
        <where>
            1=1
            <if test="F_CRUE != null and F_CRUE != ''">
                and nr.F_CRUE = #{F_CRUE}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and nr.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and nr.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and nr.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and nr.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(nr.F_NOTI)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.NOTIFICATION_RULE_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by nr.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
