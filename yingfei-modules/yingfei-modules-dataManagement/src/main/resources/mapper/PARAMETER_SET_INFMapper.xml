<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.PARAMETER_SET_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.PARAMETER_SET_INF">
            <id property="f_PRST" column="F_PRST" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_DATA_SET" column="F_DATA_SET" jdbcType="VARCHAR"/>
            <result property="f_TIME_WINDOW_TYPE" column="F_TIME_WINDOW_TYPE" jdbcType="SMALLINT"/>
            <result property="f_DATERANGE_TYPE" column="F_DATERANGE_TYPE" jdbcType="SMALLINT"/>
            <result property="f_RANGE_INTERVAL" column="F_RANGE_INTERVAL" jdbcType="SMALLINT"/>
            <result property="f_START_DATE" column="F_START_DATE" jdbcType="TIMESTAMP"/>
            <result property="f_END_DATE" column="F_END_DATE" jdbcType="TIMESTAMP"/>
            <result property="f_MAX_ITEM" column="F_MAX_ITEM" jdbcType="SMALLINT"/>
            <result property="f_EXCLUDE_DISABLED_SGS" column="F_EXCLUDE_DISABLED_SGS" jdbcType="BIT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        parametersetinf.F_PRST,parametersetinf.F_NAME,parametersetinf.F_DATA_SET,
        parametersetinf.F_TIME_WINDOW_TYPE,
        parametersetinf.F_DATERANGE_TYPE,parametersetinf.F_RANGE_INTERVAL,parametersetinf.F_START_DATE,
        parametersetinf.F_END_DATE,parametersetinf.F_MAX_ITEM,parametersetinf.F_EXCLUDE_DISABLED_SGS,
        parametersetinf.F_DEL,parametersetinf.F_CRUE,parametersetinf.F_EDUE,
        parametersetinf.F_CRTM,parametersetinf.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from PARAMETER_SET_INF parametersetinf
        left join EMPL_INF emplinf on emplinf.F_EMPL = parametersetinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = parametersetinf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and parametersetinf.F_DEL = 0
                </when>
                <otherwise>
                    and parametersetinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and parametersetinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and parametersetinf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and parametersetinf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and parametersetinf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and parametersetinf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and parametersetinf.F_EDTM &lt;= #{edEndTime}
            </if>
            <if test="notIds != null and notIds.size() != 0">
                and parametersetinf.F_PRST not in
                <foreach collection="notIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="hierarchyInfIds != null and hierarchyInfIds.size() != 0">
                and (
                <foreach collection="hierarchyInfIds" item="hierarchyInfId" separator=" or ">
                    parametersetinf.F_DATA_SET like CONCAT('%', #{hierarchyInfId}, '%')
                </foreach>
                )
            </if>
        </where>
    </sql>


    <select id="getTotal" resultType="java.lang.Long">
        select count(parametersetinf.F_PRST)
        <include refid="baseSql"/>
    </select>
    
    <select id="getList" resultType="com.yingfei.entity.dto.PARAMETER_SET_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by parametersetinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
