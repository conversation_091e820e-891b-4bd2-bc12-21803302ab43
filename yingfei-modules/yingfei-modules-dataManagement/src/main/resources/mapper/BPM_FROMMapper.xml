<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.BpmFormMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.BPM_FROM">
            <id property="f_FROM" column="F_FROM" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_STATUS" column="F_STATUS" jdbcType="SMALLINT"/>
            <result property="f_CONF" column="F_CONF" jdbcType="VARCHAR"/>
            <result property="f_FIELDS" column="F_FIELDS" jdbcType="VARCHAR"/>
            <result property="f_REMAEK" column="F_REMAEK" jdbcType="VARCHAR"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_FROM,F_NAME,F_STATUS,
        F_CONF,F_FIELDS,F_REMAEK,
        F_DEL,F_CRUE,F_EDUE,
        F_CRTM,F_EDTM
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from BPM_FROM
        where  F_FROM = #{f_FROM,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from BPM_FROM
        where  F_FROM = #{f_FROM,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" keyColumn="F_FROM" keyProperty="f_FROM" parameterType="com.yingfei.entity.domain.BPM_FROM" useGeneratedKeys="true">
        insert into BPM_FROM
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="f_FROM != null">F_FROM,</if>
                <if test="f_NAME != null">F_NAME,</if>
                <if test="f_STATUS != null">F_STATUS,</if>
                <if test="f_CONF != null">F_CONF,</if>
                <if test="f_FIELDS != null">F_FIELDS,</if>
                <if test="f_REMAEK != null">F_REMAEK,</if>
                <if test="f_DEL != null">F_DEL,</if>
                <if test="f_CRUE != null">F_CRUE,</if>
                <if test="f_EDUE != null">F_EDUE,</if>
                <if test="f_CRTM != null">F_CRTM,</if>
                <if test="f_EDTM != null">F_EDTM,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="f_FROM != null">#{f_FROM,jdbcType=BIGINT},</if>
                <if test="f_NAME != null">#{f_NAME,jdbcType=VARCHAR},</if>
                <if test="f_STATUS != null">#{f_STATUS,jdbcType=SMALLINT},</if>
                <if test="f_CONF != null">#{f_CONF,jdbcType=VARCHAR},</if>
                <if test="f_FIELDS != null">#{f_FIELDS,jdbcType=VARCHAR},</if>
                <if test="f_REMAEK != null">#{f_REMAEK,jdbcType=VARCHAR},</if>
                <if test="f_DEL != null">#{f_DEL,jdbcType=BIT},</if>
                <if test="f_CRUE != null">#{f_CRUE,jdbcType=BIGINT},</if>
                <if test="f_EDUE != null">#{f_EDUE,jdbcType=BIGINT},</if>
                <if test="f_CRTM != null">#{f_CRTM,jdbcType=TIMESTAMP},</if>
                <if test="f_EDTM != null">#{f_EDTM,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.yingfei.entity.domain.BPM_FROM">
        update BPM_FROM
        <set>
                <if test="f_NAME != null">
                    F_NAME = #{f_NAME,jdbcType=VARCHAR},
                </if>
                <if test="f_STATUS != null">
                    F_STATUS = #{f_STATUS,jdbcType=SMALLINT},
                </if>
                <if test="f_CONF != null">
                    F_CONF = #{f_CONF,jdbcType=VARCHAR},
                </if>
                <if test="f_FIELDS != null">
                    F_FIELDS = #{f_FIELDS,jdbcType=VARCHAR},
                </if>
                <if test="f_REMAEK != null">
                    F_REMAEK = #{f_REMAEK,jdbcType=VARCHAR},
                </if>
                <if test="f_DEL != null">
                    F_DEL = #{f_DEL,jdbcType=BIT},
                </if>
                <if test="f_CRUE != null">
                    F_CRUE = #{f_CRUE,jdbcType=BIGINT},
                </if>
                <if test="f_EDUE != null">
                    F_EDUE = #{f_EDUE,jdbcType=BIGINT},
                </if>
                <if test="f_CRTM != null">
                    F_CRTM = #{f_CRTM,jdbcType=TIMESTAMP},
                </if>
                <if test="f_EDTM != null">
                    F_EDTM = #{f_EDTM,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   F_FROM = #{f_FROM,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.yingfei.entity.domain.BPM_FROM">
        update BPM_FROM
        set
            F_NAME =  #{f_NAME,jdbcType=VARCHAR},
            F_STATUS =  #{f_STATUS,jdbcType=SMALLINT},
            F_CONF =  #{f_CONF,jdbcType=VARCHAR},
            F_FIELDS =  #{f_FIELDS,jdbcType=VARCHAR},
            F_REMAEK =  #{f_REMAEK,jdbcType=VARCHAR},
            F_DEL =  #{f_DEL,jdbcType=BIT},
            F_CRUE =  #{f_CRUE,jdbcType=BIGINT},
            F_EDUE =  #{f_EDUE,jdbcType=BIGINT},
            F_CRTM =  #{f_CRTM,jdbcType=TIMESTAMP},
            F_EDTM =  #{f_EDTM,jdbcType=TIMESTAMP}
        where   F_FROM = #{f_FROM,jdbcType=BIGINT}
    </update>
</mapper>
