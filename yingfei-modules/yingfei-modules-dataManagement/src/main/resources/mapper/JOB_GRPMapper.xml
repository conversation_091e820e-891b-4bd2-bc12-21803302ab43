<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.JOB_GRPMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.JOB_GRP">
            <id property="f_JBGP" column="F_JBGP" jdbcType="BIGINT"/>
            <result property="f_DIV" column="F_DIV" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_TYPE" column="F_TYPE" jdbcType="TINYINT"/>
            <result property="f_HINT" column="F_HINT" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        jobgrp.F_JBGP,jobgrp.F_DIV,jobgrp.F_NAME,
        jobgrp.F_FACTOR,jobgrp.F_DEL,jobgrp.F_CRUE,
        jobgrp.F_EDUE,jobgrp.F_CRTM,jobgrp.F_EDTM,
        jobgrp.F_TYPE,jobgrp.F_HINT,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from JOB_GRP jobgrp
        left join EMPL_INF emplinf on emplinf.F_EMPL = jobgrp.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = jobgrp.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and jobgrp.F_DEL = 0
                </when>
                <otherwise>
                    and jobgrp.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and jobgrp.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and jobgrp.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(jobgrp.F_JBGP)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.JOB_GRP_DTO">
        select
            <include refid="Base_Column_List"/>
            <include refid="baseSql"/>
        order by jobgrp.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
