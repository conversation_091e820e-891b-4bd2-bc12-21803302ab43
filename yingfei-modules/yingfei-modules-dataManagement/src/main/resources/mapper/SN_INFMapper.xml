<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.SN_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.SN_INF">
            <id property="f_SN" column="F_SN" jdbcType="BIGINT"/>
            <result property="f_DIV" column="F_DIV" jdbcType="BIGINT"/>
            <result property="f_PART" column="F_PART" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        sninf.F_SN,sninf.F_DIV,sninf.F_PART,
        sninf.F_NAME,sninf.F_FACTOR,sninf.F_DEL,
        sninf.F_CRUE,sninf.F_EDUE,sninf.F_CRTM,
        sninf.F_EDTM,pi.F_NAME as partName,
        hi.F_NAME as F_PLNT_NAME,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from SN_INF sninf
        left join PART_INF pi on pi.F_PART = sninf.F_PART
        left join HIERARCHY_INF hi on hi.F_HIER = pi.F_PLNT
        left join EMPL_INF emplinf on emplinf.F_EMPL = sninf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = sninf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and sninf.F_DEL = 0
                </when>
                <otherwise>
                    and sninf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and sninf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and sninf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="partName != null and partName != ''">
                and pi.F_NAME like concat('%', #{partName}, '%')
            </if>
            <if test="startTime != null">
                and sninf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and sninf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and sninf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and sninf.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(sninf.F_SN)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.SN_INF_DTO">
        select
            <include refid="Base_Column_List"/>
            <include refid="baseSql"/>
        order by sninf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
