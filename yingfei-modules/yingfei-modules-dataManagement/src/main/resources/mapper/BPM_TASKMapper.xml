<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.BPM_TASKMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.BPM_TASK">
            <id property="f_TASK" column="F_TASK" jdbcType="BIGINT"/>
            <result property="f_ASSIGNEE_USER" column="F_ASSIGNEE_USER" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_TASK_NUM" column="F_TASK_NUM" jdbcType="VARCHAR"/>
            <result property="f_RESULT" column="F_RESULT" jdbcType="TINYINT"/>
            <result property="f_REASON" column="F_REASON" jdbcType="VARCHAR"/>
            <result property="f_END_TIME" column="F_END_TIME" jdbcType="TIMESTAMP"/>
            <result property="f_PROCESS_DEFINITION" column="F_PROCESS_DEFINITION" jdbcType="VARCHAR"/>
            <result property="f_PROCESS_INSTANCE" column="F_PROCESS_INSTANCE" jdbcType="VARCHAR"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_TASK,F_ASSIGNEE_USER,F_NAME,
        F_TASK_NUM,F_RESULT,F_REASON,
        F_END_TIME,F_PROCESS_DEFINITION,F_PROCESS_INSTANCE,
        F_DEL,F_CRUE,F_EDUE,
        F_CRTM,F_EDTM
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from BPM_TASK
        where  F_TASK = #{f_TASK,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from BPM_TASK
        where  F_TASK = #{f_TASK,jdbcType=BIGINT} 
    </delete>

    <insert id="insertSelective" keyColumn="F_TASK" keyProperty="f_TASK" parameterType="com.yingfei.entity.domain.BPM_TASK" useGeneratedKeys="true">
        insert into BPM_TASK
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="f_TASK != null">F_TASK,</if>
                <if test="f_ASSIGNEE_USER != null">F_ASSIGNEE_USER,</if>
                <if test="f_NAME != null">F_NAME,</if>
                <if test="f_TASK_NUM != null">F_TASK_NUM,</if>
                <if test="f_RESULT != null">F_RESULT,</if>
                <if test="f_REASON != null">F_REASON,</if>
                <if test="f_END_TIME != null">F_END_TIME,</if>
                <if test="f_PROCESS_DEFINITION != null">F_PROCESS_DEFINITION,</if>
                <if test="f_PROCESS_INSTANCE != null">F_PROCESS_INSTANCE,</if>
                <if test="f_DEL != null">F_DEL,</if>
                <if test="f_CRUE != null">F_CRUE,</if>
                <if test="f_EDUE != null">F_EDUE,</if>
                <if test="f_CRTM != null">F_CRTM,</if>
                <if test="f_EDTM != null">F_EDTM,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="f_TASK != null">#{f_TASK,jdbcType=BIGINT},</if>
                <if test="f_ASSIGNEE_USER != null">#{f_ASSIGNEE_USER,jdbcType=BIGINT},</if>
                <if test="f_NAME != null">#{f_NAME,jdbcType=VARCHAR},</if>
                <if test="f_TASK_NUM != null">#{f_TASK_NUM,jdbcType=VARCHAR},</if>
                <if test="f_RESULT != null">#{f_RESULT,jdbcType=TINYINT},</if>
                <if test="f_REASON != null">#{f_REASON,jdbcType=VARCHAR},</if>
                <if test="f_END_TIME != null">#{f_END_TIME,jdbcType=TIMESTAMP},</if>
                <if test="f_PROCESS_DEFINITION != null">#{f_PROCESS_DEFINITION,jdbcType=VARCHAR},</if>
                <if test="f_PROCESS_INSTANCE != null">#{f_PROCESS_INSTANCE,jdbcType=VARCHAR},</if>
                <if test="f_DEL != null">#{f_DEL,jdbcType=BIT},</if>
                <if test="f_CRUE != null">#{f_CRUE,jdbcType=BIGINT},</if>
                <if test="f_EDUE != null">#{f_EDUE,jdbcType=BIGINT},</if>
                <if test="f_CRTM != null">#{f_CRTM,jdbcType=TIMESTAMP},</if>
                <if test="f_EDTM != null">#{f_EDTM,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.yingfei.entity.domain.BPM_TASK">
        update BPM_TASK
        <set>
                <if test="f_ASSIGNEE_USER != null">
                    F_ASSIGNEE_USER = #{f_ASSIGNEE_USER,jdbcType=BIGINT},
                </if>
                <if test="f_NAME != null">
                    F_NAME = #{f_NAME,jdbcType=VARCHAR},
                </if>
                <if test="f_TASK_NUM != null">
                    F_TASK_NUM = #{f_TASK_NUM,jdbcType=VARCHAR},
                </if>
                <if test="f_RESULT != null">
                    F_RESULT = #{f_RESULT,jdbcType=TINYINT},
                </if>
                <if test="f_REASON != null">
                    F_REASON = #{f_REASON,jdbcType=VARCHAR},
                </if>
                <if test="f_END_TIME != null">
                    F_END_TIME = #{f_END_TIME,jdbcType=TIMESTAMP},
                </if>
                <if test="f_PROCESS_DEFINITION != null">
                    F_PROCESS_DEFINITION = #{f_PROCESS_DEFINITION,jdbcType=VARCHAR},
                </if>
                <if test="f_PROCESS_INSTANCE != null">
                    F_PROCESS_INSTANCE = #{f_PROCESS_INSTANCE,jdbcType=VARCHAR},
                </if>
                <if test="f_DEL != null">
                    F_DEL = #{f_DEL,jdbcType=BIT},
                </if>
                <if test="f_CRUE != null">
                    F_CRUE = #{f_CRUE,jdbcType=BIGINT},
                </if>
                <if test="f_EDUE != null">
                    F_EDUE = #{f_EDUE,jdbcType=BIGINT},
                </if>
                <if test="f_CRTM != null">
                    F_CRTM = #{f_CRTM,jdbcType=TIMESTAMP},
                </if>
                <if test="f_EDTM != null">
                    F_EDTM = #{f_EDTM,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   F_TASK = #{f_TASK,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.yingfei.entity.domain.BPM_TASK">
        update BPM_TASK
        set 
            F_ASSIGNEE_USER =  #{f_ASSIGNEE_USER,jdbcType=BIGINT},
            F_NAME =  #{f_NAME,jdbcType=VARCHAR},
            F_TASK_NUM =  #{f_TASK_NUM,jdbcType=VARCHAR},
            F_RESULT =  #{f_RESULT,jdbcType=TINYINT},
            F_REASON =  #{f_REASON,jdbcType=VARCHAR},
            F_END_TIME =  #{f_END_TIME,jdbcType=TIMESTAMP},
            F_PROCESS_DEFINITION =  #{f_PROCESS_DEFINITION,jdbcType=VARCHAR},
            F_PROCESS_INSTANCE =  #{f_PROCESS_INSTANCE,jdbcType=VARCHAR},
            F_DEL =  #{f_DEL,jdbcType=BIT},
            F_CRUE =  #{f_CRUE,jdbcType=BIGINT},
            F_EDUE =  #{f_EDUE,jdbcType=BIGINT},
            F_CRTM =  #{f_CRTM,jdbcType=TIMESTAMP},
            F_EDTM =  #{f_EDTM,jdbcType=TIMESTAMP}
        where   F_TASK = #{f_TASK,jdbcType=BIGINT} 
    </update>
</mapper>
