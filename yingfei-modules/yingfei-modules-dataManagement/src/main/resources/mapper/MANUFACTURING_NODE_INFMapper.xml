<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.MANUFACTURING_NODE_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.MANUFACTURING_NODE_INF">
            <id property="f_MFND" column="F_MFND" jdbcType="BIGINT"/>
            <id property="f_NAME" column="F_NAME" jdbcType="BIGINT"/>
            <result property="f_MFPS" column="F_MFPS" jdbcType="BIGINT"/>
            <result property="f_DATA" column="F_DATA" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_COUNT" column="F_COUNT" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_MFND,F_MFPS,F_DATA,
        F_CRUE,F_EDUE,F_CRTM,
        F_EDTM,F_COUNT,F_NAME
    </sql>
    <update id="addCount">
        update MANUFACTURING_NODE_INF set F_COUNT = F_COUNT + 1 where F_MFND = #{fMfnd}
    </update>

    <select id="getList" resultType="com.yingfei.entity.dto.MANUFACTURING_NODE_INF_DTO">
        select mni.*,mpi.F_NAME flowName,mpi.F_PLNT plnt from MANUFACTURING_NODE_INF mni
        left join MANUFACTURING_PROCESS_INF mpi on mpi.F_MFPS = mni.F_MFPS
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and mni.F_DEL = 0
                </when>
                <otherwise>
                    and mni.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and mni.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_MFPS != null and F_MFPS != ''">
                and mni.F_MFPS = #{F_MFPS}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and mni.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
        </where>
        order by mni.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
