<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.PART_TEST_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.PART_TEST_INF">
            <id property="f_PATI" column="F_PATI" jdbcType="BIGINT"/>
            <result property="f_PART" column="F_PART" jdbcType="BIGINT"/>
            <result property="f_TEST" column="F_TEST" jdbcType="BIGINT"/>
            <result property="f_IMAGE" column="F_IMAGE" jdbcType="VARCHAR"/>
            <result property="f_TYPE" column="F_TYPE" jdbcType="VARCHAR"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_TEXT" column="F_TEXT" jdbcType="VARCHAR"/>
            <result property="f_PTRV" column="F_PTRV" jdbcType="VARCHAR"/>
            <result property="f_PLAN" column="F_PLAN" jdbcType="VARCHAR"/>
            <result property="f_DATA" column="F_DATA" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        parttestinf.F_PATI,parttestinf.F_PART,parttestinf.F_TEST,
        parttestinf.F_IMAGE,parttestinf.F_DEL,parttestinf.F_CRUE,
        parttestinf.F_EDUE,parttestinf.F_CRTM,parttestinf.F_EDTM,
        parttestinf.F_TYPE,parttestinf.F_TEXT,parttestinf.F_PTRV,
        parttestinf.F_PLAN,parttestinf.F_DATA,
        pi.F_NAME as partName,ti.F_NAME as testName,
        hi.F_NAME as F_PLNT_NAME,ipi.F_NAME as planName,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from PART_TEST_INF parttestinf
        left join PART_INF pi on pi.F_PART = parttestinf.F_PART
        left join HIERARCHY_INF hi on hi.F_HIER = pi.F_PLNT
        left join INSPECTION_PLAN_INF ipi on ipi.F_PLAN = parttestinf.F_PLAN
        left join TEST_INF ti on ti.F_TEST = parttestinf.F_TEST
        left join EMPL_INF emplinf on emplinf.F_EMPL = parttestinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = parttestinf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and parttestinf.F_DEL = 0
                </when>
                <otherwise>
                    and parttestinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_PART != null and F_PART != ''">
                and parttestinf.F_PART = #{F_PART}
            </if>
            <if test="F_PTRV != null and F_PTRV != ''">
                and parttestinf.F_PTRV = #{F_PTRV}
            </if>
            <if test="F_TEST != null and F_TEST != ''">
                and parttestinf.F_TEST = #{F_TEST}
            </if>
            <if test="partName != null and partName != ''">
                and pi.F_NAME like concat('%', #{partName}, '%')
            </if>
            <if test="testName != null and testName != ''">
                and ti.F_NAME like concat('%', #{testName}, '%')
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and parttestinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and parttestinf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and parttestinf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and parttestinf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and parttestinf.F_EDTM &lt;= #{edEndTime}
            </if>
            <if test="hierarchyInfIds != null and hierarchyInfIds.size() != 0">
                and pi.F_PLNT in
                <foreach collection="hierarchyInfIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </sql>
    
    <select id="getTotal" resultType="java.lang.Long">
        select count(parttestinf.F_PATI)
        <include refid="baseSql"/>
    </select>
    
    <select id="getList" resultType="com.yingfei.entity.dto.PART_TEST_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by parttestinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
