<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.PRCS_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.PRCS_INF">
            <id property="f_PRCS" column="F_PRCS" jdbcType="BIGINT"/>
            <result property="F_PLNT" column="F_PLNT" jdbcType="BIGINT"/>
            <result property="f_PARENT_PRCS" column="F_PARENT_PRCS" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_LONG_NAME" column="F_LONG_NAME" jdbcType="VARCHAR"/>
            <result property="f_IMAGE" column="F_IMAGE" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_LEVEL_NUM" column="F_LEVEL_NUM" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        prcsinf.F_PRCS,prcsinf.F_PLNT,prcsinf.F_PARENT_PRCS,
        prcsinf.F_NAME,prcsinf.F_LONG_NAME,prcsinf.F_IMAGE,
        prcsinf.F_FACTOR,prcsinf.F_DEL,prcsinf.F_CRUE,
        prcsinf.F_EDUE,prcsinf.F_CRTM,prcsinf.F_EDTM,
        prcsinf.F_LEVEL_NUM,prcsinf_p.F_NAME as parentName,
        emplinf.F_NAME as createName,emplinf_u.F_NAME as updateName,
        hi.F_NAME as F_PLNT_NAME
    </sql>

    <sql id="baseSql">
        from PRCS_INF prcsinf
        left join PRCS_INF prcsinf_p on prcsinf_p.F_PRCS = prcsinf.F_PARENT_PRCS
        left join EMPL_INF emplinf on prcsinf.F_CRUE = emplinf.F_EMPL
        left join EMPL_INF emplinf_u on prcsinf.F_EDUE = emplinf_u.F_EMPL
        left join TAG_LINK tal on tal.F_RESOURCE = prcsinf.F_PRCS and tal.F_TYPE = 2
        left join HIERARCHY_INF hi on hi.F_HIER = prcsinf.F_PLNT
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and prcsinf.F_DEL = 0
                </when>
                <otherwise>
                    and prcsinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_NAME != null and F_NAME != ''">
                and prcsinf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="parentName != null and parentName != ''">
                and prcsinf_p.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="ids != null and ids.size() != 0">
                and prcsinf.F_PRCS in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="F_PRCS != null">
                and prcsinf.F_PRCS = #{F_PRCS}
            </if>
            <if test="F_LEVEL_NUM != null">
                and prcsinf.F_LEVEL_NUM &lt; #{F_LEVEL_NUM}
            </if>
            <if test="tggpId != null and tggpId != ''">
                and tal.F_TGGP = #{tggpId}
            </if>
            <if test="F_PLNT != null and F_PLNT != ''">
                and prcsinf.F_PLNT = #{F_PLNT}
            </if>
            <if test="tagIds != null and tagIds.size() != 0">
                and tal.F_TAG in
                <foreach collection="tagIds" item="tagId" open="(" close=")" separator=",">
                    #{tagId}
                </foreach>
            </if>
            <if test="startTime != null">
                and prcsinf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and prcsinf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and prcsinf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and prcsinf.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getList" resultType="com.yingfei.entity.dto.PRCS_INF_DTO">
        select DISTINCT
            <include refid="Base_Column_List"/>
            <include refid="baseSql"/>
        order by prcsinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>

    <select id="getTotal" resultType="java.lang.Long">
        select count(DISTINCT prcsinf.F_PRCS)
        <include refid="baseSql"/>
    </select>

    <update id="deleteEmplPrcs">
        update EMPL_PRCS_LINK set F_DEL = 1 where F_PRCS in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="getEmplPrcs" resultType="com.yingfei.entity.domain.EMPL_PRCS_LINK">
        select * from  EMPL_PRCS_LINK where F_DEL = 0 and F_PRCS in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <update id="emplPrcsRecovery">
        update EMPL_PRCS_LINK set F_DEL = 0 where F_PRCS = #{id}
    </update>

    <delete id="emplPrcsCover">
        delete from EMPL_PRCS_LINK where F_PRCS = #{id}
    </delete>
</mapper>
