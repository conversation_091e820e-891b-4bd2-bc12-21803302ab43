<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.ACT_RE_PROCDEFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.ACT_RE_PROCDEF">
            <id property="ID_" column="ID_" jdbcType="VARCHAR"/>
            <result property="REV_" column="REV_" jdbcType="INTEGER"/>
            <result property="CATEGORY_" column="CATEGORY_" jdbcType="VARCHAR"/>
            <result property="NAME_" column="NAME_" jdbcType="VARCHAR"/>
            <result property="KEY_" column="KEY_" jdbcType="VARCHAR"/>
            <result property="VERSION_" column="VERSION_" jdbcType="INTEGER"/>
            <result property="DEPLOYMENT_ID_" column="DEPLOYMENT_ID_" jdbcType="VARCHAR"/>
            <result property="RESOURCE_NAME_" column="RESOURCE_NAME_" jdbcType="VARCHAR"/>
            <result property="DGRM_RESOURCE_NAME_" column="DGRM_RESOURCE_NAME_" jdbcType="VARCHAR"/>
            <result property="HAS_START_FORM_KEY_" column="HAS_START_FORM_KEY_" jdbcType="TINYINT"/>
            <result property="SUSPENSION_STATE_" column="SUSPENSION_STATE_" jdbcType="TINYINT"/>
            <result property="TENANT_ID_" column="TENANT_ID_" jdbcType="VARCHAR"/>
            <result property="VERSION_TAG_" column="VERSION_TAG_" jdbcType="VARCHAR"/>
            <result property="HISTORY_TTL_" column="HISTORY_TTL_" jdbcType="INTEGER"/>
            <result property="STARTABLE_" column="STARTABLE_" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_,REV_,CATEGORY_,
        NAME_,KEY_,VERSION_,
        DEPLOYMENT_ID_,RESOURCE_NAME_,DGRM_RESOURCE_NAME_,
        HAS_START_FORM_KEY_,SUSPENSION_STATE_,TENANT_ID_,
        VERSION_TAG_,HISTORY_TTL_,STARTABLE_
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ACT_RE_PROCDEF
        where  ID_ = #{ID_,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from ACT_RE_PROCDEF
        where  ID_ = #{ID_,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="ID_" keyProperty="ID_" parameterType="com.yingfei.entity.domain.ACT_RE_PROCDEF" useGeneratedKeys="true">
        insert into ACT_RE_PROCDEF
        ( ID_,REV_,CATEGORY_
        ,NAME_,KEY_,VERSION_
        ,DEPLOYMENT_ID_,RESOURCE_NAME_,DGRM_RESOURCE_NAME_
        ,HAS_START_FORM_KEY_,SUSPENSION_STATE_,TENANT_ID_
        ,VERSION_TAG_,HISTORY_TTL_,STARTABLE_
        )
        values (#{ID_,jdbcType=VARCHAR},#{REV_,jdbcType=INTEGER},#{CATEGORY_,jdbcType=VARCHAR}
        ,#{NAME_,jdbcType=VARCHAR},#{KEY_,jdbcType=VARCHAR},#{VERSION_,jdbcType=INTEGER}
        ,#{DEPLOYMENT_ID_,jdbcType=VARCHAR},#{RESOURCE_NAME_,jdbcType=VARCHAR},#{DGRM_RESOURCE_NAME_,jdbcType=VARCHAR}
        ,#{HAS_START_FORM_KEY_,jdbcType=TINYINT},#{SUSPENSION_STATE_,jdbcType=TINYINT},#{TENANT_ID_,jdbcType=VARCHAR}
        ,#{VERSION_TAG_,jdbcType=VARCHAR},#{HISTORY_TTL_,jdbcType=INTEGER},#{STARTABLE_,jdbcType=BIT}
        )
    </insert>
    <insert id="insertSelective" keyColumn="ID_" keyProperty="ID_" parameterType="com.yingfei.entity.domain.ACT_RE_PROCDEF" useGeneratedKeys="true">
        insert into ACT_RE_PROCDEF
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="ID_ != null">ID_,</if>
                <if test="REV_ != null">REV_,</if>
                <if test="CATEGORY_ != null">CATEGORY_,</if>
                <if test="NAME_ != null">NAME_,</if>
                <if test="KEY_ != null">KEY_,</if>
                <if test="VERSION_ != null">VERSION_,</if>
                <if test="DEPLOYMENT_ID_ != null">DEPLOYMENT_ID_,</if>
                <if test="RESOURCE_NAME_ != null">RESOURCE_NAME_,</if>
                <if test="DGRM_RESOURCE_NAME_ != null">DGRM_RESOURCE_NAME_,</if>
                <if test="HAS_START_FORM_KEY_ != null">HAS_START_FORM_KEY_,</if>
                <if test="SUSPENSION_STATE_ != null">SUSPENSION_STATE_,</if>
                <if test="TENANT_ID_ != null">TENANT_ID_,</if>
                <if test="VERSION_TAG_ != null">VERSION_TAG_,</if>
                <if test="HISTORY_TTL_ != null">HISTORY_TTL_,</if>
                <if test="STARTABLE_ != null">STARTABLE_,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="ID_ != null">#{ID_,jdbcType=VARCHAR},</if>
                <if test="REV_ != null">#{REV_,jdbcType=INTEGER},</if>
                <if test="CATEGORY_ != null">#{CATEGORY_,jdbcType=VARCHAR},</if>
                <if test="NAME_ != null">#{NAME_,jdbcType=VARCHAR},</if>
                <if test="KEY_ != null">#{KEY_,jdbcType=VARCHAR},</if>
                <if test="VERSION_ != null">#{VERSION_,jdbcType=INTEGER},</if>
                <if test="DEPLOYMENT_ID_ != null">#{DEPLOYMENT_ID_,jdbcType=VARCHAR},</if>
                <if test="RESOURCE_NAME_ != null">#{RESOURCE_NAME_,jdbcType=VARCHAR},</if>
                <if test="DGRM_RESOURCE_NAME_ != null">#{DGRM_RESOURCE_NAME_,jdbcType=VARCHAR},</if>
                <if test="HAS_START_FORM_KEY_ != null">#{HAS_START_FORM_KEY_,jdbcType=TINYINT},</if>
                <if test="SUSPENSION_STATE_ != null">#{SUSPENSION_STATE_,jdbcType=TINYINT},</if>
                <if test="TENANT_ID_ != null">#{TENANT_ID_,jdbcType=VARCHAR},</if>
                <if test="VERSION_TAG_ != null">#{VERSION_TAG_,jdbcType=VARCHAR},</if>
                <if test="HISTORY_TTL_ != null">#{HISTORY_TTL_,jdbcType=INTEGER},</if>
                <if test="STARTABLE_ != null">#{STARTABLE_,jdbcType=BIT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.yingfei.entity.domain.ACT_RE_PROCDEF">
        update ACT_RE_PROCDEF
        <set>
                <if test="REV_ != null">
                    REV_ = #{REV_,jdbcType=INTEGER},
                </if>
                <if test="CATEGORY_ != null">
                    CATEGORY_ = #{CATEGORY_,jdbcType=VARCHAR},
                </if>
                <if test="NAME_ != null">
                    NAME_ = #{NAME_,jdbcType=VARCHAR},
                </if>
                <if test="KEY_ != null">
                    KEY_ = #{KEY_,jdbcType=VARCHAR},
                </if>
                <if test="VERSION_ != null">
                    VERSION_ = #{VERSION_,jdbcType=INTEGER},
                </if>
                <if test="DEPLOYMENT_ID_ != null">
                    DEPLOYMENT_ID_ = #{DEPLOYMENT_ID_,jdbcType=VARCHAR},
                </if>
                <if test="RESOURCE_NAME_ != null">
                    RESOURCE_NAME_ = #{RESOURCE_NAME_,jdbcType=VARCHAR},
                </if>
                <if test="DGRM_RESOURCE_NAME_ != null">
                    DGRM_RESOURCE_NAME_ = #{DGRM_RESOURCE_NAME_,jdbcType=VARCHAR},
                </if>
                <if test="HAS_START_FORM_KEY_ != null">
                    HAS_START_FORM_KEY_ = #{HAS_START_FORM_KEY_,jdbcType=TINYINT},
                </if>
                <if test="SUSPENSION_STATE_ != null">
                    SUSPENSION_STATE_ = #{SUSPENSION_STATE_,jdbcType=TINYINT},
                </if>
                <if test="TENANT_ID_ != null">
                    TENANT_ID_ = #{TENANT_ID_,jdbcType=VARCHAR},
                </if>
                <if test="VERSION_TAG_ != null">
                    VERSION_TAG_ = #{VERSION_TAG_,jdbcType=VARCHAR},
                </if>
                <if test="HISTORY_TTL_ != null">
                    HISTORY_TTL_ = #{HISTORY_TTL_,jdbcType=INTEGER},
                </if>
                <if test="STARTABLE_ != null">
                    STARTABLE_ = #{STARTABLE_,jdbcType=BIT},
                </if>
        </set>
        where   ID_ = #{ID_,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.yingfei.entity.domain.ACT_RE_PROCDEF">
        update ACT_RE_PROCDEF
        set 
            REV_ =  #{REV_,jdbcType=INTEGER},
            CATEGORY_ =  #{CATEGORY_,jdbcType=VARCHAR},
            NAME_ =  #{NAME_,jdbcType=VARCHAR},
            KEY_ =  #{KEY_,jdbcType=VARCHAR},
            VERSION_ =  #{VERSION_,jdbcType=INTEGER},
            DEPLOYMENT_ID_ =  #{DEPLOYMENT_ID_,jdbcType=VARCHAR},
            RESOURCE_NAME_ =  #{RESOURCE_NAME_,jdbcType=VARCHAR},
            DGRM_RESOURCE_NAME_ =  #{DGRM_RESOURCE_NAME_,jdbcType=VARCHAR},
            HAS_START_FORM_KEY_ =  #{HAS_START_FORM_KEY_,jdbcType=TINYINT},
            SUSPENSION_STATE_ =  #{SUSPENSION_STATE_,jdbcType=TINYINT},
            TENANT_ID_ =  #{TENANT_ID_,jdbcType=VARCHAR},
            VERSION_TAG_ =  #{VERSION_TAG_,jdbcType=VARCHAR},
            HISTORY_TTL_ =  #{HISTORY_TTL_,jdbcType=INTEGER},
            STARTABLE_ =  #{STARTABLE_,jdbcType=BIT}
        where   ID_ = #{ID_,jdbcType=VARCHAR} 
    </update>
</mapper>
