<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.PART_REVMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.PART_REV">
            <id property="f_PTRV" column="F_PTRV" jdbcType="BIGINT"/>
            <result property="f_PART" column="F_PART" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_STTM" column="F_STTM" jdbcType="TIMESTAMP"/>
            <result property="f_FNTM" column="F_FNTM" jdbcType="TIMESTAMP"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_PTRV,F_PART,F_NAME,
        F_STTM,F_FNTM,F_DEL,
        F_CRUE,F_EDUE,F_CRTM,
        F_EDTM
    </sql>
</mapper>
