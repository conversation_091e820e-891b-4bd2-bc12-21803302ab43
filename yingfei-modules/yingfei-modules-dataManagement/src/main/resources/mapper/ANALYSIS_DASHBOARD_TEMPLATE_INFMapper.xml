<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.ANALYSIS_DASHBOARD_TEMPLATE_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.ANALYSIS_DASHBOARD_TEMPLATE_INF">
            <id property="f_ADTI" column="F_ADTI" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_TYPE" column="F_TYPE" jdbcType="TINYINT"/>
            <result property="f_DATA" column="F_DATA" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_DESC" column="F_DESC" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        adti.F_ADTI,adti.F_NAME,adti.F_TYPE,
        adti.F_DATA,adti.F_CRUE,adti.F_EDUE,
        adti.F_CRTM,adti.F_EDTM,adti.F_DESC,
        adti.F_CONFIG,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from ANALYSIS_DASHBOARD_TEMPLATE_INF adti
        left join EMPL_INF emplinf on emplinf.F_EMPL = adti.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = adti.F_EDUE
        <where>
            1=1
            <if test="F_NAME != null and F_NAME != ''">
                and adti.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_TYPE != null">
                and adti.F_TYPE = #{F_TYPE}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(adti.F_ADTI)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.ANALYSIS_DASHBOARD_TEMPLATE_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by adti.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
