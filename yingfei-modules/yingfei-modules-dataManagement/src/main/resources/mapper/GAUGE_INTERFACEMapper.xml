<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.GAUGE_INTERFACEMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.GAUGE_INTERFACE">
            <id property="f_GAIN" column="F_GAIN" jdbcType="BIGINT"/>
            <result property="f_INIT_DATA" column="F_INIT_DATA" jdbcType="VARCHAR"/>
            <result property="f_DELAY" column="F_DELAY" jdbcType="INTEGER"/>
            <result property="f_INIT_STR" column="F_INIT_STR" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        gaugeinterface.F_GAIN,gaugeinterface.F_INIT_DATA,gaugeinterface.F_DELAY,
        gaugeinterface.F_INIT_STR,gaugeinterface.F_CRUE,gaugeinterface.F_EDUE,
        gaugeinterface.F_CRTM,gaugeinterface.F_EDTM,gaugeinterface.F_NAME
    </sql>

    <sql id="baseSql">
        from GAUGE_INTERFACE gaugeinterface
        left join EMPL_INF emplinf on emplinf.F_EMPL = gaugeinterface.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = gaugeinterface.F_EDUE
        <where>
            1=1
            <if test="F_CRUE != null and F_CRUE != ''">
                and gaugeinterface.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and gaugeinterface.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and gaugeinterface.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and gaugeinterface.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and gaugeinterface.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and gaugeinterface.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <select id="getList" resultType="com.yingfei.entity.dto.GAUGE_INTERFACE_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by gaugeinterface.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
