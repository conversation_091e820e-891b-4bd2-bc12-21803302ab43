<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.SGRP_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.dto.SubgroupDataDTO">
            <id property="f_SGRP" column="F_SGRP" jdbcType="BIGINT"/>
            <result property="f_MFPS" column="F_MFPS" jdbcType="BIGINT"/>
            <result property="f_MFND" column="F_MFND" jdbcType="BIGINT"/>
            <result property="f_PART" column="F_PART" jdbcType="BIGINT"/>
            <result property="f_PRCS" column="F_PRCS" jdbcType="BIGINT"/>
            <result property="f_REV" column="F_REV" jdbcType="BIGINT"/>
            <result property="f_LOT" column="F_LOT" jdbcType="BIGINT"/>
            <result property="f_JOB" column="F_JOB" jdbcType="BIGINT"/>
            <result property="f_SHIFT" column="F_SHIFT" jdbcType="BIGINT"/>
            <result property="f_INSPECTION_TYPE" column="F_INSPECTION_TYPE" jdbcType="BIGINT"/>
            <result property="f_SGSZ" column="F_SGSZ" jdbcType="SMALLINT"/>
            <result property="f_SGTM" column="F_SGTM" jdbcType="TIMESTAMP"/>
            <result property="f_FLAG" column="F_FLAG" jdbcType="BIT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="partName" column="partName" jdbcType="VARCHAR"/>
            <result property="testName" column="testName" jdbcType="VARCHAR"/>
            <result property="prcsName" column="prcsName" jdbcType="VARCHAR"/>
            <result property="ptrvName" column="ptrvName" jdbcType="VARCHAR"/>
            <result property="lotName" column="lotName" jdbcType="VARCHAR"/>
            <result property="shiftName" column="shiftName" jdbcType="VARCHAR"/>
            <result property="jobName" column="jobName" jdbcType="VARCHAR"/>
            <result property="instName" column="instName" jdbcType="VARCHAR"/>
            <result property="f_PLNT" column="F_PLNT" jdbcType="BIGINT"/>
            <result property="f_SAMPLE_ID" column="F_SAMPLE_ID" jdbcType="VARCHAR"/>
            <result property="f_FINISH_STATUS" column="F_FINISH_STATUS" jdbcType="INTEGER"/>
            <result property="f_TEST" column="F_TEST" jdbcType="BIGINT"/>
            <collection property="sgrpValDtoList" ofType="com.yingfei.entity.dto.SGRP_VAL_DTO">
                <result property="f_SGRP" column="testF_SGRP" jdbcType="BIGINT"/>
                <result property="f_TEST" column="testF_TEST" jdbcType="BIGINT"/>
                <result property="f_DATA" column="testF_DATA" jdbcType="VARCHAR"/>
                <result property="testName" column="F_TESTName" jdbcType="VARCHAR"/>
                <result property="f_SGSZ" column="testF_SGSZ" jdbcType="INTEGER"/>
                <result property="f_SBSZ" column="testF_SBSZ" jdbcType="SMALLINT"/>
                <result property="testType" column="testType" jdbcType="VARCHAR"/>
            </collection>
    </resultMap>

    <sql id="Base_Column_List">
        si.F_SGRP,si.F_MFPS,si.F_MFND,
        si.F_PART,si.F_PRCS,si.F_REV,
        si.F_LOT,si.F_JOB,si.F_SHIFT,
        si.F_SGSZ,si.F_SGTM,si.F_FLAG,
        si.F_DEL,si.F_CRUE,si.F_EDUE,
        si.F_FINISH_STATUS,si.F_INSP_PLAN,
        si.F_SAMPLE_ID,si.F_PLNT,
        si.F_CRTM,si.F_EDTM,
        si.F_INSPECTION_TYPE,
        sv.F_TEST,
        pai.F_NAME as partName,
        pri.F_NAME as prcsName,
        pre.F_NAME as ptrvName,
        li.F_NAME as lotName,
        ji.F_NAME as jobName,
        shd.F_NAME as shiftName,
        inst.F_NAME as instName

    </sql>

    <sql id="Filter_Column_List">
        si.F_SGRP,si.F_MFPS,si.F_MFND,
        si.F_PART,si.F_PRCS,si.F_REV,
        si.F_LOT,si.F_JOB,si.F_SHIFT,
        si.F_SGSZ,si.F_SGTM,si.F_FLAG,
        si.F_DEL,si.F_CRUE,si.F_EDUE,
        si.F_CRTM,si.F_EDTM,sv.F_TEST,
        si.F_PLNT,sv.F_DATA as testData
    </sql>

    <sql id="baseSql">
        from SGRP_INF si
        left join PART_INF pai on pai.F_PART=si.F_PART
        left join PRCS_INF pri on pri.F_PRCS=si.F_PRCS
        left join LOT_INF li on li.F_LOT = si.F_LOT
        left join JOB_DAT ji on ji.F_JOB = si.F_JOB
        left join SHIFT_DAT shd on shd.F_SHIFT = si.F_SHIFT
        left join PART_REV pre on pre.F_PTRV = si.F_REV
        left join SGRP_VAL sv on sv.F_SGRP = si.F_SGRP
        left join INSPECTION_TYPE_DAT inst on inst.F_ID = si.F_SGRP
        <if test="isTemp == 1">
            LEFT JOIN TEMP_TABLE_INF tti ON tti.F_ID = si.F_SGRP
        </if>
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and si.F_DEL = 0
                </when>
                <otherwise>
                    and si.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <choose>
                <when test="F_FLAG == null">
                    and si.F_FLAG = 0
                </when>
                <when test="F_FLAG == 1">
                    and si.F_FLAG = 1
                </when>
                <when test="F_FLAG == 2">
                    and (si.F_FLAG = 0 or si.F_FLAG = 1)
                </when>
                <otherwise>
                    and si.F_FLAG = #{F_FLAG}
                </otherwise>
            </choose>
            <if test="F_SGRP != null and F_SGRP !=''">
                and si.F_SGRP = #{F_SGRP}
            </if>
            <if test="F_PART != null and F_PART !=''">
                and si.F_PART = #{F_PART}
            </if>
            <if test="F_PRCS != null and F_PRCS !=''">
                and si.F_PRCS = #{F_PRCS}
            </if>
            <if test="F_TEST != null and F_TEST !=''">
                and sv.F_TEST = #{F_TEST}
            </if>
            <if test="F_FINISH_STATUS != null">
                and si.F_FINISH_STATUS = #{F_FINISH_STATUS}
            </if>
            <if test="F_INSP_PLAN != null and F_INSP_PLAN != ''">
                and si.F_INSP_PLAN = #{F_INSP_PLAN}
            </if>
            <if test="partList != null and partList.size() != 0">
                <choose>
                    <when test="partList.size &lt;= 1000">
                        and si.F_PART in
                        <foreach collection="partList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_PART in
                        <foreach collection="partList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_PART in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="ptrvList != null and ptrvList.size() != 0">
                <choose>
                    <when test="ptrvList.size &lt;= 1000">
                        and si.F_REV in
                        <foreach collection="ptrvList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_REV in
                        <foreach collection="ptrvList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_REV in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="prcsList != null and prcsList.size() != 0">
                <choose>
                    <when test="prcsList.size &lt;= 1000">
                        and si.F_PRCS in
                        <foreach collection="prcsList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_PRCS in
                        <foreach collection="prcsList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_PRCS in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="testList != null and testList.size() != 0">
                <choose>
                    <when test="testList.size &lt;= 1000">
                        and sv.F_TEST in
                        <foreach collection="testList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (sv.F_TEST in
                        <foreach collection="testList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR sv.F_TEST in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="shiftList != null and shiftList.size() != 0">
                <choose>
                    <when test="shiftList.size &lt;= 1000">
                        and si.F_SHIFT in
                        <foreach collection="shiftList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_SHIFT in
                        <foreach collection="shiftList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_SHIFT in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="lotList != null and lotList.size() != 0">
                <choose>
                    <when test="lotList.size &lt;= 1000">
                        and si.F_LOT in
                        <foreach collection="lotList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_LOT in
                        <foreach collection="lotList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_LOT in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="jobList != null and jobList.size() != 0">
                <choose>
                    <when test="jobList.size &lt;= 1000">
                        and si.F_JOB in
                        <foreach collection="jobList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_JOB in
                        <foreach collection="jobList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_JOB in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <choose>
                <when test="isTemp == 1">
                    and tti.F_IDENTIFY = #{tempIdentify}
                </when>
                <otherwise>
                    <if test="sgrpList != null and sgrpList.size() != 0">
                        and si.F_SGRP in
                        <foreach collection="sgrpList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="planIds != null and planIds.size() != 0">
                and si.F_INSP_PLAN in
                <foreach collection="planIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="sampleIdList != null and sampleIdList.size() != 0">
                and si.F_SAMPLE_ID in
                <foreach collection="sampleIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="F_CRUE != null">
                and si.F_CRUE = #{F_CRUE}
            </if>
            <if test="startDate != null">
                and si.F_SGTM &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and si.F_SGTM &lt;= #{endDate}
            </if>
            <if test="startTime != null">
                and si.F_SGTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and si.F_SGTM &lt;= #{endTime}
            </if>
        </where>
    </sql>

    <sql id="sgrpInfSql">
        from SGRP_INF si
        left join PART_INF pai on pai.F_PART=si.F_PART
        left join PRCS_INF pri on pri.F_PRCS=si.F_PRCS
        left join LOT_INF li on li.F_LOT = si.F_LOT
        left join JOB_DAT ji on ji.F_JOB = si.F_JOB
        left join SHIFT_DAT shd on shd.F_SHIFT = si.F_SHIFT
        left join PART_REV pre on pre.F_PTRV = si.F_REV
        left join SGRP_VAL sv on sv.F_SGRP = si.F_SGRP
        <if test="isTemp == 1">
            LEFT JOIN TEMP_TABLE_INF tti ON tti.F_ID = si.F_SGRP
        </if>
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and si.F_DEL = 0
                </when>
                <otherwise>
                    and si.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <choose>
                <when test="F_FLAG == null">
                    and si.F_FLAG = 0
                </when>
                <when test="F_FLAG == 1">
                    and si.F_FLAG = 1
                </when>
                <when test="F_FLAG == 2">
                    and (si.F_FLAG = 0 or si.F_FLAG = 1)
                </when>
                <otherwise>
                    and si.F_FLAG = #{F_FLAG}
                </otherwise>
            </choose>
            <if test="F_PART != null and F_PART != ''">
                and si.F_PART = #{F_PART}
            </if>
            <if test="F_PRCS != null and F_PRCS != ''">
                and si.F_PRCS = #{F_PRCS}
            </if>
            <if test="F_LOT != null and F_LOT != ''">
                and si.F_LOT = #{F_LOT}
            </if>
            <if test="F_JOB != null and F_JOB != ''">
                and si.F_JOB = #{F_JOB}
            </if>
            <if test="F_SHIFT != null and F_SHIFT != ''">
                and si.F_SHIFT = #{F_SHIFT}
            </if>
            <if test="startDate != null">
                and si.F_SGTM &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and si.F_SGTM &lt;= #{endDate}
            </if>
            <if test="startTime != null">
                and si.F_SGTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and si.F_SGTM &lt;= #{endTime}
            </if>
            <if test="F_FINISH_STATUS != null">
                and si.F_FINISH_STATUS = #{F_FINISH_STATUS}
            </if>
            <if test="F_INSP_PLAN != null and F_INSP_PLAN != ''">
                and si.F_INSP_PLAN = #{F_INSP_PLAN}
            </if>
            <if test="partList != null and partList.size() != 0">
                <choose>
                    <when test="partList.size &lt;= 1000">
                        and si.F_PART in
                        <foreach collection="partList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_PART in
                        <foreach collection="partList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_PART in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="ptrvList != null and ptrvList.size() != 0">
                <choose>
                    <when test="ptrvList.size &lt;= 1000">
                        and si.F_REV in
                        <foreach collection="ptrvList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_REV in
                        <foreach collection="ptrvList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_REV in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="prcsList != null and prcsList.size() != 0">
                <choose>
                    <when test="prcsList.size &lt;= 1000">
                        and si.F_PRCS in
                        <foreach collection="prcsList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_PRCS in
                        <foreach collection="prcsList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_PRCS in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="testList != null and testList.size() != 0">
                <choose>
                    <when test="testList.size &lt;= 1000">
                        and sv.F_TEST in
                        <foreach collection="testList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (sv.F_TEST in
                        <foreach collection="testList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR sv.F_TEST in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="shiftList != null and shiftList.size() != 0">
                <choose>
                    <when test="shiftList.size &lt;= 1000">
                        and si.F_SHIFT in
                        <foreach collection="shiftList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_SHIFT in
                        <foreach collection="shiftList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_SHIFT in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="lotList != null and lotList.size() != 0">
                <choose>
                    <when test="lotList.size &lt;= 1000">
                        and si.F_LOT in
                        <foreach collection="lotList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and (si.F_LOT in
                        <foreach collection="lotList" item="id" open="(" close=")" index="index">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 1000 == 999">) OR si.F_LOT in (</when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{id}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <choose>
                <when test="isTemp == 1">
                    and tti.F_IDENTIFY = #{tempIdentify}
                </when>
                <otherwise>
                    <if test="sgrpList != null and sgrpList.size() != 0">
                        and si.F_SGRP in
                        <foreach collection="sgrpList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
        </where>
    </sql>

    <select id="getFilterList" resultType="com.yingfei.entity.dto.SubgroupDataDTO">
        select
            <include refid="Filter_Column_List"/>
            <include refid="baseSql"/>
    </select>

    <select id="getTopOne" resultType="com.yingfei.entity.dto.SubgroupDataDTO">
        select
        <if test="dbType == 1">
            top 1
        </if>
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by si.F_SGTM desc
        <choose>
            <when test="dbType == 2">
                FETCH FIRST 1 ROW ONLY;
            </when>
            <when test="dbType == 3">
                LIMIT 1
            </when>
        </choose>
    </select>

    <select id="getSubgroupDataDTOList" resultMap="BaseResultMap">
        select
        <if test="dbType == 1">
            top (#{totalNum})
        </if>
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by si.F_SGTM desc,si.F_SGRP DESC
        <choose>
            <when test="dbType == 2">
                FETCH FIRST #{totalNum} ROW ONLY;
            </when>
            <when test="dbType == 3">
                LIMIT #{totalNum}
            </when>
        </choose>
    </select>

    <select id="getViewDataSubgroupDataDTOList"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by si.F_SGTM desc,si.F_SGRP DESC
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>

    <select id="getViewDataTotal" resultType="java.lang.Long">
        select count(si.F_SGRP)
        <include refid="baseSql"/>
    </select>

    <select id="getSgrpInfTotal" resultType="java.lang.Long">
        select count(si.F_SGRP)
        <include refid="sgrpInfSql"/>
    </select>

    <select id="getSgrpInfList" resultType="com.yingfei.entity.dto.SubgroupDataDTO">
        select
            si.F_SGRP,si.F_MFPS,si.F_MFND,
            si.F_PART,si.F_PRCS,si.F_REV,
            si.F_LOT,si.F_JOB,si.F_SHIFT,
            si.F_SGSZ,si.F_SGTM,si.F_FLAG,
            si.F_DEL,si.F_CRUE,si.F_EDUE,
            si.F_FINISH_STATUS,si.F_INSP_PLAN,
            si.F_SAMPLE_ID,
            si.F_CRTM,si.F_EDTM,pai.F_NAME as partName,
            pri.F_NAME as prcsName
        <include refid="sgrpInfSql"/>
        order by si.F_SGTM desc,si.F_SGRP DESC
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>

    <select id="getSubgroupIdList" resultType="java.lang.String">
        select top (#{totalNum})
            si.F_SGRP
        <include refid="baseSql"/>
        order by si.F_SGTM desc,si.F_SGRP DESC
    </select>

    <select id="getSgrpValDtoList" resultType="com.yingfei.entity.dto.SGRP_VAL_DTO">
        SELECT
            sv.F_SGRP,
            sv.F_TEST,
            sv.F_DATA,
            sv.F_SGSZ,
            sv.F_SBSZ,
            ti.F_TYPE as testType,
            ti.F_NAME as testName
        <include refid="sgrpValSelectSql"/>
    </select>

    <sql id="sgrpValSelectSql">
        FROM SGRP_VAL sv
        LEFT JOIN TEST_INF ti ON ti.F_TEST = sv.F_TEST
        WHERE
        1=1
        <if test="F_TEST != null and F_TEST !=''">
            and sv.F_TEST = #{F_TEST}
        </if>
        <if test="testType == 1">
            and ti.F_TYPE = 1
        </if>
        <if test="testList != null and testList.size() != 0">
            <choose>
                <when test="testList.size &lt;= 1000">
                    and sv.F_TEST in
                    <foreach collection="testList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    and (sv.F_TEST in
                    <foreach collection="testList" item="id" open="(" close=")" index="index">
                        <if test="index != 0">
                            <choose>
                                <when test="index % 1000 == 999">) OR sv.F_TEST in (</when>
                                <otherwise>,</otherwise>
                            </choose>
                        </if>
                        #{id}
                    </foreach>
                    )
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="sgrpList != null and sgrpList.size() != 0">
                and sv.F_SGRP in
                <foreach collection="sgrpList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                AND sv.F_SGRP IN (
                SELECT
                si.F_SGRP
                <include refid="baseSql"/>
                )
            </otherwise>
        </choose>
    </sql>

    <select id="getSgrpDscDtoList" resultType="com.yingfei.entity.dto.SGRP_DSC_DTO">
        SELECT
            sd.*,dd.F_NAME as descName,dg.F_NAME as dsgpName
        FROM SGRP_DSC sd
        left join DESC_DAT dd on dd.F_DESC = sd.F_DESC
        left join DESC_GRP dg on dg.F_DSGP = sd.F_DSGP
        WHERE
        1=1
        <choose>
            <when test="sgrpList != null and sgrpList.size() != 0">
                and sd.F_SGRP in
                <foreach collection="sgrpList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                AND sd.F_SGRP IN (
                SELECT
                si.F_SGRP
                <include refid="baseSql"/>
                )
            </otherwise>
        </choose>
    </select>

    <select id="getLotList" resultType="com.yingfei.entity.dto.LOT_INF_DTO">
        SELECT
        li.F_LOT,li.F_NAME
        FROM LOT_INF li
        WHERE
        1=1
        AND li.F_LOT IN (
        SELECT
        si.F_LOT
        <include refid="baseSql"/>
        GROUP BY si.F_LOT
        )
    </select>

    <select id="getJobList" resultType="com.yingfei.entity.dto.JOB_DAT_DTO">
        SELECT
        jd.F_JOB,jd.F_NAME
        FROM JOB_DAT jd
        WHERE
        1=1
        AND jd.F_JOB IN (
        SELECT
        si.F_JOB
        <include refid="baseSql"/>
        GROUP BY si.F_JOB
        )
    </select>

    <select id="getShiftList" resultType="com.yingfei.entity.dto.SHIFT_DAT_DTO">
        SELECT
        sd.F_SHIFT,sd.F_NAME
        FROM SHIFT_DAT sd
        WHERE
        1=1
        AND sd.F_SHIFT IN (
        SELECT
        si.F_SHIFT
        <include refid="baseSql"/>
        GROUP BY si.F_SHIFT
        )
    </select>

    <select id="getPtrvtList" resultType="com.yingfei.entity.dto.PART_REV_DTO">
        SELECT
        pr.F_PTRV,pr.F_NAME
        FROM PART_REV pr
        WHERE
        1=1
        AND pr.F_PTRV IN (
        SELECT
        si.F_REV
        <include refid="baseSql"/>
        GROUP BY si.F_REV
        )
    </select>

    <select id="getSnList" resultType="com.yingfei.entity.dto.SN_INF_DTO">
        SELECT
        sni.*
        FROM SN_INF sni
        WHERE
        1=1
        AND sni.F_PART IN (
        SELECT
        si.F_PART
        <include refid="baseSql"/>
        GROUP BY si.F_PART
        )
    </select>

    <select id="getEvntInfList" resultType="com.yingfei.entity.domain.EVNT_INF">
        SELECT
        ei.*
        FROM EVNT_INF ei
        WHERE
        1=1
        AND ei.F_SGRP IN (
        SELECT
        si.F_SGRP
        <include refid="baseSql"/>
        )
    </select>

    <select id="getSgrpDscList" resultType="com.yingfei.entity.domain.SGRP_DSC">
        SELECT
        sd.*
        FROM SGRP_DSC sd
        WHERE
        1=1
        AND sd.F_SGRP IN (
        SELECT
        si.F_SGRP
        <include refid="baseSql"/>
        )
    </select>

    <select id="getPtrvList" resultType="com.yingfei.entity.dto.PART_REV_DTO">

    </select>
</mapper>
