<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.INSPECTION_PLAN_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.INSPECTION_PLAN_INF">
            <id property="f_PLAN" column="F_PLAN" jdbcType="BIGINT"/>
            <result property="f_MFPS" column="F_MFPS" jdbcType="BIGINT"/>
            <result property="f_MFND" column="F_MFND" jdbcType="VARCHAR"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_DATA" column="F_DATA" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_COUNT" column="F_COUNT" jdbcType="TIMESTAMP"/>
            <result property="f_IMG" column="F_IMG" jdbcType="VARCHAR"/>
            <result property="f_EMPL" column="F_EMPL" jdbcType="BIGINT"/>
            <result property="f_ROLE" column="F_ROLE" jdbcType="BIGINT"/>
            <result property="f_TYPE" column="F_TYPE" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        inspectionplaninf.F_PLAN,inspectionplaninf.F_MFPS,inspectionplaninf.F_MFND,
        inspectionplaninf.F_NAME,inspectionplaninf.F_DATA,inspectionplaninf.F_CRUE,
        inspectionplaninf.F_EDUE,inspectionplaninf.F_CRTM,inspectionplaninf.F_EDTM,
        inspectionplaninf.F_COUNT,inspectionplaninf.F_IMG,inspectionplaninf.F_EMPL,
        inspectionplaninf.F_ROLE,inspectionplaninf.F_CHILD,inspectionplaninf.F_TYPE,
        manufacturingnodeinf.F_NAME as F_MFNDName,
        manufacturingprocessinf.F_NAME as F_MFPSName
    </sql>

    <sql id="baseSql">
        from INSPECTION_PLAN_INF inspectionplaninf
        left join MANUFACTURING_NODE_INF manufacturingnodeinf on manufacturingnodeinf.F_MFND = inspectionplaninf.F_MFND
        left join MANUFACTURING_PROCESS_INF manufacturingprocessinf on manufacturingprocessinf.F_MFPS = inspectionplaninf.F_MFPS
        left join EMPL_INF emplinf on emplinf.F_EMPL = inspectionplaninf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = inspectionplaninf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and inspectionplaninf.F_DEL = 0
                </when>
                <otherwise>
                    and inspectionplaninf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_NAME != null and F_NAME != ''">
                and inspectionplaninf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="F_MFND != null and F_MFND != ''">
                and inspectionplaninf.F_MFND = #{F_MFND}
            </if>
            <if test="F_CRUE != null">
                and inspectionplaninf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_TYPE != null">
                and inspectionplaninf.F_TYPE = #{F_TYPE}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and inspectionplaninf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and inspectionplaninf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and inspectionplaninf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and inspectionplaninf.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>

    <sql id="workBoardSql">
        from INSPECTION_PLAN_INF inspectionplaninf
        left join MANUFACTURING_NODE_INF manufacturingnodeinf on manufacturingnodeinf.F_MFND = inspectionplaninf.F_MFND
        left join MANUFACTURING_PROCESS_INF manufacturingprocessinf on manufacturingprocessinf.F_MFPS =
        inspectionplaninf.F_MFPS
        left join EMPL_INF emplinf on emplinf.F_EMPL = inspectionplaninf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = inspectionplaninf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and inspectionplaninf.F_DEL = 0
                </when>
                <otherwise>
                    and inspectionplaninf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="hierarchyInfIds != null and hierarchyInfIds.size() != 0">
                and manufacturingprocessinf.F_PLNT in
                <foreach collection="hierarchyInfIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

                AND (
                <trim prefix="(" suffix=")" suffixOverrides="or">
                    <if test="F_EMPL != null and F_EMPL != ''">
                        inspectionplaninf.F_EMPL LIKE concat('%', #{F_EMPL}, '%') OR
                    </if>
                    <if test="F_ROLE != null and F_ROLE != ''">
                        inspectionplaninf.F_ROLE LIKE concat('%', #{F_ROLE}, '%') OR
                    </if>
                    (inspectionplaninf.F_EMPL IS NULL OR
                    inspectionplaninf.F_EMPL = '') AND
                    (inspectionplaninf.F_ROLE IS NULL OR
                    inspectionplaninf.F_ROLE = '')
                </trim>
                )


            <if test="F_NAME != null and F_NAME != ''">
                and inspectionplaninf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(inspectionplaninf.F_PLAN)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.INSPECTION_PLAN_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by inspectionplaninf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>

    <select id="getWorkBoardPlan" resultType="com.yingfei.entity.dto.INSPECTION_PLAN_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="workBoardSql"/>
        order by inspectionplaninf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>

    <select id="getUnfinishedList" resultType="com.yingfei.entity.domain.SGRP_INF_UNFINISHED">
        select siu.* ,ipi.F_CHILD as childData
        from SGRP_INF_UNFINISHED siu
        left join INSPECTION_PLAN_INF ipi on ipi.F_PLAN = siu.F_INSP_PLAN
        where siu.F_INSP_PLAN = #{F_PLAN}
        order by siu.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
