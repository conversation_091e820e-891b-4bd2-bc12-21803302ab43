<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.ANALYSIS_DASHBOARD_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.ANALYSIS_DASHBOARD_INF">
            <id property="f_DASH" column="F_DASH" jdbcType="BIGINT"/>
            <result property="f_MENU" column="F_MENU" jdbcType="BIGINT"/>
            <result property="f_PRST" column="F_PRST" jdbcType="BIGINT"/>
            <result property="f_ADTI" column="F_ADTI" jdbcType="BIGINT"/>
            <result property="f_TITLE" column="F_TITLE"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        adi.F_DASH,adi.F_MENU,adi.F_PRST,
        adi.F_ADTI,adi.F_CRUE,adi.F_EDUE,
        adi.F_TITLE,
        adi.F_CRTM,adi.F_EDTM,adti.F_NAME as templateName,
        mi.F_NAME as menuName,psi.F_NAME as parameterName,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>
    <sql id="baseSql">
        from ANALYSIS_DASHBOARD_INF adi
        LEFT JOIN ANALYSIS_DASHBOARD_TEMPLATE_INF adti ON adti.F_ADTI = adi.F_ADTI
        LEFT JOIN MENU_INF mi ON mi.F_MENU = adi.F_MENU
        LEFT JOIN PARAMETER_SET_INF psi ON psi.F_PRST = adi.F_PRST
        left join EMPL_INF emplinf on emplinf.F_EMPL = adi.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = adi.F_EDUE
        <where>
            1=1
            <if test="F_MENU != null ">
                and adi.F_MENU = #{F_MENU}
            </if>
            <if test="F_PRST != null">
                and adi.F_PRST = #{F_PRST}
            </if>
            <if test="F_ADTI != null">
                and adi.F_ADTI = #{F_ADTI}
            </if>
            <if test="menuName != null and menuName != ''">
                and mi.F_NAME = #{menuName}
            </if>
            <if test="templateName != null and templateName != ''">
                and adti.F_NAME = #{templateName}
            </if>
            <if test="paramsetName != null and paramsetName != ''">
                and psi.F_NAME = #{paramsetName}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(adi.F_DASH)
        <include refid="baseSql"/>
    </select>


    <select id="getList" resultType="com.yingfei.entity.dto.ANALYSIS_DASHBOARD_INF_DTO">
        select
            <include refid="Base_Column_List"/>
            <include refid="baseSql"/>
        order by adi.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
