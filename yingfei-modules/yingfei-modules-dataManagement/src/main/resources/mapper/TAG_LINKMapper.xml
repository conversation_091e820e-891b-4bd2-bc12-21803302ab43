<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.TAG_LINKMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.TAG_LINK">
            <id property="f_TAG_LINK" column="F_TAG_LINK" jdbcType="BIGINT"/>
            <result property="f_RESOURCE" column="F_RESOURCE" jdbcType="BIGINT"/>
            <result property="f_TYPE" column="F_TYPE" jdbcType="TINYINT"/>
            <result property="f_TAG" column="F_TAG" jdbcType="BIGINT"/>
            <result property="f_TGGP" column="F_TGGP" jdbcType="BIGINT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        taglink.F_TAG_LINK,taglink.F_RESOURCE,taglink.F_TYPE,
        taglink.F_TAG,taglink.F_TGGP,taglink.F_CRUE,
        taglink.F_EDUE,taglink.F_CRTM,taglink.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from TAG_LINK taglink
        left join EMPL_INF emplinf on emplinf.F_EMPL = taglink.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = taglink.F_EDUE
        <where>
            1=1
            <if test="F_TYPE != null and F_TYPE != ''">
                and taglink.F_TYPE = #{F_TYPE}
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and taglink.F_CRUE = #{F_CRUE}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and taglink.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and taglink.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and taglink.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and taglink.F_EDTM &lt;= #{edEndTime}
            </if>
            <if test="resourceIds != null and resourceIds.size() != 0">
                and taglink.F_RESOURCE in
                <foreach collection="resourceIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </sql>
    
    <select id="getTotal" resultType="java.lang.Long">
        select count(taglink.F_TAG_LINK)
        <include refid="baseSql"/>
    </select>
    
    <select id="getList" resultType="com.yingfei.entity.dto.TAG_LINK_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by taglink.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
