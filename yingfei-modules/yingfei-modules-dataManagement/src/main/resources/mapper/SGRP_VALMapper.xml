<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.SGRP_VALMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.SGRP_VAL">
            <id property="f_SGRP" column="F_SGRP" jdbcType="BIGINT"/>
            <result property="f_TEST" column="F_TEST" jdbcType="BIGINT"/>
            <result property="f_TSNO" column="F_TSNO" jdbcType="SMALLINT"/>
            <result property="f_SBNO" column="F_SBNO" jdbcType="SMALLINT"/>
            <result property="f_VAL" column="F_VAL" jdbcType="DECIMAL"/>
            <result property="f_DEF" column="F_DEF" jdbcType="BIGINT"/>
            <result property="f_IMAGE" column="F_IMAGE" jdbcType="VARCHAR"/>
            <result property="f_SN" column="F_SN" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_SGRP,F_TEST,F_TSNO,
        F_SBNO,F_VAL,F_DEF,
        F_IMAGE,F_SN
    </sql>
</mapper>
