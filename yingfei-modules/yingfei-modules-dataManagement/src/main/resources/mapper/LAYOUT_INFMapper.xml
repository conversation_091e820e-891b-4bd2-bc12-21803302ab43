<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.LAYOUT_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.LAYOUT_INF">
            <id property="f_ID" column="F_ID" jdbcType="BIGINT"/>
            <result property="f_EMPL" column="F_EMPL" jdbcType="BIGINT"/>
            <result property="f_DATA" column="F_DATA" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_MENU" column="F_MENU" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_ID,F_EMPL,F_DATA,
        F_CRUE,F_EDUE,F_CRTM,
        F_EDTM,F_MENU
    </sql>
</mapper>
