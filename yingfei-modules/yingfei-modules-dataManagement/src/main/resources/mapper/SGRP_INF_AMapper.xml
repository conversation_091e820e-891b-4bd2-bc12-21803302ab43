<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.SGRP_INF_AMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.SGRP_INF_A">
            <id property="f_SGRP" column="F_SGRP" jdbcType="BIGINT"/>
            <result property="f_MFPS" column="F_MFPS" jdbcType="BIGINT"/>
            <result property="f_MFND" column="F_MFND" jdbcType="BIGINT"/>
            <result property="f_PART" column="F_PART" jdbcType="BIGINT"/>
            <result property="f_PRCS" column="F_PRCS" jdbcType="BIGINT"/>
            <result property="f_REV" column="F_REV" jdbcType="BIGINT"/>
            <result property="f_LOT" column="F_LOT" jdbcType="BIGINT"/>
            <result property="f_JOB" column="F_JOB" jdbcType="BIGINT"/>
            <result property="f_SHIFT" column="F_SHIFT" jdbcType="BIGINT"/>
            <result property="f_SGSZ" column="F_SGSZ" jdbcType="SMALLINT"/>
            <result property="f_SGTM" column="F_SGTM" jdbcType="TIMESTAMP"/>
            <result property="f_FLAG" column="F_FLAG" jdbcType="BIT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_NUM" column="F_NUM" jdbcType="BIGINT"/>
            <result property="f_INSP_PLAN" column="F_INSP_PLAN" jdbcType="BIGINT"/>
            <result property="f_STATUS" column="F_STATUS" jdbcType="BIGINT"/>
            <result property="f_PLNT" column="F_PLNT" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_SGRP,F_MFPS,F_MFND,
        F_PART,F_PRCS,F_REV,
        F_LOT,F_JOB,F_SHIFT,
        F_SGSZ,F_SGTM,F_FLAG,
        F_DEL,F_CRUE,F_EDUE,
        F_CRTM,F_EDTM,F_PLNT
    </sql>

    <select id="getCacheMaxMinTime" resultType="com.yingfei.entity.domain.SGRP_INF_A">
        select
        <if test="dbType == 1">
            top 1
        </if>
            *
        from SGRP_INF_A
        <choose>
            <when test="b">
                order by F_CRTM desc
            </when>
            <otherwise>
                order by F_CRTM asc
            </otherwise>
        </choose>
        <choose>
            <when test="dbType == 2">
                FETCH FIRST 1 ROW ONLY;
            </when>
            <when test="dbType == 3">
                LIMIT 1
            </when>
        </choose>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.SubgroupDataDTO">
        select sia.*,sva.F_TEST,sva.F_DATA as testData
        from SGRP_INF_A sia
        left join SGRP_VAL_A sva on sva.F_SGRP = sia.F_SGRP
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and sia.F_DEL = 0
                </when>
                <otherwise>
                    and sia.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="partList != null and partList.size() != 0">
                and sia.F_PART in
                <foreach collection="partList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="prcsList != null and prcsList.size() != 0">
                and sia.F_PRCS in
                <foreach collection="prcsList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="lotList != null and lotList.size() != 0">
                and sia.F_LOT in
                <foreach collection="lotList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="testList != null and testList.size() != 0">
                and sva.F_TEST in
                <foreach collection="testList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="F_CRUE != null">
                and sia.F_CRUE = #{F_CRUE}
            </if>
            <if test="startDate != null">
                and sia.F_SGTM &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and sia.F_SGTM &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="getDataHistorical" resultType="com.yingfei.entity.dto.SubgroupDataDTO">
        select sia.*,
        ipi.F_NAME as planName,
        pai.F_NAME as partName,
        pri.F_NAME as prcsName
        from SGRP_INF_A sia
        left join INSPECTION_PLAN_INF ipi on ipi.F_PLAN = sia.F_INSP_PLAN
        left join PART_INF pai on pai.F_PART = sia.F_PART
        left join PRCS_INF pri on pri.F_PRCS = sia.F_PRCS
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and sia.F_DEL = 0
                </when>
                <otherwise>
                    and sia.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null">
                and sia.F_CRUE = #{F_CRUE}
            </if>
            <if test="startTime != null">
                and sia.F_SGTM &gt;= #{startTime}
            </if>
        </where>
        order by sia.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
