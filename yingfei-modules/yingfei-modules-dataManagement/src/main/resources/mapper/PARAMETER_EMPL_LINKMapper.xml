<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.PARAMETER_EMPL_LINKMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.PARAMETER_EMPL_LINK">
            <id property="f_ID" column="F_ID" jdbcType="BIGINT"/>
            <result property="f_TYPE" column="F_TYPE" jdbcType="BIGINT"/>
            <result property="f_DATA_ID" column="F_DATA_ID" jdbcType="VARCHAR"/>
            <result property="f_PARAMETER_ID" column="F_PARAMETER_ID" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_ID,F_TYPE,F_DATA_ID,
        F_PARAMETER_ID,
        F_CRUE,F_EDUE,F_CRTM,
        F_EDTM
    </sql>


    <select id="findByNeEmplAndRole" resultType="com.yingfei.entity.domain.PARAMETER_EMPL_LINK">
        select
        <include refid="Base_Column_List"/>
        from PARAMETER_EMPL_LINK
        <where>
            (F_DATA_ID != #{userId} or F_DATA_ID != #{roleId})
            <if test="parameterIds != null and parameterIds.size() != 0">
                and F_PARAMETER_ID not in
                <foreach collection="parameterIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>


</mapper>
