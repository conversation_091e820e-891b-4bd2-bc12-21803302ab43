<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.ACTIVED_RULE_TEMPLATE_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.ACTIVED_RULE_TEMPLATE_INF">
            <id property="f_ARTP" column="F_ARTP" jdbcType="BIGINT"/>
            <result property="f_DIV" column="F_DIV" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_CHART_ONE" column="F_CHART_ONE" jdbcType="BIGINT"/>
            <result property="f_CHART_TWO" column="F_CHART_TWO" jdbcType="BIGINT"/>
            <result property="f_CHART_THREE" column="F_CHART_THREE" jdbcType="BIGINT"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        activedruletemplateinf.F_ARTP,activedruletemplateinf.F_DIV,activedruletemplateinf.F_NAME,
        activedruletemplateinf.F_CHART_ONE,activedruletemplateinf.F_CHART_TWO,activedruletemplateinf.F_CHART_THREE,
        activedruletemplateinf.F_FACTOR,activedruletemplateinf.F_DEL,activedruletemplateinf.F_CRUE,
        activedruletemplateinf.F_EDUE,activedruletemplateinf.F_CRTM,activedruletemplateinf.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from ACTIVED_RULE_TEMPLATE_INF activedruletemplateinf
        left join EMPL_INF emplinf on emplinf.F_EMPL = activedruletemplateinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = activedruletemplateinf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and activedruletemplateinf.F_DEL = 0
                </when>
                <otherwise>
                    and activedruletemplateinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and activedruletemplateinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
        </where>
    </sql>


    <select id="getTotal" resultType="java.lang.Long">
        select count(activedruletemplateinf.F_ARTP)
        <include refid="baseSql"/>
    </select>
    
    <select id="getList" resultType="com.yingfei.entity.dto.ACTIVED_RULE_TEMPLATE_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by activedruletemplateinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
