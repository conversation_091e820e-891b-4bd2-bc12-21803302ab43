<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.BpmProcessDefinitionExtMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.BPM_PROCESS_DEFINITION">
            <id property="f_BPDE" column="F_BPDE" jdbcType="BIGINT"/>
            <result property="f_PROCESS_DEFINITION" column="F_PROCESS_DEFINITION" jdbcType="VARCHAR"/>
            <result property="f_MODE" column="F_MODE" jdbcType="VARCHAR"/>
            <result property="f_DESCRIPTION" column="F_DESCRIPTION" jdbcType="VARCHAR"/>
            <result property="f_FROM_TYPE" column="F_FROM_TYPE" jdbcType="TINYINT"/>
            <result property="f_FROM" column="F_FROM" jdbcType="BIGINT"/>
            <result property="f_FROM_CONF" column="F_FROM_CONF" jdbcType="VARCHAR"/>
            <result property="f_FROM_FIELDS" column="F_FROM_FIELDS" jdbcType="VARCHAR"/>
            <result property="f_FROM_SUBMIT_PATH" column="F_FROM_SUBMIT_PATH" jdbcType="VARCHAR"/>
            <result property="f_FROM_VIEW_PATH" column="F_FROM_VIEW_PATH" jdbcType="VARCHAR"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_BPDE,F_PROCESS_DEFINITION,F_MODE,
        F_DESCRIPTION,F_FROM_TYPE,F_FROM,
        F_FROM_CONF,F_FROM_FIELDS,F_FROM_SUBMIT_PATH,
        F_FROM_VIEW_PATH,F_DEL,F_CRUE,
        F_EDUE,F_CRTM,F_EDTM
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from BPM_PROCESS_DEFINITION
        where  F_BPDE = #{f_BPDE,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from BPM_PROCESS_DEFINITION
        where  F_BPDE = #{f_BPDE,jdbcType=BIGINT} 
    </delete>

    <insert id="insertSelective" keyColumn="F_BPDE" keyProperty="f_BPDE" parameterType="com.yingfei.entity.domain.BPM_PROCESS_DEFINITION" useGeneratedKeys="true">
        insert into BPM_PROCESS_DEFINITION
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="f_BPDE != null">F_BPDE,</if>
                <if test="f_PROCESS_DEFINITION != null">F_PROCESS_DEFINITION,</if>
                <if test="f_MODE != null">F_MODE,</if>
                <if test="f_DESCRIPTION != null">F_DESCRIPTION,</if>
                <if test="f_FROM_TYPE != null">F_FROM_TYPE,</if>
                <if test="f_FROM != null">F_FROM,</if>
                <if test="f_FROM_CONF != null">F_FROM_CONF,</if>
                <if test="f_FROM_FIELDS != null">F_FROM_FIELDS,</if>
                <if test="f_FROM_SUBMIT_PATH != null">F_FROM_SUBMIT_PATH,</if>
                <if test="f_FROM_VIEW_PATH != null">F_FROM_VIEW_PATH,</if>
                <if test="f_DEL != null">F_DEL,</if>
                <if test="f_CRUE != null">F_CRUE,</if>
                <if test="f_EDUE != null">F_EDUE,</if>
                <if test="f_CRTM != null">F_CRTM,</if>
                <if test="f_EDTM != null">F_EDTM,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="f_BPDE != null">#{f_BPDE,jdbcType=BIGINT},</if>
                <if test="f_PROCESS_DEFINITION != null">#{f_PROCESS_DEFINITION,jdbcType=VARCHAR},</if>
                <if test="f_MODE != null">#{f_MODE,jdbcType=VARCHAR},</if>
                <if test="f_DESCRIPTION != null">#{f_DESCRIPTION,jdbcType=VARCHAR},</if>
                <if test="f_FROM_TYPE != null">#{f_FROM_TYPE,jdbcType=TINYINT},</if>
                <if test="f_FROM != null">#{f_FROM,jdbcType=BIGINT},</if>
                <if test="f_FROM_CONF != null">#{f_FROM_CONF,jdbcType=VARCHAR},</if>
                <if test="f_FROM_FIELDS != null">#{f_FROM_FIELDS,jdbcType=VARCHAR},</if>
                <if test="f_FROM_SUBMIT_PATH != null">#{f_FROM_SUBMIT_PATH,jdbcType=VARCHAR},</if>
                <if test="f_FROM_VIEW_PATH != null">#{f_FROM_VIEW_PATH,jdbcType=VARCHAR},</if>
                <if test="f_DEL != null">#{f_DEL,jdbcType=BIT},</if>
                <if test="f_CRUE != null">#{f_CRUE,jdbcType=BIGINT},</if>
                <if test="f_EDUE != null">#{f_EDUE,jdbcType=BIGINT},</if>
                <if test="f_CRTM != null">#{f_CRTM,jdbcType=TIMESTAMP},</if>
                <if test="f_EDTM != null">#{f_EDTM,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.yingfei.entity.domain.BPM_PROCESS_DEFINITION">
        update BPM_PROCESS_DEFINITION
        <set>
                <if test="f_PROCESS_DEFINITION != null">
                    F_PROCESS_DEFINITION = #{f_PROCESS_DEFINITION,jdbcType=VARCHAR},
                </if>
                <if test="f_MODE != null">
                    F_MODE = #{f_MODE,jdbcType=VARCHAR},
                </if>
                <if test="f_DESCRIPTION != null">
                    F_DESCRIPTION = #{f_DESCRIPTION,jdbcType=VARCHAR},
                </if>
                <if test="f_FROM_TYPE != null">
                    F_FROM_TYPE = #{f_FROM_TYPE,jdbcType=TINYINT},
                </if>
                <if test="f_FROM != null">
                    F_FROM = #{f_FROM,jdbcType=BIGINT},
                </if>
                <if test="f_FROM_CONF != null">
                    F_FROM_CONF = #{f_FROM_CONF,jdbcType=VARCHAR},
                </if>
                <if test="f_FROM_FIELDS != null">
                    F_FROM_FIELDS = #{f_FROM_FIELDS,jdbcType=VARCHAR},
                </if>
                <if test="f_FROM_SUBMIT_PATH != null">
                    F_FROM_SUBMIT_PATH = #{f_FROM_SUBMIT_PATH,jdbcType=VARCHAR},
                </if>
                <if test="f_FROM_VIEW_PATH != null">
                    F_FROM_VIEW_PATH = #{f_FROM_VIEW_PATH,jdbcType=VARCHAR},
                </if>
                <if test="f_DEL != null">
                    F_DEL = #{f_DEL,jdbcType=BIT},
                </if>
                <if test="f_CRUE != null">
                    F_CRUE = #{f_CRUE,jdbcType=BIGINT},
                </if>
                <if test="f_EDUE != null">
                    F_EDUE = #{f_EDUE,jdbcType=BIGINT},
                </if>
                <if test="f_CRTM != null">
                    F_CRTM = #{f_CRTM,jdbcType=TIMESTAMP},
                </if>
                <if test="f_EDTM != null">
                    F_EDTM = #{f_EDTM,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   F_BPDE = #{f_BPDE,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.yingfei.entity.domain.BPM_PROCESS_DEFINITION">
        update BPM_PROCESS_DEFINITION
        set 
            F_PROCESS_DEFINITION =  #{f_PROCESS_DEFINITION,jdbcType=VARCHAR},
            F_MODE =  #{f_MODE,jdbcType=VARCHAR},
            F_DESCRIPTION =  #{f_DESCRIPTION,jdbcType=VARCHAR},
            F_FROM_TYPE =  #{f_FROM_TYPE,jdbcType=TINYINT},
            F_FROM =  #{f_FROM,jdbcType=BIGINT},
            F_FROM_CONF =  #{f_FROM_CONF,jdbcType=VARCHAR},
            F_FROM_FIELDS =  #{f_FROM_FIELDS,jdbcType=VARCHAR},
            F_FROM_SUBMIT_PATH =  #{f_FROM_SUBMIT_PATH,jdbcType=VARCHAR},
            F_FROM_VIEW_PATH =  #{f_FROM_VIEW_PATH,jdbcType=VARCHAR},
            F_DEL =  #{f_DEL,jdbcType=BIT},
            F_CRUE =  #{f_CRUE,jdbcType=BIGINT},
            F_EDUE =  #{f_EDUE,jdbcType=BIGINT},
            F_CRTM =  #{f_CRTM,jdbcType=TIMESTAMP},
            F_EDTM =  #{f_EDTM,jdbcType=TIMESTAMP}
        where   F_BPDE = #{f_BPDE,jdbcType=BIGINT} 
    </update>
</mapper>
