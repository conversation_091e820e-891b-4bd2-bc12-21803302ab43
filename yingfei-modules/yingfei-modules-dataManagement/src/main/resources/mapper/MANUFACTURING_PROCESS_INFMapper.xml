<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.MANUFACTURING_PROCESS_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.MANUFACTURING_PROCESS_INF">
            <id property="f_MFPS" column="F_MFPS" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_DATA" column="F_DATA" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_PLNT" column="F_PLNT" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        manufacturingprocessinf.F_MFPS,manufacturingprocessinf.F_NAME,manufacturingprocessinf.F_DATA,
        manufacturingprocessinf.F_CRUE,manufacturingprocessinf.F_EDUE,manufacturingprocessinf.F_CRTM,
        manufacturingprocessinf.F_EDTM,manufacturingprocessinf.F_PLNT,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="Base_Column_List_Two">
        manufacturingprocessinf.F_MFPS,manufacturingprocessinf.F_NAME,
        manufacturingprocessinf.F_CRUE,manufacturingprocessinf.F_EDUE,manufacturingprocessinf.F_CRTM,
        manufacturingprocessinf.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from MANUFACTURING_PROCESS_INF manufacturingprocessinf
        left join EMPL_INF emplinf on emplinf.F_EMPL = manufacturingprocessinf.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = manufacturingprocessinf.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and manufacturingprocessinf.F_DEL = 0
                </when>
                <otherwise>
                    and manufacturingprocessinf.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and manufacturingprocessinf.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and manufacturingprocessinf.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and manufacturingprocessinf.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and manufacturingprocessinf.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and manufacturingprocessinf.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and manufacturingprocessinf.F_EDTM &lt;= #{edEndTime}
            </if>
            <if test="F_PLNT != null and F_PLNT != ''">
                and manufacturingprocessinf.F_PLNT = #{F_PLNT}
            </if>
            <if test="hierarchyInfIds != null and hierarchyInfIds.size() != 0">
                and manufacturingprocessinf.F_PLNT in
                <foreach collection="hierarchyInfIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(manufacturingprocessinf.F_MFPS)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.MANUFACTURING_PROCESS_INF_DTO">
        select
        <include refid="Base_Column_List_Two"/>
        <include refid="baseSql"/>
        order by manufacturingprocessinf.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
