<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.ROOT_CAUSE_GRPMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.ROOT_CAUSE_GRP">
            <id property="f_RCGP" column="F_RCGP" jdbcType="BIGINT"/>
            <result property="f_DIV" column="F_DIV" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_FACTOR" column="F_FACTOR" jdbcType="FLOAT"/>
            <result property="f_DEL" column="F_DEL" jdbcType="BIT"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        rootcausegrp.F_RCGP,rootcausegrp.F_DIV,rootcausegrp.F_NAME,
        rootcausegrp.F_FACTOR,rootcausegrp.F_DEL,rootcausegrp.F_CRUE,
        rootcausegrp.F_EDUE,rootcausegrp.F_CRTM,rootcausegrp.F_EDTM,
        emplinf.F_NAME as createName,
        emplinf_u.F_NAME as updateName
    </sql>

    <sql id="baseSql">
        from ROOT_CAUSE_GRP rootcausegrp
        left join EMPL_INF emplinf on emplinf.F_EMPL = rootcausegrp.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = rootcausegrp.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and rootcausegrp.F_DEL = 0
                </when>
                <otherwise>
                    and rootcausegrp.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_CRUE != null and F_CRUE != ''">
                and rootcausegrp.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and rootcausegrp.F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
        </where>
    </sql>
    
    <select id="getTotal" resultType="java.lang.Long">
        select count(rootcausegrp.F_RCGP)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.ROOT_CAUSE_GRP_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by rootcausegrp.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
