<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.IQC_GB2828_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.IQC_GB2828_INF">
            <id property="f_ID" column="F_ID" jdbcType="BIGINT"/>
            <result property="f_LEVEL" column="F_LEVEL" jdbcType="VARCHAR"/>
            <result property="f_STATE" column="F_STATE" jdbcType="VARCHAR"/>
            <result property="f_AQL" column="F_AQL" jdbcType="FLOAT"/>
            <result property="f_LOT_MAX" column="F_LOT_MAX" jdbcType="INTEGER"/>
            <result property="f_LOT_MIN" column="F_LOT_MIN" jdbcType="INTEGER"/>
            <result property="f_SAMPLE_NO" column="F_SAMPLE_NO" jdbcType="INTEGER"/>
            <result property="f_SAMPLE_SIZE" column="F_SAMPLE_SIZE" jdbcType="INTEGER"/>
            <result property="f_ACCEPT" column="F_ACCEPT" jdbcType="INTEGER"/>
            <result property="f_REJECT" column="F_REJECT" jdbcType="INTEGER"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ig.F_ID,ig.F_LEVEL,ig.F_STATE,
        ig.F_AQL,ig.F_LOT_MAX,ig.F_LOT_MIN,
        ig.F_SAMPLE_NO,ig.F_SAMPLE_SIZE,ig.F_ACCEPT,
        ig.F_REJECT,ig.F_CRUE,ig.F_EDUE,
        ig.F_CRTM,F_EDTM,ig.F_END
    </sql>

    <sql id="baseSql">
        from IQC_GB2828_INF ig
        left join EMPL_INF emplinf on emplinf.F_EMPL = ig.F_CRUE
        LEFT JOIN EMPL_INF emplinf_u ON emplinf_u.F_EMPL = ig.F_EDUE
        <where>
            1=1
            <if test="F_LEVEL != null and F_LEVEL != ''">
                and ig.F_LEVEL = #{F_LEVEL}
            </if>
            <if test="F_CRUE != null and F_CRUE != ''">
                and ig.F_CRUE = #{F_CRUE}
            </if>
            <if test="F_STATE != null and F_STATE != ''">
                and ig.F_STATE = #{F_STATE}
            </if>
            <if test="F_AQL != null">
                and ig.F_AQL = #{F_AQL}
            </if>
            <if test="createName != null and createName != ''">
                and emplinf.F_NAME like concat('%', #{createName}, '%')
            </if>
            <if test="updateName != null and updateName != ''">
                and emplinf_u.F_NAME like concat('%', #{updateName}, '%')
            </if>
            <if test="startTime != null">
                and ig.F_CRTM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and ig.F_CRTM &lt;= #{endTime}
            </if>
            <if test="edStartTime != null">
                and ig.F_EDTM &gt;= #{edStartTime}
            </if>
            <if test="edEndTime != null">
                and ig.F_EDTM &lt;= #{edEndTime}
            </if>
        </where>
    </sql>
    
    <select id="getList" resultType="com.yingfei.entity.domain.IQC_GB2828_INF">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by ig.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
    
    <select id="getTotal" resultType="java.lang.Long">
        select count(ig.F_ID)
        <include refid="baseSql"/>
    </select>
</mapper>
