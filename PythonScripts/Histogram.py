import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from scipy.stats import norm
import sys
import os
import json


# plt.rcParams['font.family']='Arial' # 设置字体和figure大小
# plt.rcParams['font.size']=15
# plt.figure(figsize=(10,7))
#
# data=(2.75,2.75,2.75,2.7516,2.7533,2.7511,2.7516,2.754,2.7521,2.7529,2.7529,2.7511,2.7522,2.7526,2.7528,2.7524,2.7513,2.7526,2.7526,2.7516,2.7512,2.7523,2.7508,2.752,2.7519,2.7525,2.7521,2.756,2.75,2.75,2.7505,2.7505,2.753,2.7525,2.7525,2.7525,2.7527,2.7525,2.7525,2.7519,2.753,2.752,2.7523,2.7537,2.7517,2.7524,2.7512,2.752,2.7515,2.7528,2.7525,2.753,2.7511,2.7527,2.7516,2.7514,2.7516,2.7531,2.7521,2.7541,2.7524,2.7537,2.7525,2.755,2.7527,2.7524)

def minitab_style_bins(data):
    n = len(data)
    # Sturges' Rule（小样本）
    k_sturges = int(np.ceil(np.log2(n)) + 1)
    # Freedman-Diaconis（抗离群值）
    q1, q3 = np.percentile(data, [25, 75])
    iqr = q3 - q1
    bin_width_fd = 2 * iqr / (n ** (1 / 3))
    k_fd = int(np.ceil((np.max(data) - np.min(data)) / bin_width_fd))
    # 选择较小的分组数（避免过度分组）
    k = min(k_sturges, k_fd)
    # 取整分组边界（类似Minitab）
    bin_width = (np.max(data) - np.min(data)) / k
    rounded_min = np.floor(np.min(data) / bin_width) * bin_width
    rounded_max = np.ceil(np.max(data) / bin_width) * bin_width
    return np.linspace(rounded_min, rounded_max, k + 1)


if __name__ == '__main__':
    # 获取传递的参数
    param1 = sys.argv[1]
    with open(param1, 'r', encoding='utf-8') as file:
        content = file.read()
    # 删除文件
    os.remove(param1)
    data = json.loads(content)

    bins_custom = minitab_style_bins(data)
    # print("这是分组详情=", bins_custom)
    print("bins_custom=", bins_custom, "&&")
    fig, ax1 = plt.subplots()
    # 绘制直方图
    counts, bins, patches = ax1.hist(
        data, bins=bins_custom, color='lightblue', edgecolor='black', alpha=0.7
    )
    # print("这是频数=", counts)
    print("counts=", counts, "&&")
    ax1.set_xlabel("Value")
    ax1.set_ylabel("Frequency  (Count)", color='blue')
    # 创建第二个坐标轴（概率密度）
    ax2 = ax1.twinx()
    mu, std = norm.fit(data)
    x = np.linspace(bins_custom[0], bins_custom[-1], 100)
    # print("以下是概率密度曲线取的点=", x)
    print("point=", x, "&&")
    pdf = norm.pdf(x, mu, std)
    # print("以下是概率密度曲线的取值=", pdf)
    print("value=", pdf, "&&")
    # print(pdf)
    # ax2.plot(x,  pdf, 'r-', label='PDF')
    # ax2.set_ylabel("Probability  Density", color='red')
    #
    # plt.title("Histogram  with Dual Y-Axis (Count + PDF)")
    # plt.show()
